#!/bin/bash
# This script is used to build the Node.js application

# Exit on error
set -e

# Print commands
set -x

# Print Node.js version
node --version
npm --version

# Install dependencies
npm install

# Install backend dependencies
cd backend
npm install

# Return to root directory
cd ..

# Install and build frontend
cd frontend/youtube-transcribe
npm install
npm run build

# Return to root directory
cd ../..

echo "Build completed successfully!"
