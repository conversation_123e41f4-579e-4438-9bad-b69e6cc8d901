#!/usr/bin/env bash
# exit on error
set -o errexit

# Print commands for debugging
set -x

# Explicitly state this is a Node.js project
echo "This is a Node.js project, not a Rust project"

# Install dependencies for the root project
npm install

# Install backend dependencies
cd backend
npm install

# Return to the root directory
cd ..

# Install frontend dependencies and build
cd frontend/youtube-transcribe
npm install
npm run build

# Return to the root directory
cd ../..

echo "Build completed successfully!"
