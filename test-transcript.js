const axios = require('axios');

async function testTranscript() {
  try {
    const videoId = 'TT81fe2IobI'; // One of the example videos
    const response = await axios.get(`http://localhost:3000/api/transcript/${videoId}`);
    
    console.log('Response status:', response.status);
    console.log('Response data type:', typeof response.data);
    console.log('Has transcript property:', !!response.data.transcript);
    
    if (response.data.transcript) {
      console.log('Transcript length:', response.data.transcript.length);
      console.log('First transcript item:', response.data.transcript[0]);
    }
  } catch (error) {
    console.error('Error fetching transcript:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

testTranscript();
