/**
 * Test script to verify transcript fetching with different languages
 */
const axios = require('axios');

// Test videos to use
const testVideos = [
  'arj7oStGLkU', // Inside the mind of a master procrastinator (English)
  'jNQXAC9IVRw', // Me at the zoo (English)
  'Ks-_Mh1QhMc', // Your body language may shape who you are (English)
  'gRyPjRrjS34', // Wir müssen reden (German)
  'BZbChKzedEk'  // French video
];

// Languages to test
const languages = ['en', 'de', 'fr', 'es', 'it'];

async function testTranscriptFetching() {
  console.log('Starting transcript fetching tests...');

  for (const videoId of testVideos) {
    console.log(`\nTesting video: ${videoId}`);

    // First, get available languages
    try {
      const langResponse = await axios.get(`http://localhost:3000/api/captions/${videoId}`);
      console.log(`Available languages for ${videoId}:`,
        langResponse.data.map(lang => `${lang.snippet.language} (${lang.snippet.trackKind})`));

      // Test fetching transcript in each available language
      const availableLanguages = langResponse.data.map(lang => lang.snippet.language);

      for (const lang of languages) {
        if (availableLanguages.includes(lang) || lang === 'en') {
          console.log(`\nFetching ${videoId} in language: ${lang}`);
          try {
            const response = await axios.get(`http://localhost:3000/api/transcript/${videoId}?lang=${lang}`);
            console.log(`Success! Received ${response.data.transcript.length} transcript items`);
            console.log(`Language: ${response.data.language}, Auto-generated: ${response.data.isAutoGenerated}`);

            // Print first item as sample
            if (response.data.transcript.length > 0) {
              console.log('Sample item:', response.data.transcript[0]);
            }
          } catch (error) {
            console.error(`Error fetching ${videoId} in ${lang}:`,
              error.response ? error.response.status : error.message);
          }
        }
      }
    } catch (error) {
      console.error(`Error getting languages for ${videoId}:`,
        error.response ? error.response.status : error.message);
    }
  }

  console.log('\nTests completed!');
}

// Run the tests
testTranscriptFetching().catch(err => console.error('Test failed:', err));
