/**
 * Test script for the specific German video
 */

const transcriptService = require('./services/transcriptService');

// Test video ID
const VIDEO_ID = 'h9R6ZeNruuI'; // German video
const LANGUAGE = 'de'; // German

// Function to format time in MM:SS format
function formatTime(seconds) {
  if (typeof seconds !== 'number' || isNaN(seconds)) {
    seconds = 0;
  }
  
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  
  if (hours > 0) {
    return [
      hours.toString().padStart(2, '0'),
      minutes.toString().padStart(2, '0'),
      secs.toString().padStart(2, '0')
    ].join(':');
  } else {
    return [
      minutes.toString().padStart(2, '0'),
      secs.toString().padStart(2, '0')
    ].join(':');
  }
}

// Function to analyze transcript for issues
function analyzeTranscript(transcriptItems) {
  console.log('\nANALYZING TRANSCRIPT FOR ISSUES:');
  
  // Check for timestamp tags in text
  const timestampTagsCount = transcriptItems.filter(item => 
    item.text.includes('<') && item.text.includes('>') && 
    /\d+:\d+/.test(item.text)
  ).length;
  
  console.log(`\n1. Timestamp tags in text: ${timestampTagsCount > 0 ? 'YES' : 'NO'}`);
  if (timestampTagsCount > 0) {
    console.log('   Examples:');
    transcriptItems.filter(item => 
      item.text.includes('<') && item.text.includes('>') && 
      /\d+:\d+/.test(item.text)
    ).slice(0, 3).forEach(item => {
      console.log(`   - "${item.text}"`);
    });
  }
  
  // Check for duplicate text segments
  const textMap = new Map();
  const duplicateTexts = [];
  
  transcriptItems.forEach(item => {
    if (textMap.has(item.text)) {
      duplicateTexts.push({
        text: item.text,
        occurrences: textMap.get(item.text) + 1
      });
      textMap.set(item.text, textMap.get(item.text) + 1);
    } else {
      textMap.set(item.text, 1);
    }
  });
  
  const duplicateCount = duplicateTexts.length;
  console.log(`\n2. Duplicate text segments: ${duplicateCount > 0 ? 'YES' : 'NO'}`);
  if (duplicateCount > 0) {
    console.log('   Examples:');
    duplicateTexts.slice(0, 3).forEach(dup => {
      console.log(`   - "${dup.text}" (${dup.occurrences} occurrences)`);
    });
  }
  
  // Check for punctuation issues
  const punctuationIssuesCount = transcriptItems.filter(item => 
    item.text.match(/([.!?,;:])([^\s])/) || // No space after punctuation
    item.text.match(/^\s*[.!?,;:]/) // Starts with punctuation
  ).length;
  
  console.log(`\n3. Punctuation issues: ${punctuationIssuesCount > 0 ? 'YES' : 'NO'}`);
  if (punctuationIssuesCount > 0) {
    console.log('   Examples:');
    transcriptItems.filter(item => 
      item.text.match(/([.!?,;:])([^\s])/) || 
      item.text.match(/^\s*[.!?,;:]/)
    ).slice(0, 3).forEach(item => {
      console.log(`   - "${item.text}"`);
    });
  }
  
  // Check for very short segments (less than 0.5 seconds)
  const shortSegments = transcriptItems.filter(item => 
    (item.end - item.start) < 0.5
  ).length;
  
  console.log(`\n4. Very short segments (<0.5s): ${shortSegments > 0 ? 'YES' : 'NO'}`);
  if (shortSegments > 0) {
    console.log('   Examples:');
    transcriptItems.filter(item => 
      (item.end - item.start) < 0.5
    ).slice(0, 3).forEach(item => {
      console.log(`   - "${item.text}" (${(item.end - item.start).toFixed(2)}s)`);
    });
  }
  
  // Check for overlapping segments
  let overlappingSegments = 0;
  for (let i = 0; i < transcriptItems.length - 1; i++) {
    if (transcriptItems[i].end > transcriptItems[i + 1].start) {
      overlappingSegments++;
    }
  }
  
  console.log(`\n5. Overlapping segments: ${overlappingSegments > 0 ? 'YES' : 'NO'}`);
  if (overlappingSegments > 0) {
    console.log('   Examples:');
    for (let i = 0, count = 0; i < transcriptItems.length - 1 && count < 3; i++) {
      if (transcriptItems[i].end > transcriptItems[i + 1].start) {
        console.log(`   - "${transcriptItems[i].text}" (${formatTime(transcriptItems[i].start)}-${formatTime(transcriptItems[i].end)}) overlaps with`);
        console.log(`     "${transcriptItems[i+1].text}" (${formatTime(transcriptItems[i+1].start)}-${formatTime(transcriptItems[i+1].end)})`);
        count++;
      }
    }
  }
  
  // Check for very long segments (more than 30 seconds)
  const longSegments = transcriptItems.filter(item => 
    (item.end - item.start) > 30
  ).length;
  
  console.log(`\n6. Very long segments (>30s): ${longSegments > 0 ? 'YES' : 'NO'}`);
  if (longSegments > 0) {
    console.log('   Examples:');
    transcriptItems.filter(item => 
      (item.end - item.start) > 30
    ).slice(0, 3).forEach(item => {
      console.log(`   - "${item.text.substring(0, 100)}..." (${(item.end - item.start).toFixed(2)}s)`);
    });
  }
  
  // Check for repeated phrases within segments
  const repeatedPhrases = transcriptItems.filter(item => {
    const words = item.text.split(' ');
    for (let i = 0; i < words.length - 6; i++) {
      const phrase = words.slice(i, i + 3).join(' ');
      for (let j = i + 3; j < words.length - 3; j++) {
        const comparePhrase = words.slice(j, j + 3).join(' ');
        if (phrase === comparePhrase && phrase.length > 10) {
          return true;
        }
      }
    }
    return false;
  }).length;
  
  console.log(`\n7. Repeated phrases within segments: ${repeatedPhrases > 0 ? 'YES' : 'NO'}`);
  if (repeatedPhrases > 0) {
    console.log('   Examples:');
    transcriptItems.filter(item => {
      const words = item.text.split(' ');
      for (let i = 0; i < words.length - 6; i++) {
        const phrase = words.slice(i, i + 3).join(' ');
        for (let j = i + 3; j < words.length - 3; j++) {
          const comparePhrase = words.slice(j, j + 3).join(' ');
          if (phrase === comparePhrase && phrase.length > 10) {
            return true;
          }
        }
      }
      return false;
    }).slice(0, 3).forEach(item => {
      console.log(`   - "${item.text.substring(0, 100)}..."`);
    });
  }
}

// Main test function
async function testSpecificGermanVideo() {
  try {
    console.log(`Testing German transcript processing for video ID: ${VIDEO_ID}\n`);
    
    // Fetch the transcript using our service
    console.log('Fetching transcript with transcriptService...');
    const transcript = await transcriptService.getTranscript(VIDEO_ID, LANGUAGE, true);
    
    console.log(`Retrieved ${transcript.transcript.length} transcript segments`);
    
    // Analyze the transcript
    analyzeTranscript(transcript.transcript);
    
    // Display sample segments
    console.log('\nSAMPLE TRANSCRIPT SEGMENTS:');
    for (let i = 0; i < Math.min(20, transcript.transcript.length); i++) {
      console.log(`\n[${i+1}] [${transcript.transcript[i].formattedStart}] "${transcript.transcript[i].text}"`);
    }
    
    console.log('\nTest completed successfully!');
    
  } catch (error) {
    console.error('Error in test:', error);
  }
}

// Run the test
testSpecificGermanVideo().catch(console.error);
