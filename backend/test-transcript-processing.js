/**
 * Test script for transcript processing
 * 
 * This script tests the transcript processing for both English and non-English transcripts.
 */

const transcriptService = require('./services/transcriptService');

// Test video IDs
const ENGLISH_VIDEO_ID = 'Gv2fzC96Z40';
const GERMAN_VIDEO_ID = 'Gv2fzC96Z40'; // Using the same video but with German subtitles

async function testTranscriptProcessing() {
  try {
    console.log('Testing transcript processing...\n');
    
    // Test English transcript
    console.log('1. TESTING ENGLISH TRANSCRIPT:');
    console.log('-----------------------------');
    const englishTranscript = await transcriptService.getTranscript(ENGLISH_VIDEO_ID, 'en', true);
    
    console.log(`Retrieved ${englishTranscript.transcript.length} English transcript segments`);
    console.log('\nSample of English transcript:');
    englishTranscript.transcript.slice(0, 10).forEach(item => {
      console.log(`[${item.id}] [${item.formattedStart}] ${item.text}`);
    });
    
    // Test German transcript
    console.log('\n\n2. TESTING GERMAN TRANSCRIPT:');
    console.log('-----------------------------');
    const germanTranscript = await transcriptService.getTranscript(GERMAN_VIDEO_ID, 'de', true);
    
    console.log(`Retrieved ${germanTranscript.transcript.length} German transcript segments`);
    console.log('\nSample of German transcript:');
    germanTranscript.transcript.slice(0, 10).forEach(item => {
      console.log(`[${item.id}] [${item.formattedStart}] ${item.text}`);
    });
    
    // Analyze the German transcript for issues
    console.log('\nAnalyzing German transcript for issues:');
    
    // Check for timestamp tags in text
    const timestampTagsCount = germanTranscript.transcript.filter(item => 
      item.text.includes('<') && item.text.includes('>') && 
      /\d+:\d+/.test(item.text)
    ).length;
    
    console.log(`1. Timestamp tags in text: ${timestampTagsCount > 0 ? 'YES' : 'NO'}`);
    
    // Check for duplicate text segments
    const textMap = new Map();
    const duplicateTexts = [];
    
    germanTranscript.transcript.forEach(item => {
      if (textMap.has(item.text)) {
        duplicateTexts.push({
          text: item.text,
          occurrences: textMap.get(item.text) + 1
        });
        textMap.set(item.text, textMap.get(item.text) + 1);
      } else {
        textMap.set(item.text, 1);
      }
    });
    
    const duplicateCount = duplicateTexts.length;
    console.log(`2. Duplicate text segments: ${duplicateCount > 0 ? 'YES' : 'NO'}`);
    
    // Check for HTML entities
    const htmlEntitiesCount = germanTranscript.transcript.filter(item => 
      item.text.includes('&amp;') || 
      item.text.includes('&lt;') || 
      item.text.includes('&gt;') ||
      item.text.includes('&#39;')
    ).length;
    
    console.log(`3. Unprocessed HTML entities: ${htmlEntitiesCount > 0 ? 'YES' : 'NO'}`);
    
    // Check for punctuation issues
    const punctuationIssuesCount = germanTranscript.transcript.filter(item => 
      item.text.match(/([.!?,;:])([^\s])/) || // No space after punctuation
      item.text.match(/^\s*[.!?,;:]/) // Starts with punctuation
    ).length;
    
    console.log(`4. Punctuation issues: ${punctuationIssuesCount > 0 ? 'YES' : 'NO'}`);
    
    // Check for overlapping segments
    let overlappingSegments = 0;
    for (let i = 0; i < germanTranscript.transcript.length - 1; i++) {
      if (germanTranscript.transcript[i].end > germanTranscript.transcript[i + 1].start) {
        overlappingSegments++;
      }
    }
    
    console.log(`5. Overlapping segments: ${overlappingSegments > 0 ? 'YES' : 'NO'}`);
    
    // Check for very short segments (less than 0.1 seconds)
    const shortSegments = germanTranscript.transcript.filter(item => 
      (item.end - item.start) < 0.1
    ).length;
    
    console.log(`6. Very short segments (<0.1s): ${shortSegments > 0 ? 'YES' : 'NO'}`);
    
    console.log('\nTest completed successfully!');
    
  } catch (error) {
    console.error('Error in test:', error);
  }
}

// Run the test
testTranscriptProcessing().catch(console.error);
