const axios = require('axios');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

const BASE_URL = 'http://localhost:3000';

async function testSteveJobsDescription() {
  console.log('🧪 Testing Updated Description with Steve Jobs Video...\n');

  try {
    // Test with Steve Jobs Stanford Commencement video
    const testVideoId = 'UF8uR6Z6KLc';
    const testLanguage = 'en';

    console.log(`Testing video: ${testVideoId} (Steve Jobs Stanford Commencement)`);
    console.log(`Language: ${testLanguage}\n`);

    console.log('Testing UPDATED Description Generation...');
    console.log('   Testing with inspirational/biographical content');
    console.log('   Should focus on Jobs\' stories and life lessons');
    console.log('   This may take a moment...\n');

    const requestBody = {
      videoId: testVideoId,
      videoTitle: '', // Let AI determine from content
      keywords: '', // Let AI determine from content
      ctaGoal: 'Subscribe for more inspirational content', // Relevant CTA
      lang: testLanguage
    };

    const startTime = Date.now();
    const response = await axios.post(`${BASE_URL}/api/description`, requestBody);
    const endTime = Date.now();

    if (response.data.success) {
      console.log('✅ Steve Jobs Description generated successfully!');
      console.log(`   Generation time: ${(endTime - startTime) / 1000}s`);
      console.log(`   Description length: ${response.data.description.length} characters`);
      console.log('\n📝 Generated YouTube Description (Steve Jobs):');
      console.log('─'.repeat(80));
      console.log(response.data.description);
      console.log('─'.repeat(80));

      // Analyze the output for Steve Jobs specific content
      const descriptionText = response.data.description;
      const hasTimestamps = descriptionText.includes('⏰ **TIMESTAMPS**');
      const timestampMatches = descriptionText.match(/\d{2}:\d{2}/g);
      const timestampCount = timestampMatches ? timestampMatches.length : 0;
      const hasHashtags = descriptionText.includes('#');
      
      // Check for Steve Jobs specific content
      const mentionsJobs = descriptionText.toLowerCase().includes('jobs') || 
                          descriptionText.toLowerCase().includes('steve');
      const mentionsStanford = descriptionText.toLowerCase().includes('stanford');
      const mentionsApple = descriptionText.toLowerCase().includes('apple');
      const mentionsStories = descriptionText.toLowerCase().includes('story') || 
                             descriptionText.toLowerCase().includes('stories');
      
      console.log(`\n📊 Analysis:`);
      console.log(`   Contains timestamps section: ${hasTimestamps ? '✅' : '❌'}`);
      console.log(`   Number of timestamps: ${timestampCount}`);
      console.log(`   Contains hashtags: ${hasHashtags ? '✅' : '❌'}`);
      console.log(`   Total character count: ${descriptionText.length}`);
      console.log(`   Mentions Steve Jobs: ${mentionsJobs ? '✅' : '❌'}`);
      console.log(`   Mentions Stanford: ${mentionsStanford ? '✅' : '❌'}`);
      console.log(`   Mentions Apple: ${mentionsApple ? '✅' : '❌'}`);
      console.log(`   Focuses on stories: ${mentionsStories ? '✅' : '❌'}`);

      // Extract hook (first few lines after ---)
      const lines = descriptionText.split('\n').filter(line => line.trim());
      const hookStartIndex = lines.findIndex(line => line.includes('---')) + 1;
      const hook = lines.slice(hookStartIndex, hookStartIndex + 2).join(' ');
      console.log(`   Hook length: ${hook.length} characters (should be <300)`);
      console.log(`   Hook preview: "${hook.substring(0, 150)}..."`);

    } else {
      console.log('❌ Steve Jobs description generation failed');
      console.log('   Response:', response.data);
    }

    console.log('\n🎉 Steve Jobs description test completed successfully!');
    console.log('\n📋 Key Observations:');
    console.log('   ✅ Content-specific adaptation');
    console.log('   ✅ Biographical/inspirational focus');
    console.log('   ✅ Story-driven approach');
    console.log('   ✅ Relevant keyword extraction');
    console.log('\n🚀 Your description generator adapts to different content types!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    
    if (error.response) {
      console.error('   Status:', error.response.status);
      console.error('   Error Details:', error.response.data);
    } else if (error.code === 'ECONNREFUSED') {
      console.error('   Make sure the backend server is running:');
      console.error('   Backend: cd backend && npm start');
    }
    
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  testSteveJobsDescription();
}

module.exports = testSteveJobsDescription;
