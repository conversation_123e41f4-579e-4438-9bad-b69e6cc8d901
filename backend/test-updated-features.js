const axios = require('axios');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

const BASE_URL = 'http://localhost:3000';

async function testUpdatedFeatures() {
  console.log('🧪 Testing Updated Summary & New Quotes Features...\n');

  try {
    // Test with an example video (Dunning-Kruger Effect)
    const testVideoId = 'TT81fe2IobI';
    const testLanguage = 'en';

    console.log(`Testing video: ${testVideoId} (Dunning-K<PERSON><PERSON> Effect)`);
    console.log(`Language: ${testLanguage}\n`);

    // Test 1: Updated Summary with new prompt
    console.log('1. Testing UPDATED Summary Generation...');
    console.log('   Using new structured prompt with Key Takeaways & Summary sections');
    console.log('   This may take a moment...\n');

    const startTime1 = Date.now();
    const summaryResponse = await axios.get(`${BASE_URL}/api/summary/${testVideoId}?lang=${testLanguage}`);
    const endTime1 = Date.now();

    if (summaryResponse.data.success) {
      console.log('✅ Updated Summary generated successfully!');
      console.log(`   Generation time: ${(endTime1 - startTime1) / 1000}s`);
      console.log(`   Summary length: ${summaryResponse.data.summary.length} characters`);
      console.log('\n📝 Generated Summary (with new structure):');
      console.log('─'.repeat(80));
      console.log(summaryResponse.data.summary);
      console.log('─'.repeat(80));
    } else {
      console.log('❌ Summary generation failed');
      console.log('   Response:', summaryResponse.data);
    }

    console.log('\n' + '='.repeat(80) + '\n');

    // Test 2: New Quotes Feature
    console.log('2. Testing NEW Quotes Generation...');
    console.log('   Extracting 7 categories of significant quotes');
    console.log('   This may take a moment...\n');

    const startTime2 = Date.now();
    const quotesResponse = await axios.get(`${BASE_URL}/api/quotes/${testVideoId}?lang=${testLanguage}`);
    const endTime2 = Date.now();

    if (quotesResponse.data.success) {
      console.log('✅ Quotes generated successfully!');
      console.log(`   Generation time: ${(endTime2 - startTime2) / 1000}s`);
      console.log(`   Quotes length: ${quotesResponse.data.quotes.length} characters`);
      console.log('\n💬 Generated Quotes (7 categories):');
      console.log('─'.repeat(80));
      console.log(quotesResponse.data.quotes);
      console.log('─'.repeat(80));
    } else {
      console.log('❌ Quotes generation failed');
      console.log('   Response:', quotesResponse.data);
    }

    console.log('\n🎉 All updated features tested successfully!');
    console.log('\n📋 Summary of New Features:');
    console.log('   ✅ Updated Summary with structured format (Key Takeaways + Detailed Summary)');
    console.log('   ✅ New Quotes extraction with 7 categories');
    console.log('   ✅ Both features use Gemini 2.5 Flash Lite');
    console.log('   ✅ Automatic generation when videos are selected');
    console.log('\n🚀 Your YouTube Transcript Generator now has enhanced AI features!');
    console.log('\nNext steps:');
    console.log('   1. Open http://localhost:5173/ in your browser');
    console.log('   2. Click on an example video');
    console.log('   3. Check the Summary tab for the new structured format');
    console.log('   4. Check the Quotes tab for extracted key quotes');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    
    if (error.response) {
      console.error('   Status:', error.response.status);
      console.error('   Error Details:', error.response.data);
    } else if (error.code === 'ECONNREFUSED') {
      console.error('   Make sure both servers are running:');
      console.error('   Backend: cd backend && npm start');
      console.error('   Frontend: cd frontend/youtube-transcribe && npm run dev');
    }
    
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  testUpdatedFeatures();
}

module.exports = testUpdatedFeatures;
