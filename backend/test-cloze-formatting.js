const axios = require('axios');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

const BASE_URL = 'http://localhost:3000';

async function testClozeFormatting() {
  console.log('🧪 Testing Cloze Card Formatting...\n');

  try {
    // Test with an example video
    const testVideoId = 'TT81fe2IobI';
    const testLanguage = 'en';

    console.log(`Testing video: ${testVideoId}`);
    console.log(`Language: ${testLanguage}\n`);

    console.log('Generating Anki flash cards with focus on cloze formatting...');

    const requestBody = {
      videoId: testVideoId,
      lang: testLanguage
    };

    const startTime = Date.now();
    const response = await axios.post(`${BASE_URL}/api/anki-cards`, requestBody);
    const endTime = Date.now();

    if (response.data.success) {
      console.log('✅ Cards generated successfully!');
      console.log(`   Generation time: ${(endTime - startTime) / 1000}s`);
      console.log(`   Number of cards: ${response.data.cards.length}`);

      // Filter and analyze cloze cards
      const clozeCards = response.data.cards.filter(card => card.type === 'cloze');
      console.log(`   Cloze cards: ${clozeCards.length}`);

      if (clozeCards.length > 0) {
        console.log('\n📝 Cloze Card Analysis:');
        console.log('─'.repeat(80));
        
        clozeCards.forEach((card, index) => {
          console.log(`\n${index + 1}. Cloze Card [${card.difficulty}]`);
          console.log(`   Question: ${card.question}`);
          console.log(`   Answer: ${card.answer}`);
          console.log(`   Timestamp: ${Math.floor(card.timestamp / 60)}:${(card.timestamp % 60).toString().padStart(2, '0')}`);
          console.log(`   Tags: ${card.tags.join(', ')}`);
          
          // Validate cloze formatting
          const hasClozeMarkers = /\{\{c\d+::[^}]+\}\}/.test(card.question);
          const clozeCount = (card.question.match(/\{\{c\d+::[^}]+\}\}/g) || []).length;
          
          console.log(`   ✅ Has cloze markers: ${hasClozeMarkers}`);
          console.log(`   ✅ Number of cloze deletions: ${clozeCount}`);
          
          if (hasClozeMarkers) {
            // Extract cloze deletions
            const clozeMatches = card.question.match(/\{\{c(\d+)::([^}]+)\}\}/g);
            if (clozeMatches) {
              console.log(`   📋 Cloze deletions:`);
              clozeMatches.forEach(match => {
                const clozeMatch = match.match(/\{\{c(\d+)::([^}]+)\}\}/);
                if (clozeMatch) {
                  console.log(`      c${clozeMatch[1]}: "${clozeMatch[2]}"`);
                }
              });
            }
          } else {
            console.log(`   ⚠️ Missing proper cloze formatting!`);
          }
        });
        
        console.log('─'.repeat(80));

        // Test CSV generation for cloze cards
        console.log('\n📄 Testing CSV Export for Cloze Cards:');
        
        const sampleCloze = clozeCards[0];
        if (sampleCloze) {
          console.log('\nSample Cloze Card CSV Row:');
          
          // Simulate the CSV generation logic
          const front = `"${sampleCloze.question.replace(/"/g, '""')}"`;
          const back = '""'; // Empty for cloze cards
          const tags = `"${sampleCloze.tags.join(' ')} ${sampleCloze.type} ${sampleCloze.difficulty}"`;
          const sourceVideo = `"Test Video ${testVideoId}"`;
          const minutes = Math.floor(sampleCloze.timestamp / 60);
          const seconds = Math.floor(sampleCloze.timestamp % 60);
          const timestamp = `"${minutes}:${seconds.toString().padStart(2, '0')}"`;
          const videoUrl = `"https://youtube.com/watch?v=${testVideoId}&t=${Math.floor(sampleCloze.timestamp)}s"`;
          const cardType = '"cloze"';
          const difficulty = `"${sampleCloze.difficulty}"`;
          const createdDate = `"${new Date().toISOString().split('T')[0]}"`;
          const userNotes = '""';
          
          const csvRow = `${front},${back},${tags},${sourceVideo},${timestamp},${videoUrl},${cardType},${difficulty},${createdDate},${userNotes}`;
          
          console.log('CSV Header:');
          console.log('"Front","Back","Tags","Source_Video","Timestamp","Video_URL","Card_Type","Difficulty","Created_Date","User_Notes"');
          console.log('\nCSV Row:');
          console.log(csvRow);
          
          console.log('\n✅ Cloze CSV format validation:');
          console.log(`   - Front field contains cloze markers: ${front.includes('{{c')}`);
          console.log(`   - Back field is empty: ${back === '""'}`);
          console.log(`   - Card type is "cloze": ${cardType === '"cloze"'}`);
        }

      } else {
        console.log('⚠️ No cloze cards were generated. This might indicate:');
        console.log('   - The AI prompt needs adjustment');
        console.log('   - The content is not suitable for cloze cards');
        console.log('   - The distribution algorithm needs tuning');
      }

      // Test format validation
      console.log('\n🔍 Format Validation Summary:');
      const validClozeCards = clozeCards.filter(card => 
        /\{\{c\d+::[^}]+\}\}/.test(card.question) && 
        card.answer && 
        card.answer.length > 0
      );
      
      console.log(`✅ Valid cloze cards: ${validClozeCards.length}/${clozeCards.length}`);
      
      if (validClozeCards.length === clozeCards.length && clozeCards.length > 0) {
        console.log('🎉 All cloze cards are properly formatted!');
      } else if (clozeCards.length === 0) {
        console.log('⚠️ No cloze cards generated - check AI prompt');
      } else {
        console.log('⚠️ Some cloze cards need formatting fixes');
      }

    } else {
      console.log('❌ Card generation failed');
      console.log('   Response:', response.data);
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    
    if (error.response) {
      console.log('   Status:', error.response.status);
      console.log('   Data:', error.response.data);
    }
  }
}

// Run the test
testClozeFormatting().then(() => {
  console.log('\n🏁 Cloze formatting test completed');
});
