const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');
const path = require('path');
const { google } = require('googleapis');
const axios = require('axios');
const { YoutubeTranscript } = require('youtube-transcript');
const he = require('he'); // HTML entity decoder
const fs = require('fs');
const fsPromises = fs.promises;
const transcriptService = require('./services/transcriptService');
const geminiService = require('./services/geminiService');
const exampleCacheService = require('./services/exampleVideoCacheService');

// Load environment variables
dotenv.config();

// Utility function to format time in MM:SS or HH:MM:SS format
function formatTime(seconds) {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  if (hours > 0) {
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  } else {
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }
}

// Helper function to detect content density for card generation
function detectContentDensity(transcript) {
  const text = transcript.toLowerCase();

  // High-density indicators (lectures, tutorials, educational content)
  const highDensityKeywords = [
    'tutorial', 'lesson', 'lecture', 'course', 'learn', 'teach', 'explain', 'definition',
    'concept', 'theory', 'principle', 'method', 'technique', 'algorithm', 'formula',
    'step', 'process', 'procedure', 'instruction', 'guide', 'how to', 'what is',
    'why does', 'when to', 'where to', 'which means', 'in other words', 'for example',
    'specifically', 'important', 'key point', 'remember', 'note that', 'understand',
    'analysis', 'research', 'study', 'data', 'evidence', 'conclusion', 'result',
    'first', 'second', 'third', 'next', 'then', 'finally', 'therefore', 'because'
  ];

  // Count high-density keyword occurrences
  let densityScore = 0;
  highDensityKeywords.forEach(keyword => {
    const matches = (text.match(new RegExp(keyword, 'g')) || []).length;
    densityScore += matches;
  });

  // Calculate density ratio (keywords per 1000 characters)
  const densityRatio = (densityScore / transcript.length) * 1000;

  console.log(`Content density analysis: ${densityScore} keywords, ${densityRatio.toFixed(2)} per 1000 chars`);

  // Determine multiplier based on density (more generous thresholds)
  if (densityRatio > 8) {
    return 2.0; // High-density content (lectures, tutorials)
  } else if (densityRatio > 4) {
    return 1.5; // Medium-density content (educational discussions)
  } else {
    return 1.2; // Standard content gets slight boost for better coverage
  }
}

// Utility function to validate and fix timestamps in generated content
function validateAndFixTimestamps(text, maxDurationSeconds) {
  // More comprehensive regex to catch various timestamp formats
  const timestampRegex = /(\d{1,3}:\d{2})/g;
  const matches = text.match(timestampRegex);

  if (!matches) {
    return { hasInvalidTimestamps: false, invalidCount: 0, fixedDescription: text };
  }

  let invalidCount = 0;
  let fixedText = text;
  const maxValidMinutes = Math.floor(maxDurationSeconds / 60);
  const maxValidSeconds = Math.floor(maxDurationSeconds % 60);
  const maxValidTimestamp = `${maxValidMinutes.toString().padStart(2, '0')}:${maxValidSeconds.toString().padStart(2, '0')}`;

  console.log(`🔍 Validating timestamps against max duration: ${formatTime(maxDurationSeconds)}`);

  matches.forEach(timestamp => {
    const [minutes, seconds] = timestamp.split(':').map(Number);

    // Check for invalid time format (e.g., seconds > 59)
    const isInvalidFormat = seconds >= 60;
    const totalSeconds = minutes * 60 + seconds;
    const exceedsVideoDuration = totalSeconds > maxDurationSeconds;

    if (isInvalidFormat || exceedsVideoDuration) {
      invalidCount++;

      let validTimestamp;
      if (isInvalidFormat) {
        // For invalid format, use a reasonable timestamp within the video
        const reasonableMinutes = Math.min(minutes, maxValidMinutes);
        const reasonableSeconds = Math.min(59, maxValidSeconds);
        validTimestamp = `${reasonableMinutes.toString().padStart(2, '0')}:${reasonableSeconds.toString().padStart(2, '0')}`;
        console.log(`🔧 Fixed invalid format timestamp: ${timestamp} -> ${validTimestamp} (invalid seconds)`);
      } else {
        // For timestamps that exceed duration, use max valid timestamp
        validTimestamp = maxValidTimestamp;
        console.log(`🔧 Fixed timestamp exceeding duration: ${timestamp} -> ${validTimestamp} (video ends at ${formatTime(maxDurationSeconds)})`);
      }

      // Replace the invalid timestamp with the valid one
      fixedText = fixedText.replace(new RegExp(timestamp.replace(':', '\\:'), 'g'), validTimestamp);
    } else {
      console.log(`✅ Valid timestamp: ${timestamp} (${totalSeconds}s)`);
    }
  });

  console.log(`📊 Timestamp validation complete: ${invalidCount} invalid timestamps fixed`);

  return {
    hasInvalidTimestamps: invalidCount > 0,
    invalidCount,
    fixedDescription: fixedText
  };
}

// Initialize Express app
const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());

// YouTube API configuration
const YOUTUBE_API_KEY = process.env.YOUTUBE_API_KEY;
const YOUTUBE_API_BASE_URL = 'https://www.googleapis.com/youtube/v3';

// Log API key status (without revealing the full key)
const apiKeyStatus = YOUTUBE_API_KEY
  ? `API key configured (starts with ${YOUTUBE_API_KEY.substring(0, 5)}...)`
  : 'API key not configured';
console.log(`YouTube API status: ${apiKeyStatus}`);

// Check if frontend build directory exists
const frontendBuildPath = path.join(__dirname, '../frontend/youtube-transcribe/dist');
const frontendExists = fs.existsSync(frontendBuildPath);

// Serve static files from the public directory
const publicPath = path.join(__dirname, 'public');
app.use(express.static(publicPath));
console.log(`Serving static files from: ${publicPath}`);

if (frontendExists) {
  console.log(`Serving frontend static files from: ${frontendBuildPath}`);
  // Serve static files
  app.use(express.static(frontendBuildPath));
} else {
  console.log('Frontend build directory not found. Only API endpoints will be available.');
}

// API Routes
// Health check
app.get('/api/health', (req, res) => {
  res.json({ status: 'ok', message: 'Server is running' });
});

// Get example videos
app.get('/api/examples', async (req, res) => {
  try {
    const exampleVideosPath = path.join(__dirname, 'data/example-videos.json');
    const exampleVideosData = JSON.parse(await fsPromises.readFile(exampleVideosPath, 'utf8'));
    res.json(exampleVideosData);
  } catch (error) {
    console.error('Error fetching example videos:', error.message);
    res.status(500).json({
      error: 'Failed to fetch example videos',
      details: process.env.NODE_ENV === 'development' ? error.toString() : undefined
    });
  }
});

// Get video information
app.get('/api/video/:videoId', async (req, res) => {
  try {
    const { videoId } = req.params;

    // Make a direct request to the YouTube API using axios
    const response = await axios.get(`${YOUTUBE_API_BASE_URL}/videos`, {
      params: {
        part: 'snippet,contentDetails',
        id: videoId,
        key: YOUTUBE_API_KEY
      }
    });

    if (!response.data.items || response.data.items.length === 0) {
      return res.status(404).json({ error: 'Video not found' });
    }

    res.json(response.data.items[0]);
  } catch (error) {
    console.error('Error fetching video info:', error.message);

    // Extract error details from YouTube API response
    const youtubeError = error.response?.data?.error;
    const errorMessage = youtubeError?.message || 'Failed to fetch video information';
    const statusCode = error.response?.status || 500;

    res.status(statusCode).json({
      error: errorMessage,
      details: process.env.NODE_ENV === 'development' ? JSON.stringify(youtubeError) : undefined
    });
  }
});

// Get video captions/subtitles
app.get('/api/captions/:videoId', async (req, res) => {
  try {
    const { videoId } = req.params;

    // Make a direct request to the YouTube API using axios
    const captionResponse = await axios.get(`${YOUTUBE_API_BASE_URL}/captions`, {
      params: {
        part: 'snippet',
        videoId: videoId,
        key: YOUTUBE_API_KEY
      }
    });

    if (!captionResponse.data.items || captionResponse.data.items.length === 0) {
      return res.status(404).json({ error: 'No captions found for this video' });
    }

    res.json(captionResponse.data.items);
  } catch (error) {
    console.error('Error fetching captions:', error.message);

    // Extract error details from YouTube API response
    const youtubeError = error.response?.data?.error;
    const errorMessage = youtubeError?.message || 'Failed to fetch captions';
    const statusCode = error.response?.status || 500;

    res.status(statusCode).json({
      error: errorMessage,
      details: process.env.NODE_ENV === 'development' ? JSON.stringify(youtubeError) : undefined
    });
  }
});

// Helper function to format time in HH:MM:SS format
function formatTime(seconds) {
  // Convert to milliseconds and create a date object
  const date = new Date(seconds * 1000);

  // Extract hours, minutes, seconds
  const hours = date.getUTCHours();
  const minutes = date.getUTCMinutes();
  const secs = date.getUTCSeconds();

  // Format the time string
  return [
    hours > 0 ? hours.toString().padStart(2, '0') : null,
    minutes.toString().padStart(2, '0'),
    secs.toString().padStart(2, '0')
  ].filter(Boolean).join(':');
}

// Format time for SRT format (HH:MM:SS,mmm)
function formatSrtTime(seconds) {
  const date = new Date(seconds * 1000);
  const hours = date.getUTCHours().toString().padStart(2, '0');
  const minutes = date.getUTCMinutes().toString().padStart(2, '0');
  const secs = date.getUTCSeconds().toString().padStart(2, '0');
  const ms = date.getUTCMilliseconds().toString().padStart(3, '0');

  return `${hours}:${minutes}:${secs},${ms}`;
}

// Format time for WebVTT format (HH:MM:SS.mmm)
function formatVttTime(seconds) {
  const date = new Date(seconds * 1000);
  const hours = date.getUTCHours().toString().padStart(2, '0');
  const minutes = date.getUTCMinutes().toString().padStart(2, '0');
  const secs = date.getUTCSeconds().toString().padStart(2, '0');
  const ms = date.getUTCMilliseconds().toString().padStart(3, '0');

  return `${hours}:${minutes}:${secs}.${ms}`;
}

// Convert transcript to plain text format
function convertToTxt(transcript) {
  return transcript.map(item => {
    return `[${item.formattedStart}] ${item.text}`;
  }).join('\n\n');
}

// Convert transcript to SRT format
function convertToSrt(transcript) {
  return transcript.map((item, index) => {
    // Format timestamps for SRT (HH:MM:SS,mmm)
    const startTime = formatSrtTime(item.start);
    const endTime = formatSrtTime(item.end);

    return `${index + 1}\n${startTime} --> ${endTime}\n${item.text}\n`;
  }).join('\n');
}

// Convert transcript to WebVTT format
function convertToVtt(transcript) {
  // WebVTT header
  let vtt = 'WEBVTT\n\n';

  // Add cues
  vtt += transcript.map((item, index) => {
    // Format timestamps for VTT (HH:MM:SS.mmm)
    const startTime = formatVttTime(item.start);
    const endTime = formatVttTime(item.end);

    return `${index + 1}\n${startTime} --> ${endTime}\n${item.text}`;
  }).join('\n\n');

  return vtt;
}

// Convert transcript to DOC format (simple RTF format that can be opened by Word)
function convertToDoc(transcript) {
  // RTF header
  let rtf = '{\\rtf1\\ansi\\deff0 {\\fonttbl {\\f0 Times New Roman;}}';
  rtf += '\\f0\\fs24 '; // Set font and size

  // Add title
  rtf += '{\\b\\fs28 YouTube Transcript}\\par\\par';

  // Add transcript content
  transcript.forEach(item => {
    // Add timestamp in bold
    rtf += `{\\b [${item.formattedStart}]} `;
    // Add text with proper RTF escaping
    const escapedText = item.text
      .replace(/\\/g, '\\\\')
      .replace(/\{/g, '\\{')
      .replace(/\}/g, '\\}');
    rtf += escapedText + '\\par\\par';
  });

  rtf += '}'; // Close RTF document
  return rtf;
}

// Download captions in specific format
app.get('/api/captions/:videoId/download', async (req, res) => {
  try {
    const { videoId } = req.params;
    const { format = 'txt', lang = 'en' } = req.query;

    // For example videos, use cache; for others, fetch fresh to ensure completeness
    const isExampleVideo = exampleCacheService.isExampleVideo(videoId);
    const forceFresh = !isExampleVideo; // Only force fresh for non-example videos
    const transcriptData = await transcriptService.getTranscript(videoId, lang, forceFresh);

    if (!transcriptData || !transcriptData.transcript || transcriptData.transcript.length === 0) {
      return res.status(404).json({ error: 'No transcript found for this video' });
    }

    const formattedTranscript = transcriptData.transcript;

    // Convert to the requested format
    let content = '';
    let filename = `youtube-transcript-${videoId}`;

    switch (format.toLowerCase()) {
      case 'txt':
        content = convertToTxt(formattedTranscript);
        filename += '.txt';
        res.setHeader('Content-Type', 'text/plain');
        break;

      case 'srt':
        content = convertToSrt(formattedTranscript);
        filename += '.srt';
        res.setHeader('Content-Type', 'text/plain');
        break;

      case 'vtt':
        content = convertToVtt(formattedTranscript);
        filename += '.vtt';
        res.setHeader('Content-Type', 'text/vtt');
        break;

      case 'doc':
        content = convertToDoc(formattedTranscript);
        filename += '.rtf'; // Use RTF extension for better compatibility
        res.setHeader('Content-Type', 'application/rtf');
        break;

      default:
        return res.status(400).json({ error: 'Unsupported format. Supported formats: txt, srt, vtt, doc' });
    }

    // Set headers for file download
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);

    // Send the file content
    res.send(content);

  } catch (error) {
    console.error('Error downloading captions:', error.message);

    const errorMessage = error.message || 'Failed to download captions';
    const statusCode = error.response?.status || 500;

    res.status(statusCode).json({
      error: errorMessage,
      details: process.env.NODE_ENV === 'development' ? error.toString() : undefined
    });
  }
});

// Get transcript content
app.get('/api/transcript/:videoId', async (req, res) => {
  try {
    const { videoId } = req.params;
    const { lang = 'en' } = req.query;
    // For example videos, NEVER use forceFresh - always prioritize cache
    const isExampleVideo = exampleCacheService.isExampleVideo(videoId);
    const forceFresh = isExampleVideo ? false : (req.query._t ? true : false);

    // Check if this is an example video and try cache first
    if (isExampleVideo && !forceFresh) {
      const cachedTranscript = await exampleCacheService.getCachedTranscript(videoId, lang);
      if (cachedTranscript) {
        console.log(`🚀 Fast cache hit for example video ${videoId} in ${lang}`);
        return res.json(cachedTranscript);
      }
    }

    // Use the new transcript service with fallback methods
    // Pass forceFresh to skip cache when language changes
    const transcript = await transcriptService.getTranscript(videoId, lang, forceFresh);

    // Cache the transcript if it's an example video
    if (exampleCacheService.isExampleVideo(videoId) && transcript) {
      await exampleCacheService.saveTranscriptToCache(videoId, lang, transcript);
    }

    // Return the transcript
    res.json(transcript);
  } catch (error) {
    console.error('Error fetching transcript:', error.message);
    res.status(500).json({
      error: 'Failed to fetch transcript',
      details: process.env.NODE_ENV === 'development' ? error.toString() : undefined
    });
  }
});

// Gemini AI API endpoints
// Test Gemini connection
app.get('/api/gemini/test', async (req, res) => {
  try {
    const testResult = await geminiService.testConnection();
    res.json(testResult);
  } catch (error) {
    console.error('Error testing Gemini connection:', error.message);
    res.status(500).json({
      error: 'Failed to test Gemini connection',
      details: process.env.NODE_ENV === 'development' ? error.toString() : undefined
    });
  }
});

// Generate content with Gemini
app.post('/api/gemini/generate', async (req, res) => {
  try {
    const { prompt, options = {} } = req.body;

    if (!prompt || typeof prompt !== 'string') {
      return res.status(400).json({ error: 'Prompt is required and must be a string' });
    }

    const response = await geminiService.generateContent(prompt, options);

    res.json({
      success: true,
      prompt,
      response,
      model: 'models/gemini-2.5-flash-lite-preview-06-17'
    });
  } catch (error) {
    console.error('Error generating content with Gemini:', error.message);
    res.status(500).json({
      error: 'Failed to generate content',
      details: process.env.NODE_ENV === 'development' ? error.toString() : undefined
    });
  }
});

// Get Gemini model info
app.get('/api/gemini/info', (req, res) => {
  try {
    const modelInfo = geminiService.getModelInfo();
    res.json(modelInfo);
  } catch (error) {
    console.error('Error getting Gemini model info:', error.message);
    res.status(500).json({
      error: 'Failed to get model info',
      details: process.env.NODE_ENV === 'development' ? error.toString() : undefined
    });
  }
});

// Generate video summary using Gemini
app.get('/api/summary/:videoId', async (req, res) => {
  try {
    const { videoId } = req.params;
    const { lang = 'en' } = req.query;

    console.log(`Generating summary for video ${videoId} in language ${lang}`);

    // Check if this is an example video and try cache first
    if (exampleCacheService.isExampleVideo(videoId)) {
      const cachedSummary = await exampleCacheService.getCachedContent(videoId, lang, 'summary');
      if (cachedSummary) {
        console.log(`🚀 Fast cache hit for summary ${videoId} in ${lang}`);
        return res.json(cachedSummary);
      }
    }

    // First, get the transcript for the video
    const transcriptData = await transcriptService.getTranscript(videoId, lang, false);

    if (!transcriptData || !transcriptData.transcript || transcriptData.transcript.length === 0) {
      return res.status(404).json({ error: 'No transcript found for this video' });
    }

    // Extract the text content from the transcript
    const transcriptText = transcriptData.transcript
      .map(item => item.text)
      .join(' ')
      .trim();

    if (!transcriptText) {
      return res.status(400).json({ error: 'Transcript is empty' });
    }

    console.log(`Transcript length: ${transcriptText.length} characters`);

    // Create the updated prompt for Gemini
    const prompt = `You are an expert summarizer of transcripts. Your goal is to create two distinct summaries using ONLY the following markdown structure. Your summaries will be evaluated on conciseness, accuracy, clarity, insightfulness, and actionability. Do not add any additional text or commentary.

## 🎯 Key Takeaways

**Core Points:**

Create a concise bullet-point summary (4-6 points)
Highlight the most crucial information and actionable main takeaways.
Present each point in a clear, generalized, and prescriptive format.
Keep the total length to approximately 150 words

## 🔍 Summary
Provide a detailed summary (approximately 200-500 words, but not more than half the transcript length) that includes:

Main Themes:

Identify and summarize the primary topics and key discussions.
Extract important insights and conclusions from the discussions.
Provide supporting details and context to explain the main points.
Include notable examples or illustrative scenarios that reinforce understanding.
Explicitly identify and explain relevant connections and transitions between topics.
Formatting Requirements:

Begin each main topic discussion with a bold header using **Topic Name**.
Structure content under each bold topic header with relevant details and insights.
Use paragraphs containing clear topic sentences for flowing information.
Maintain clarity while preserving important nuances and avoiding redundancy.
Don't use bullet point lists for the In-Depth Analysis but rather normal sentences.

Specifically, I am looking for summaries that are:

Concise: Avoid unnecessary wordiness.
Actionable: Extract insights that the reader can apply.
Insightful: Go beyond just restating facts; provide deeper understanding.
Well-Organized: Group related themes together logically, with smooth transitions.
Nuanced: Capture subtle distinctions and complex arguments.
Include Topic Sentences: Ensure all paragraphs have topic sentences that lead to the topics being discussed.

For example:

**Main Topic 1**
[Detailed discussion of first main topic]

**Main Topic 2**
[Detailed discussion of second main topic]

Create two distinct summaries using ONLY the following markdown structure. Do not add any additional text or commentary.

${transcriptText}`;

    // Generate the summary using Gemini
    const summary = await geminiService.generateContent(prompt, {
      temperature: 0.7,
      maxOutputTokens: 2048
    });

    const responseData = {
      success: true,
      videoId,
      language: transcriptData.language || lang,
      summary,
      transcriptLength: transcriptData.transcript.length,
      model: 'models/gemini-2.5-flash-lite-preview-06-17'
    };

    // Cache the summary if it's an example video
    if (exampleCacheService.isExampleVideo(videoId)) {
      await exampleCacheService.saveContentToCache(videoId, lang, 'summary', responseData);
    }

    res.json(responseData);

  } catch (error) {
    console.error('Error generating summary:', error.message);
    res.status(500).json({
      error: 'Failed to generate summary',
      details: process.env.NODE_ENV === 'development' ? error.toString() : undefined
    });
  }
});

// Generate video quotes using Gemini
app.get('/api/quotes/:videoId', async (req, res) => {
  try {
    const { videoId } = req.params;
    const { lang = 'en' } = req.query;

    console.log(`Generating quotes for video ${videoId} in language ${lang}`);

    // Check if this is an example video and try cache first
    if (exampleCacheService.isExampleVideo(videoId)) {
      const cachedQuotes = await exampleCacheService.getCachedContent(videoId, lang, 'quotes');
      if (cachedQuotes) {
        console.log(`🚀 Fast cache hit for quotes ${videoId} in ${lang}`);
        return res.json(cachedQuotes);
      }
    }

    // First, get the transcript for the video
    const transcriptData = await transcriptService.getTranscript(videoId, lang, false);

    if (!transcriptData || !transcriptData.transcript || transcriptData.transcript.length === 0) {
      return res.status(404).json({ error: 'No transcript found for this video' });
    }

    // Extract the text content from the transcript
    const transcriptText = transcriptData.transcript
      .map(item => item.text)
      .join(' ')
      .trim();

    if (!transcriptText) {
      return res.status(400).json({ error: 'Transcript is empty' });
    }

    console.log(`Transcript length: ${transcriptText.length} characters`);

    // Create the quotes prompt for Gemini
    const prompt = `### ROLE ###
You are an expert text analyst and research assistant. Your sole purpose is to meticulously scan a provided transcript and extract significant quotes based on a set of predefined categories.

### TASK ###
You will analyze the transcript provided below and identify quotes that fall into the specified categories. For each category, you will extract at least two distinct quotes.

### CRITICAL RULES ###
1.  **VERBATIM EXTRACTION ONLY:** You must NOT, under any circumstances, alter, paraphrase, summarize, or invent any text. Every single character of an extracted quote (including punctuation) must be an exact, 1:1 copy of a corresponding sequence of text from the source transcript.
2.  **COMPLETE SENTIMENTS:** A quote should represent a complete thought or idea. Do not cut off a sentence midway. Find a concise, complete sentence or phrase that is directly from the text.
3.  **NO COMBINING:** Do not combine two separate sentences from different parts of the transcript to form a single quote. Each quote must be a contiguous block of text from the original source.
4.  **QUOTE REQUIREMENTS:** For each category, find at least two quotes. If you can find only one quote that fits, provide just that one. If you cannot find any suitable quotes for a specific category, you must output: "No significant quotes found for this category." Do not force a quote to fit.
5.  **NO SPEAKER NAMES:** Do not include speaker names or any attribution in your output.

### QUOTE CATEGORIES TO FIND ###
1.  **Pithy & Memorable Statement:** A concise, impactful "soundbite."
2.  **Core Argument & Thesis:** A statement capturing the central message.
3.  **Surprising or Controversial Claim:** A statement that challenges common wisdom.
4.  **Key Data & Evidence:** A statement containing a specific statistic or piece of evidence.
5.  **Actionable Insight & Directive:** A clear instruction or recommendation.
6.  **Emotional & Anecdotal Moment:** A quote conveying strong emotion or a personal story.
7.  **Forward-Looking Statement & Prediction:** A statement about the future.

### OUTPUT FORMAT ###
Present the extracted quotes in the following Markdown format. For each category, provide at least two quotes as separate blockquotes.

---

**1. Pithy & Memorable Statement**
> "[Insert verbatim quote #1 here.]"
> "[Insert verbatim quote #2 here.]"

**2. Core Argument & Thesis**
> "[Insert verbatim quote #1 here.]"
> "[Insert verbatim quote #2 here.]"

**3. Surprising or Controversial Claim**
> "[Insert verbatim quote #1 here.]"
> "[Insert verbatim quote #2 here.]"

**4. Key Data & Evidence**
> "[Insert verbatim quote #1 here.]"
> "[Insert verbatim quote #2 here.]"

**5. Actionable Insight & Directive**
> "[Insert verbatim quote #1 here.]"
> "[Insert verbatim quote #2 here.]"

**6. Emotional & Anecdotal Moment**
> "[Insert verbatim quote #1 here.]"
> "[Insert verbatim quote #2 here.]"

**7. Forward-Looking Statement & Prediction**
> "[Insert verbatim quote #1 here.]"
> "[Insert verbatim quote #2 here.]"

---

### TRANSCRIPT FOR ANALYSIS ###

${transcriptText}`;

    // Generate the quotes using Gemini
    const quotes = await geminiService.generateContent(prompt, {
      temperature: 0.3,
      maxOutputTokens: 2048
    });

    const responseData = {
      success: true,
      videoId,
      language: transcriptData.language || lang,
      quotes,
      transcriptLength: transcriptData.transcript.length,
      model: 'models/gemini-2.5-flash-lite-preview-06-17'
    };

    // Cache the quotes if it's an example video
    if (exampleCacheService.isExampleVideo(videoId)) {
      await exampleCacheService.saveContentToCache(videoId, lang, 'quotes', responseData);
    }

    res.json(responseData);

  } catch (error) {
    console.error('Error generating quotes:', error.message);
    res.status(500).json({
      error: 'Failed to generate quotes',
      details: process.env.NODE_ENV === 'development' ? error.toString() : undefined
    });
  }
});

// Generate YouTube description with timestamps using Gemini
app.post('/api/description', async (req, res) => {
  try {
    const { videoId, videoTitle, keywords, ctaGoal, lang = 'en' } = req.body;

    if (!videoId) {
      return res.status(400).json({ error: 'Video ID is required' });
    }

    console.log(`Generating YouTube description for video ${videoId} in language ${lang}`);

    // Check if this is an example video and try cache first
    if (exampleCacheService.isExampleVideo(videoId)) {
      const cachedDescription = await exampleCacheService.getCachedContent(videoId, lang, 'description');
      if (cachedDescription) {
        console.log(`🚀 Fast cache hit for description ${videoId} in ${lang}`);
        return res.json(cachedDescription);
      }
    }

    // First, get the transcript for the video
    const transcriptData = await transcriptService.getTranscript(videoId, lang, false);

    if (!transcriptData || !transcriptData.transcript || transcriptData.transcript.length === 0) {
      return res.status(404).json({ error: 'No transcript found for this video' });
    }

    // Calculate video duration from transcript
    const lastItem = transcriptData.transcript[transcriptData.transcript.length - 1];
    const videoDurationSeconds = lastItem.end;
    const videoDurationFormatted = formatTime(videoDurationSeconds);

    // Extract the text content from the transcript with timestamps
    const transcriptWithTimestamps = transcriptData.transcript
      .map(item => `${item.formattedStart} - ${item.text}`)
      .join('\n');

    if (!transcriptWithTimestamps) {
      return res.status(400).json({ error: 'Transcript is empty' });
    }

    console.log(`Transcript length: ${transcriptWithTimestamps.length} characters`);
    console.log(`Video duration: ${videoDurationSeconds} seconds (${videoDurationFormatted})`);

    // Create the description prompt for Gemini
    const prompt = `### ROLE ###
You are an expert YouTube Strategist and compelling storyteller. Your mission is to "watch" a video by reading its transcript and then write a captivating YouTube description and create helpful timestamps. You distill the video's most powerful stories, lessons, and quotes into a description that makes people eager to click play.

### TASK ###
You will be given a video transcript and some guiding information (topic, keywords). Your job is to write a single, high-impact, conversational YouTube description and a set of timestamps.

### CRITICAL RULE FOR DESCRIPTION CONTENT ###
**The description MUST be about the content of the video itself, based entirely on the provided transcript.** You will extract the core themes, stories, advice, and emotional journey shared by the speaker(s). **DO NOT**, under any circumstances, write a meta-description about the process of content analysis, metrics, or video strategy, unless that is the literal topic of the transcript. Your job is to summarize the video's message, not your own task.

### DESCRIPTION WRITING GUIDELINES ###
- **Tone:** Conversational, friendly, and engaging. Speak directly to the viewer.
- **Hook (First 2-3 Lines):** Write a captivating hook (**under 300 characters**) that reflects a central theme or a powerful question raised in the video. It must include the main keyword and make someone stop scrolling.
- **Value & Substance:** After the hook, summarize the core message of the video. What powerful stories are shared? What key lessons will the viewer learn from the speaker? Pull these ideas directly from the transcript.
- **Keyword Integration:** Naturally weave the provided keywords into the description. The text must read like a human wrote it.
- **Call-to-Action (CTA):** Based on the user's goal, write a clear and friendly call-to-action to encourage interaction.

### TIMESTAMP GENERATION GUIDELINES ###
- **CRITICAL CONSTRAINT:** The video duration is ${videoDurationFormatted}. ALL timestamps MUST be within this duration. Never generate timestamps beyond ${videoDurationFormatted}.
- **Analyze the Transcript:** Read the transcript to identify distinct sections, topic changes, stories, or key takeaways.
- **Create Descriptive Labels:** For each timestamp, write a short, clear label that describes what happens in that section of the video.
- **Format:** Timestamps must be in the \`MM:SS\` format.
- **Start at Zero:** Always include a \`00:00\` timestamp for the Introduction.
- **Quantity:** Generate between 5 and 10 timestamps.
- **Validation:** Before finalizing each timestamp, verify it does not exceed ${videoDurationFormatted}.

### OUTPUT FORMAT ###
---
[Begin the description here with the 2-3 line hook. Continue by summarizing the key stories and lessons from the transcript, making it clear what the viewer will gain from watching. The tone should be conversational. End this section with the call-to-action.]

***

⏰ **TIMESTAMPS**
00:00 - [Label for the Introduction]
[MM:SS] - [Label for the next key moment/story]
[MM:SS] - [Label for the next key moment/story]
[MM:SS] - [And so on...]

***

#[Hashtag1] #[Hashtag2] #[Hashtag3] #[Hashtag4]
---

### USER-PROVIDED INFORMATION ###

**Video Topic/Title:** ${videoTitle || 'Analyze the video content and create an appropriate title'}

**Main Keywords:** ${keywords || 'Extract 2-3 relevant keywords from the video content'}

**Call-to-Action Goal:** ${ctaGoal || 'Subscribe for more content'}

**Video Duration:** ${videoDurationFormatted} (${Math.round(videoDurationSeconds)} seconds)

**IMPORTANT:** All timestamps must be within 00:00 to ${videoDurationFormatted}. Do not exceed this duration.

**Video Transcript:**
${transcriptWithTimestamps}`;

    // Generate the description using Gemini
    let description = await geminiService.generateContent(prompt, {
      temperature: 0.7,
      maxOutputTokens: 2048
    });

    // Validate and fix timestamps in the generated description
    const timestampValidation = validateAndFixTimestamps(description, videoDurationSeconds);
    if (timestampValidation.hasInvalidTimestamps) {
      console.log(`⚠️ Found ${timestampValidation.invalidCount} invalid timestamps, fixing...`);
      description = timestampValidation.fixedDescription;
    }

    const responseData = {
      success: true,
      videoId,
      language: transcriptData.language || lang,
      description,
      transcriptLength: transcriptData.transcript.length,
      videoTitle: videoTitle || 'Video Content Analysis',
      keywords: keywords || 'video content, analysis, insights',
      ctaGoal: ctaGoal || 'Subscribe for more content',
      model: 'models/gemini-2.5-flash-lite-preview-06-17',
      videoDuration: videoDurationFormatted,
      timestampValidation: timestampValidation.hasInvalidTimestamps ? {
        originalInvalidCount: timestampValidation.invalidCount,
        fixed: true
      } : { fixed: false }
    };

    // Cache the description if it's an example video
    if (exampleCacheService.isExampleVideo(videoId)) {
      await exampleCacheService.saveContentToCache(videoId, lang, 'description', responseData);
    }

    res.json(responseData);

  } catch (error) {
    console.error('Error generating description:', error.message);
    res.status(500).json({
      error: 'Failed to generate description',
      details: process.env.NODE_ENV === 'development' ? error.toString() : undefined
    });
  }
});

// Generate Anki flash cards using Gemini
app.post('/api/anki-cards', async (req, res) => {
  try {
    const { videoId, lang = 'en' } = req.body;

    if (!videoId) {
      return res.status(400).json({ error: 'Video ID is required' });
    }

    console.log(`Generating Anki flash cards for video ${videoId} in language ${lang}`);

    // Check if this is an example video and try cache first
    if (exampleCacheService.isExampleVideo(videoId)) {
      const cachedAnkiCards = await exampleCacheService.getCachedContent(videoId, lang, 'anki-cards');
      if (cachedAnkiCards) {
        console.log(`🚀 Fast cache hit for anki-cards ${videoId} in ${lang}`);
        return res.json(cachedAnkiCards);
      }
    }

    // First, get the transcript for the video
    const transcriptData = await transcriptService.getTranscript(videoId, lang, false);

    if (!transcriptData || !transcriptData.transcript || transcriptData.transcript.length === 0) {
      return res.status(404).json({ error: 'No transcript found for this video' });
    }

    // Calculate video duration from transcript
    const lastItem = transcriptData.transcript[transcriptData.transcript.length - 1];
    const videoDurationSeconds = lastItem ? (lastItem.end || lastItem.start + 5) : 300;
    const videoDurationMinutes = videoDurationSeconds / 60;
    const videoDurationFormatted = formatTime(videoDurationSeconds);

    // Extract the text content from the transcript WITH timestamps for better context
    const transcriptSegments = transcriptData.transcript.map((item, index) => ({
      id: index + 1,
      timestamp: item.start,
      formattedTime: formatTime(item.start),
      text: item.text.trim(),
      duration: (item.end || item.start + 3) - item.start
    }));

    // Create a structured transcript text that preserves timing context
    const structuredTranscript = transcriptSegments
      .map(segment => `[${segment.formattedTime}] ${segment.text}`)
      .join('\n');

    if (!structuredTranscript) {
      return res.status(400).json({ error: 'Transcript is empty' });
    }

    console.log(`Transcript segments: ${transcriptSegments.length}`);
    console.log(`Structured transcript length: ${structuredTranscript.length} characters`);
    console.log(`Video duration: ${videoDurationSeconds} seconds (${videoDurationFormatted})`);

    // Calculate appropriate number of cards based on video duration and content density
    // Base requirement: 1 card per 2 minutes, with generous minimums
    const baseCardCount = Math.ceil(videoDurationMinutes / 2.0);

    // Detect content density for multiplier
    const contentDensityMultiplier = detectContentDensity(structuredTranscript);

    // Apply density multiplier (2x for high-density content like lectures/tutorials)
    const adjustedCardCount = Math.ceil(baseCardCount * contentDensityMultiplier);

    // Ensure minimum requirements based on video length
    let targetCardCount = adjustedCardCount;
    if (videoDurationMinutes >= 10) {
      targetCardCount = Math.max(5, adjustedCardCount); // Minimum 5 for 10+ min videos
    } else if (videoDurationMinutes >= 5) {
      targetCardCount = Math.max(3, adjustedCardCount); // Minimum 3 for 5+ min videos
    } else {
      targetCardCount = Math.max(2, adjustedCardCount); // Minimum 2 for shorter videos
    }

    // Final count: 2-30 cards maximum (ensuring sufficient quantity)
    targetCardCount = Math.min(30, Math.max(2, targetCardCount));

    console.log(`Target card count: ${targetCardCount} (base: ${baseCardCount}, density: ${contentDensityMultiplier}x, duration: ${videoDurationMinutes.toFixed(1)} min)`);

    // Create the improved Anki cards prompt for Gemini
    const prompt = `### ROLE ###
You are an expert educational content creator specializing in creating accurate, transcript-based flashcards. Your primary goal is to extract factual information ONLY from the provided video transcript without adding external knowledge or making inferences.

### CRITICAL REQUIREMENTS ###
🚨 **TRANSCRIPT ACCURACY MANDATE** 🚨
- ONLY use information explicitly stated in the transcript
- DO NOT add external knowledge or context
- DO NOT make inferences beyond what is directly stated
- DO NOT create content from general knowledge about the topic
- Every answer must be traceable to specific transcript segments

### TASK ###
Analyze the timestamped video transcript and generate exactly ${targetCardCount} high-quality flashcards. Each card must be based on explicit content from the transcript.

### CARD TYPE DISTRIBUTION ###
Generate exactly ${targetCardCount} cards with this distribution:
- **70% Basic Q&A format** (${Math.round(targetCardCount * 0.7)} cards): "What is X?", "Define Y", "Who/What/When/Where/Why" questions
- **20% Process/sequence questions** (${Math.round(targetCardCount * 0.2)} cards): "What are the steps in X?", "What happens after Y?"
- **10% True/False statements** (${Math.round(targetCardCount * 0.1)} cards): Fact verification and common misconceptions

### CONTENT EXTRACTION RULES ###
1. **Quote Directly**: Use exact phrases and terminology from the transcript
2. **Timestamp Reference**: Each card must reference the specific time when the information appears
3. **Context Preservation**: Maintain the speaker's original meaning and context
4. **No Hallucination**: If information isn't explicitly stated, don't create a card about it
5. **Factual Focus**: Prioritize concrete facts, numbers, processes, and explicit statements
6. **Question Variety**: Use diverse question formats for engagement

### QUESTION TYPE GUIDELINES ###

**Basic Q&A (70%) - Use these question formats:**
- "What is [concept mentioned in transcript]?"
- "Who is [person mentioned]?"
- "When did [event] happen?"
- "Where does [location/process] occur?"
- "Why does [reason given in transcript]?"
- "How much/many [specific number/amount]?"
- "Define [term used by speaker]"
- "According to the speaker, what [specific claim]?"

**Process/Sequence (20%) - Focus on:**
- "What are the steps to [process mentioned]?"
- "What happens after [event in sequence]?"
- "How does the speaker describe the process of [topic]?"
- "What is the order of [sequence mentioned]?"
- "What comes first/next/last in [process]?"

**True/False (10%) - Create statements that:**
- Test specific facts mentioned in transcript
- Address common misconceptions the speaker corrects
- Verify numerical data or claims made
- Format: "True or False: [statement from transcript]"

### CARD QUALITY STANDARDS ###
- Question must be answerable using ONLY transcript content
- Answer must quote or paraphrase directly from transcript
- Include accurate timestamp where information appears
- Use clear, unambiguous language
- Avoid questions requiring external knowledge
- Ensure answers are complete but concise

### OUTPUT FORMAT ###
Return a valid JSON array with card objects. Each card must have this exact structure:

{
  "type": "qa" | "process" | "truefalse",
  "question": "string",
  "answer": "string",
  "timestamp": number (seconds from video start),
  "difficulty": "beginner" | "intermediate" | "advanced",
  "tags": ["concept1", "concept2"],
  "context": "exact quote or paraphrase from transcript",
  "transcriptQuote": "direct quote from transcript that supports this card"
}

### EXAMPLE CARD CREATION PROCESS ###

**Step 1**: Find explicit information in transcript
Transcript segment: "[5:23] The company went from 2,000 to 10,000 monthly recurring revenue in one month"

**Step 2**: Create card based ONLY on this information
{
  "type": "qa",
  "question": "What was the company's monthly recurring revenue growth mentioned in the video?",
  "answer": "The company went from 2,000 to 10,000 monthly recurring revenue in one month",
  "timestamp": 323,
  "difficulty": "beginner",
  "tags": ["revenue", "growth", "business"],
  "context": "Discussion of rapid business growth",
  "transcriptQuote": "The company went from 2,000 to 10,000 monthly recurring revenue in one month"
}

### CARD TYPE EXAMPLES ###

**Q&A Card** (Direct fact from transcript):
{
  "type": "qa",
  "question": "According to the speaker, what specific amount did they mention?",
  "answer": "[Exact quote or close paraphrase from transcript]",
  "timestamp": [actual timestamp],
  "difficulty": "beginner",
  "tags": ["relevant", "topics"],
  "context": "Context from transcript",
  "transcriptQuote": "[Exact quote from transcript]"
}

**Process Card** (Step-by-step from transcript):
{
  "type": "process",
  "question": "What process did the speaker describe for [specific topic]?",
  "answer": "[Steps as mentioned in transcript, in order]",
  "timestamp": [actual timestamp],
  "difficulty": "intermediate",
  "tags": ["process", "steps"],
  "context": "Process explanation from transcript",
  "transcriptQuote": "[Exact quote describing the process]"
}

**True/False Card** (Based on explicit statement):
{
  "type": "truefalse",
  "question": "True or False: [Statement directly from or based on transcript]",
  "answer": "True/False - [Explanation using transcript content]",
  "timestamp": [actual timestamp],
  "difficulty": "beginner",
  "tags": ["fact", "verification"],
  "context": "Factual statement from transcript",
  "transcriptQuote": "[Supporting quote from transcript]"
}

### VIDEO INFORMATION ###
- **Video Duration**: ${videoDurationFormatted} (${Math.round(videoDurationSeconds)} seconds)
- **Target Cards**: ${targetCardCount}
- **Language**: ${lang}
- **Transcript Segments**: ${transcriptSegments.length}

### TIMESTAMPED TRANSCRIPT ###
${structuredTranscript}

### FINAL INSTRUCTIONS ###
1. Read through the entire timestamped transcript carefully
2. Identify ${targetCardCount} distinct pieces of factual information
3. Create cards that test knowledge of these specific facts
4. Ensure every answer can be verified against the transcript
5. Use accurate timestamps from the transcript segments
6. Include the "transcriptQuote" field with exact supporting text

Generate exactly ${targetCardCount} flashcards now as a JSON array:`;

    // Generate the Anki cards using Gemini
    let ankiCardsResponse = await geminiService.generateContent(prompt, {
      temperature: 0.3, // Lower temperature for more consistent, factual output
      maxOutputTokens: 4096 // Higher token limit for multiple cards
    });

    // Parse the JSON response
    let ankiCards;
    try {
      // Clean the response to extract JSON
      const jsonMatch = ankiCardsResponse.match(/\[[\s\S]*\]/);
      if (!jsonMatch) {
        throw new Error('No JSON array found in response');
      }

      ankiCards = JSON.parse(jsonMatch[0]);

      if (!Array.isArray(ankiCards)) {
        throw new Error('Response is not an array');
      }

      console.log(`Generated ${ankiCards.length} Anki cards`);

    } catch (parseError) {
      console.error('Error parsing Anki cards JSON:', parseError.message);
      console.log('Raw response:', ankiCardsResponse);

      // Fallback: create a simple card from the content
      ankiCards = [{
        type: 'qa',
        question: 'What is the main topic of this video?',
        answer: 'Based on the transcript content provided',
        timestamp: 0,
        difficulty: 'beginner',
        tags: ['general'],
        context: 'Video overview'
      }];
    }

    // Validate and clean the cards
    const validatedCards = ankiCards
      .filter(card => {
        // Ensure required fields exist and are valid
        const hasValidQuestion = card.question && typeof card.question === 'string' && card.question.trim().length > 0;
        const hasValidAnswer = card.answer && typeof card.answer === 'string' && card.answer.trim().length > 0;
        const hasValidTimestamp = typeof card.timestamp === 'number' &&
                                  card.timestamp >= 0 &&
                                  card.timestamp <= videoDurationSeconds;
        const hasValidType = ['qa', 'process', 'truefalse'].includes(card.type); // Removed 'cloze'

        if (!hasValidQuestion) {
          console.warn('Filtered out card with invalid question:', card.question);
        }
        if (!hasValidAnswer) {
          console.warn('Filtered out card with invalid answer:', card.answer);
        }
        if (!hasValidTimestamp) {
          console.warn('Filtered out card with invalid timestamp:', card.timestamp);
        }

        // Additional validation: check if content appears to be from transcript
        const hasTranscriptQuote = card.transcriptQuote && typeof card.transcriptQuote === 'string';
        const contentLooksValid = hasTranscriptQuote ||
          transcriptSegments.some(segment =>
            segment.text.toLowerCase().includes(card.answer.toLowerCase().substring(0, 20)) ||
            card.answer.toLowerCase().includes(segment.text.toLowerCase().substring(0, 20))
          );

        if (!contentLooksValid) {
          console.warn('Filtered out card with potentially hallucinated content:', card.question);
        }

        return hasValidQuestion && hasValidAnswer && hasValidTimestamp && hasValidType && contentLooksValid;
      })
      .map((card, index) => ({
        id: `card_${index + 1}`,
        type: card.type || 'qa',
        question: card.question.trim(),
        answer: card.answer.trim(),
        timestamp: Math.min(Math.max(0, card.timestamp), videoDurationSeconds), // Clamp timestamp
        difficulty: ['beginner', 'intermediate', 'advanced'].includes(card.difficulty) ? card.difficulty : 'intermediate',
        tags: Array.isArray(card.tags) ? card.tags.filter(tag => typeof tag === 'string' && tag.trim().length > 0) : ['general'],
        context: card.context && typeof card.context === 'string' ? card.context.trim() : '',
        transcriptQuote: card.transcriptQuote && typeof card.transcriptQuote === 'string' ? card.transcriptQuote.trim() : '',
        selected: true // Default to selected for export
      }));

    console.log(`Validated ${validatedCards.length} cards out of ${ankiCards.length} generated`);

    // Check if we have enough valid cards
    if (validatedCards.length === 0) {
      return res.status(400).json({
        error: 'No valid cards could be generated from the transcript',
        details: 'The AI was unable to create valid flashcards from this content. This may happen with very short videos or content that lacks educational material.'
      });
    }

    // Warn if we have significantly fewer cards than expected
    if (validatedCards.length < Math.max(2, targetCardCount * 0.5)) {
      console.warn(`Generated significantly fewer cards than expected: ${validatedCards.length} vs target ${targetCardCount}`);
    }

    const responseData = {
      success: true,
      videoId,
      language: transcriptData.language || lang,
      cards: validatedCards,
      metadata: {
        transcriptLength: transcriptData.transcript.length,
        videoDuration: videoDurationFormatted,
        videoDurationSeconds,
        targetCardCount,
        actualCardCount: validatedCards.length,
        validationRate: ankiCards.length > 0 ? Math.round((validatedCards.length / ankiCards.length) * 100) : 100,
        model: 'models/gemini-2.5-flash-lite-preview-06-17'
      }
    };

    // Cache the Anki cards if it's an example video
    if (exampleCacheService.isExampleVideo(videoId)) {
      await exampleCacheService.saveContentToCache(videoId, lang, 'anki-cards', responseData);
    }

    res.json(responseData);

  } catch (error) {
    console.error('Error generating Anki cards:', error.message);
    res.status(500).json({
      error: 'Failed to generate Anki cards',
      details: process.env.NODE_ENV === 'development' ? error.toString() : undefined
    });
  }
});

// Generate social media posts using Gemini
app.post('/api/social-media', async (req, res) => {
  try {
    const { videoId, coreMessage, lang = 'en' } = req.body;

    if (!videoId) {
      return res.status(400).json({ error: 'Video ID is required' });
    }

    console.log(`Generating social media posts for video ${videoId} in language ${lang}`);

    // Check if this is an example video and try cache first
    if (exampleCacheService.isExampleVideo(videoId)) {
      const cachedSocialMedia = await exampleCacheService.getCachedContent(videoId, lang, 'social-media');
      if (cachedSocialMedia) {
        console.log(`🚀 Fast cache hit for social-media ${videoId} in ${lang}`);
        return res.json(cachedSocialMedia);
      }
    }

    // First, get the transcript for the video
    const transcriptData = await transcriptService.getTranscript(videoId, lang, false);

    if (!transcriptData || !transcriptData.transcript || transcriptData.transcript.length === 0) {
      return res.status(404).json({ error: 'No transcript found for this video' });
    }

    // Extract the text content from the transcript
    const transcriptText = transcriptData.transcript
      .map(item => item.text)
      .join(' ');

    if (!transcriptText) {
      return res.status(400).json({ error: 'Transcript is empty' });
    }

    console.log(`Transcript length: ${transcriptText.length} characters`);

    // Create the social media prompt for Gemini
    const prompt = `### ROLE ###
You are an expert Social Media Manager and Content Strategist. You specialize in repurposing long-form content, like video transcripts, into engaging, high-performing social media posts that are natively optimized for each platform.

### TASK ###
Analyze the provided transcript and the core message. Based on this information, create three distinct social media posts: one for Twitter (X), one for Instagram, and one for LinkedIn. Each post must be uniquely crafted to fit the specific audience, tone, and best practices of its platform.

### PLATFORM-SPECIFIC GUIDELINES ###

#### 1. Twitter (X) Post ####
- **Tone:** Concise, punchy, and conversational. Use a strong hook to stop the scroll. Questions or bold statements work well.
- **Length:** Strictly adhere to the 280-character limit.
- **Content:** Extract one single, powerful quote or a surprising statistic from the transcript that can stand alone.
- **Hashtags:** Include 2-3 highly relevant hashtags at the end.
- **Formatting:** Use line breaks for readability. Emojis can be used to add personality.

#### 2. Instagram Post ####
- **Tone:** Inspirational, educational, or story-driven. More personal than Twitter.
- **Structure:**
    1.  **Hook:** Write a compelling first line to grab attention immediately.
    2.  **Caption:** Write a longer-form caption (3-5 paragraphs) that expands on a key story, lesson, or concept from the transcript. Use storytelling to connect with the audience emotionally or intellectually.
    3.  **Visual Idea:** **This is critical.** After the caption, describe an ideal visual to accompany the post (e.g., "Image Idea: A carousel post. Slide 1: Quote from the hook. Slide 2: A diagram explaining the core concept. Slide 3: A picture of the speaker.")
- **Formatting:** Use generous line breaks to create white space and improve readability. Emojis are encouraged to break up text and add visual flair.
- **Hashtags:** Include a block of 5-10 relevant hashtags at the very end for discoverability.
- **CTA:** End with a Call-to-Action that encourages engagement (e.g., "What are your thoughts? Comment below! 👇" or "Save this post for later!").

#### 3. LinkedIn Post ####
- **Tone:** Professional, authoritative, and insightful. Aim to start a conversation among industry peers.
- **Structure:**
    1.  **Hook:** Start with a professional hook that targets a specific pain point, offers a key insight, or presents a forward-looking statement from the transcript.
    2.  **Body:** Elaborate on the hook with 2-3 key points from the transcript. Use bullet points or numbered lists to present information clearly and make the post skimmable. Provide value and showcase expertise.
    3.  **CTA:** End with a thought-provoking question for your professional network to encourage meaningful comments and discussion.
- **Formatting:** Use professional emojis sparingly (e.g., 💡, 🚀, ✅, 📈). Use line breaks to structure the post for easy reading.
- **Hashtags:** Include 3-5 professional, niche-specific hashtags at the end (e.g., #Leadership, #DigitalTransformation, #ProjectManagement).

### OVERALL REQUIREMENTS ###
- **Source Material:** All core ideas, quotes, and data points must be derived directly from the provided transcript. Do not invent information.
- **No Filler:** Do not include introductions like "Here are your posts." Output only the formatted content for the three platforms.

### OUTPUT FORMAT ###
---

**🐦 TWITTER (X) POST**

[Your concise, punchy tweet here, under 280 characters.]

#hashtag1 #hashtag2

---

**📱 INSTAGRAM POST**

[Your compelling hook goes here.]

[Your longer, story-driven caption goes here, with plenty of line breaks for readability.]

[Continue caption...]

[Your engagement-focused call-to-action here.]

***
*Image Idea: [Your detailed description of the ideal visual for this post.]*

#hashtag1 #hashtag2 #hashtag3 #hashtag4 #hashtag5 #hashtag6

---

**💼 LINKEDIN POST**

[Your professional, insightful hook goes here.]

Here are the key takeaways:

• [Bulleted point 1 from the transcript.]
• [Bulleted point 2 from the transcript.]
• [Bulleted point 3 from the transcript.]

[Your thought-provoking question for your network goes here.]

#ProfessionalHashtag1 #ProfessionalHashtag2 #ProfessionalHashtag3

---

### USER-PROVIDED INFORMATION ###

**Core Message of the Video:** ${coreMessage || 'Analyze the transcript and determine the main message'}

**Video Transcript:**
${transcriptText}`;

    // Generate the social media posts using Gemini
    const socialMediaPosts = await geminiService.generateContent(prompt, {
      temperature: 0.7,
      maxOutputTokens: 2048
    });

    const responseData = {
      success: true,
      videoId,
      language: transcriptData.language || lang,
      socialMediaPosts,
      transcriptLength: transcriptData.transcript.length,
      coreMessage: coreMessage || 'Auto-analyzed from transcript',
      model: 'models/gemini-2.5-flash-lite-preview-06-17'
    };

    // Cache the social media posts if it's an example video
    if (exampleCacheService.isExampleVideo(videoId)) {
      await exampleCacheService.saveContentToCache(videoId, lang, 'social-media', responseData);
    }

    res.json(responseData);

  } catch (error) {
    console.error('Error generating social media posts:', error.message);
    res.status(500).json({
      error: 'Failed to generate social media posts',
      details: process.env.NODE_ENV === 'development' ? error.toString() : undefined
    });
  }
});

// Root path handler - serve index.html for the frontend
app.get('/', (req, res) => {
  if (frontendExists) {
    res.sendFile(path.join(frontendBuildPath, 'index.html'));
  } else {
    res.json({
      message: 'YouTube Transcript Generator API',
      endpoints: [
        '/api/health',
        '/api/video/:videoId',
        '/api/captions/:videoId',
        '/api/transcript/:videoId',
        '/api/captions/:videoId/download',
        '/api/summary/:videoId',
        '/api/quotes/:videoId',
        '/api/description (POST)',
        '/api/anki-cards (POST)',
        '/api/social-media (POST)',
        '/api/gemini/test',
        '/api/gemini/generate',
        '/api/gemini/info'
      ]
    });
  }
});

// Simple 404 handler for API routes
app.get('/api/*', (req, res) => {
  res.status(404).json({ error: 'API endpoint not found' });
});

// For all other routes, serve the frontend index.html if it exists
app.get('*', (req, res) => {
  if (frontendExists) {
    res.sendFile(path.join(frontendBuildPath, 'index.html'));
  } else {
    res.status(404).json({ error: 'Not found' });
  }
});

// Initialize example video cache service
async function initializeServices() {
  try {
    await exampleCacheService.initializeCacheDirectories();
    console.log('✅ Example video cache service initialized');
  } catch (error) {
    console.error('⚠️ Error initializing cache service:', error.message);
  }
}

// Start the server
app.listen(PORT, async () => {
  console.log(`Server running on port ${PORT}`);
  console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`Frontend build exists: ${frontendExists}`);
  if (frontendExists) {
    console.log(`Frontend served from: ${frontendBuildPath}`);
  }

  // Initialize services
  await initializeServices();

  console.log('🚀 Server ready and cache service initialized!');
});
