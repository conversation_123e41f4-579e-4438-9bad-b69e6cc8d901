/**
 * Comprehensive test for German transcript processing
 * 
 * This script tests the German transcript processing for a specific video
 * and provides detailed analysis of the issues.
 */

const fs = require('fs').promises;
const path = require('path');
const transcriptService = require('./services/transcriptService');
const nonEnglishProcessor = require('./services/nonEnglishTranscriptProcessor');
const { exec } = require('child_process');
const util = require('util');

// Convert exec to Promise-based
const execPromise = util.promisify(exec);

// Test video ID
const VIDEO_ID = 'h9R6ZeNruuI'; // New German video
const LANGUAGE = 'de'; // German

// Function to fetch raw transcript using yt-dlp
async function fetchRawTranscript(videoId, lang) {
  const tempDir = path.join(__dirname, 'temp');
  
  try {
    // Create temp directory if it doesn't exist
    await fs.mkdir(tempDir, { recursive: true });
    
    console.log(`Fetching raw ${lang} transcript for ${videoId} using yt-dlp...`);
    const command = `yt-dlp --write-auto-sub --sub-lang ${lang} --skip-download https://www.youtube.com/watch?v=${videoId} -o "${tempDir}/%(id)s.%(ext)s"`;
    
    const { stdout } = await execPromise(command);
    console.log(stdout);
    
    // Find the subtitle file
    const files = await fs.readdir(tempDir);
    const subtitleFile = files.find(file => 
      file.startsWith(videoId) && 
      file.includes(`.${lang}.`) && 
      file.endsWith('.vtt')
    );
    
    if (!subtitleFile) {
      console.error(`No ${lang} subtitle file found`);
      return null;
    }
    
    console.log(`Found subtitle file: ${subtitleFile}`);
    
    // Read the subtitle file
    const subtitlePath = path.join(tempDir, subtitleFile);
    const subtitleContent = await fs.readFile(subtitlePath, 'utf8');
    
    // Save the raw subtitle content for analysis
    const rawOutputPath = path.join(__dirname, `${videoId}-${lang}-raw.vtt`);
    await fs.writeFile(rawOutputPath, subtitleContent);
    console.log(`Raw ${lang} transcript saved to ${rawOutputPath}`);
    
    return subtitleContent;
  } catch (error) {
    console.error('Error fetching raw transcript:', error);
    return null;
  }
}

// Function to parse VTT content
function parseVttContent(vttContent) {
  console.log('Parsing VTT content...');
  
  // Split the content into blocks (each subtitle entry)
  const blocks = vttContent.trim().split(/\n\s*\n/);
  const transcriptItems = [];
  
  console.log(`Found ${blocks.length} blocks in VTT file`);
  
  for (let i = 0; i < blocks.length; i++) {
    const block = blocks[i].trim();
    
    // Skip the WEBVTT header block
    if (block === 'WEBVTT' || block.startsWith('WEBVTT ') || 
        block.startsWith('Kind:') || block.startsWith('Language:')) {
      continue;
    }
    
    // Split the block into lines
    const lines = block.split('\n');
    
    // Find the line with the timestamp
    const timestampLineIndex = lines.findIndex(line => line.includes('-->'));
    
    if (timestampLineIndex === -1) {
      continue; // Skip blocks without timestamps
    }
    
    // Extract the timestamp
    const timestampLine = lines[timestampLineIndex];
    const times = timestampLine.split('-->').map(t => t.trim());
    
    // Parse the start time
    const startTime = parseVttTimestamp(times[0]);
    
    // Parse the end time, removing any alignment info
    const endTimePart = times[1].split(' ')[0];
    const endTime = parseVttTimestamp(endTimePart);
    
    // Extract the text (all lines after the timestamp line)
    const textLines = lines.slice(timestampLineIndex + 1);
    
    // Skip empty text blocks
    if (textLines.length === 0) {
      continue;
    }
    
    // Join all text lines with a space
    let text = textLines.join(' ');
    
    // Skip empty text after joining
    if (!text.trim()) {
      continue;
    }
    
    // Add to transcript items
    transcriptItems.push({
      start: startTime,
      end: endTime,
      text: text
    });
  }
  
  // Sort by start time to ensure proper sequence
  transcriptItems.sort((a, b) => a.start - b.start);
  
  console.log(`Parsed ${transcriptItems.length} transcript items from VTT file`);
  
  return transcriptItems;
}

// Function to parse VTT timestamp to seconds
function parseVttTimestamp(timestamp) {
  // Handle both HH:MM:SS.mmm and MM:SS.mmm formats
  const parts = timestamp.split(':');
  let hours = 0, minutes = 0, seconds = 0;

  if (parts.length === 3) {
    hours = parseInt(parts[0]);
    minutes = parseInt(parts[1]);
    seconds = parseFloat(parts[2]);
  } else if (parts.length === 2) {
    minutes = parseInt(parts[0]);
    seconds = parseFloat(parts[1]);
  }

  return hours * 3600 + minutes * 60 + seconds;
}

// Function to format time in MM:SS format
function formatTime(seconds) {
  if (typeof seconds !== 'number' || isNaN(seconds)) {
    seconds = 0;
  }
  
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  
  if (hours > 0) {
    return [
      hours.toString().padStart(2, '0'),
      minutes.toString().padStart(2, '0'),
      secs.toString().padStart(2, '0')
    ].join(':');
  } else {
    return [
      minutes.toString().padStart(2, '0'),
      secs.toString().padStart(2, '0')
    ].join(':');
  }
}

// Function to analyze transcript for issues
function analyzeTranscript(transcriptItems) {
  console.log('\nANALYZING TRANSCRIPT FOR ISSUES:');
  
  // Check for timestamp tags in text
  const timestampTagsCount = transcriptItems.filter(item => 
    item.text.includes('<') && item.text.includes('>') && 
    /\d+:\d+/.test(item.text)
  ).length;
  
  console.log(`\n1. Timestamp tags in text: ${timestampTagsCount > 0 ? 'YES' : 'NO'}`);
  if (timestampTagsCount > 0) {
    console.log('   Examples:');
    transcriptItems.filter(item => 
      item.text.includes('<') && item.text.includes('>') && 
      /\d+:\d+/.test(item.text)
    ).slice(0, 3).forEach(item => {
      console.log(`   - "${item.text}"`);
    });
  }
  
  // Check for duplicate text segments
  const textMap = new Map();
  const duplicateTexts = [];
  
  transcriptItems.forEach(item => {
    if (textMap.has(item.text)) {
      duplicateTexts.push({
        text: item.text,
        occurrences: textMap.get(item.text) + 1
      });
      textMap.set(item.text, textMap.get(item.text) + 1);
    } else {
      textMap.set(item.text, 1);
    }
  });
  
  const duplicateCount = duplicateTexts.length;
  console.log(`\n2. Duplicate text segments: ${duplicateCount > 0 ? 'YES' : 'NO'}`);
  if (duplicateCount > 0) {
    console.log('   Examples:');
    duplicateTexts.slice(0, 3).forEach(dup => {
      console.log(`   - "${dup.text}" (${dup.occurrences} occurrences)`);
    });
  }
  
  // Check for punctuation issues
  const punctuationIssuesCount = transcriptItems.filter(item => 
    item.text.match(/([.!?,;:])([^\s])/) || // No space after punctuation
    item.text.match(/^\s*[.!?,;:]/) // Starts with punctuation
  ).length;
  
  console.log(`\n3. Punctuation issues: ${punctuationIssuesCount > 0 ? 'YES' : 'NO'}`);
  if (punctuationIssuesCount > 0) {
    console.log('   Examples:');
    transcriptItems.filter(item => 
      item.text.match(/([.!?,;:])([^\s])/) || 
      item.text.match(/^\s*[.!?,;:]/)
    ).slice(0, 3).forEach(item => {
      console.log(`   - "${item.text}"`);
    });
  }
  
  // Check for very short segments (less than 0.5 seconds)
  const shortSegments = transcriptItems.filter(item => 
    (item.end - item.start) < 0.5
  ).length;
  
  console.log(`\n4. Very short segments (<0.5s): ${shortSegments > 0 ? 'YES' : 'NO'}`);
  if (shortSegments > 0) {
    console.log('   Examples:');
    transcriptItems.filter(item => 
      (item.end - item.start) < 0.5
    ).slice(0, 3).forEach(item => {
      console.log(`   - "${item.text}" (${(item.end - item.start).toFixed(2)}s)`);
    });
  }
  
  // Check for overlapping segments
  let overlappingSegments = 0;
  for (let i = 0; i < transcriptItems.length - 1; i++) {
    if (transcriptItems[i].end > transcriptItems[i + 1].start) {
      overlappingSegments++;
    }
  }
  
  console.log(`\n5. Overlapping segments: ${overlappingSegments > 0 ? 'YES' : 'NO'}`);
  if (overlappingSegments > 0) {
    console.log('   Examples:');
    for (let i = 0, count = 0; i < transcriptItems.length - 1 && count < 3; i++) {
      if (transcriptItems[i].end > transcriptItems[i + 1].start) {
        console.log(`   - "${transcriptItems[i].text}" (${formatTime(transcriptItems[i].start)}-${formatTime(transcriptItems[i].end)}) overlaps with`);
        console.log(`     "${transcriptItems[i+1].text}" (${formatTime(transcriptItems[i+1].start)}-${formatTime(transcriptItems[i+1].end)})`);
        count++;
      }
    }
  }
}

// Main test function
async function testGermanTranscript() {
  try {
    console.log(`Testing German transcript processing for video ID: ${VIDEO_ID}\n`);
    
    // Step 1: Fetch the raw transcript
    const rawVttContent = await fetchRawTranscript(VIDEO_ID, LANGUAGE);
    if (!rawVttContent) {
      console.error('Failed to fetch raw transcript');
      return;
    }
    
    // Step 2: Parse the raw VTT content
    const rawTranscriptItems = parseVttContent(rawVttContent);
    
    // Save the raw parsed items for analysis
    await fs.writeFile(
      path.join(__dirname, `${VIDEO_ID}-${LANGUAGE}-raw-parsed.json`),
      JSON.stringify(rawTranscriptItems, null, 2)
    );
    
    // Step 3: Process the transcript using our service
    console.log('\nProcessing transcript with transcriptService...');
    const processedTranscript = await transcriptService.getTranscript(VIDEO_ID, LANGUAGE, true);
    
    // Save the processed transcript for analysis
    await fs.writeFile(
      path.join(__dirname, `${VIDEO_ID}-${LANGUAGE}-processed.json`),
      JSON.stringify(processedTranscript, null, 2)
    );
    
    // Step 4: Analyze the raw transcript
    console.log('\nRAW TRANSCRIPT ANALYSIS:');
    analyzeTranscript(rawTranscriptItems);
    
    // Step 5: Analyze the processed transcript
    console.log('\nPROCESSED TRANSCRIPT ANALYSIS:');
    analyzeTranscript(processedTranscript.transcript);
    
    // Step 6: Compare sample segments
    console.log('\nCOMPARING SAMPLE SEGMENTS (RAW vs PROCESSED):');
    for (let i = 0; i < Math.min(10, rawTranscriptItems.length); i++) {
      console.log(`\nSegment ${i+1}:`);
      console.log(`RAW: [${formatTime(rawTranscriptItems[i].start)}] "${rawTranscriptItems[i].text}"`);
      
      if (i < processedTranscript.transcript.length) {
        console.log(`PROCESSED: [${processedTranscript.transcript[i].formattedStart}] "${processedTranscript.transcript[i].text}"`);
      } else {
        console.log('PROCESSED: [missing]');
      }
    }
    
    console.log('\nTest completed successfully!');
    
  } catch (error) {
    console.error('Error in test:', error);
  }
}

// Run the test
testGermanTranscript().catch(console.error);
