const axios = require('axios');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

const BASE_URL = 'http://localhost:3000';

async function testAnkiCardsAPI() {
  console.log('🧪 Testing Anki Flash Cards API Integration...\n');

  try {
    // Test with an example video (Dunning-Kruger Effect)
    const testVideoId = 'TT81fe2IobI';
    const testLanguage = 'en';

    console.log(`Testing video: ${testVideoId} (Dunning-Krug<PERSON> Effect)`);
    console.log(`Language: ${testLanguage}\n`);

    console.log('Testing Anki Flash Cards Generation...');
    console.log('   Creating educational flashcards from video transcript');
    console.log('   This may take a moment...\n');

    const requestBody = {
      videoId: testVideoId,
      lang: testLanguage
    };

    const startTime = Date.now();
    const response = await axios.post(`${BASE_URL}/api/anki-cards`, requestBody);
    const endTime = Date.now();

    if (response.data.success) {
      console.log('✅ Anki flash cards generated successfully!');
      console.log(`   Generation time: ${(endTime - startTime) / 1000}s`);
      console.log(`   Number of cards: ${response.data.cards.length}`);
      console.log(`   Video duration: ${response.data.metadata.videoDuration}`);
      console.log(`   Target count: ${response.data.metadata.targetCardCount}`);
      console.log(`   Actual count: ${response.data.metadata.actualCardCount}`);

      // Analyze card distribution
      const cardTypes = {};
      const difficulties = {};
      
      response.data.cards.forEach(card => {
        cardTypes[card.type] = (cardTypes[card.type] || 0) + 1;
        difficulties[card.difficulty] = (difficulties[card.difficulty] || 0) + 1;
      });

      console.log('\n📊 Card Distribution:');
      console.log('   Types:', cardTypes);
      console.log('   Difficulties:', difficulties);

      console.log('\n📝 Sample Cards:');
      console.log('─'.repeat(80));
      
      // Show first 3 cards as examples
      response.data.cards.slice(0, 3).forEach((card, index) => {
        console.log(`\n${index + 1}. [${card.type.toUpperCase()}] ${card.difficulty}`);
        console.log(`   Q: ${card.question}`);
        console.log(`   A: ${card.answer}`);
        console.log(`   ⏰ ${Math.floor(card.timestamp / 60)}:${(card.timestamp % 60).toString().padStart(2, '0')}`);
        console.log(`   🏷️ ${card.tags.join(', ')}`);
        if (card.context) {
          console.log(`   📝 ${card.context}`);
        }
      });
      
      console.log('─'.repeat(80));

      // Validate card structure
      const validCards = response.data.cards.filter(card => 
        card.question && card.answer && 
        typeof card.timestamp === 'number' &&
        card.type && card.difficulty
      );

      console.log(`\n✅ Validation: ${validCards.length}/${response.data.cards.length} cards are properly formatted`);

      if (validCards.length === response.data.cards.length) {
        console.log('🎉 All cards passed validation!');
      } else {
        console.log('⚠️ Some cards may have formatting issues');
      }

    } else {
      console.log('❌ Anki cards generation failed');
      console.log('   Response:', response.data);
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    
    if (error.response) {
      console.log('   Status:', error.response.status);
      console.log('   Data:', error.response.data);
    }
  }
}

// Run the test
testAnkiCardsAPI().then(() => {
  console.log('\n🏁 Test completed');
});
