const axios = require('axios');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

const BASE_URL = 'http://localhost:3000';

async function testCompleteIntegration() {
  console.log('🧪 Testing Complete YouTube Transcript Generator Integration...\n');

  try {
    // Test 1: Server Health
    console.log('1. Testing server health...');
    const healthResponse = await axios.get(`${BASE_URL}/api/health`);
    console.log('✅ Server is running:', healthResponse.data.message);

    // Test 2: Gemini API Status
    console.log('\n2. Testing Gemini API status...');
    const geminiInfoResponse = await axios.get(`${BASE_URL}/api/gemini/info`);
    console.log('✅ Gemini API configured:');
    console.log('   Model:', geminiInfoResponse.data.model);
    console.log('   API Key Configured:', geminiInfoResponse.data.apiKeyConfigured);

    // Test 3: Example Video Transcript
    const testVideoId = 'TT81fe2IobI'; // Dunning-Kruger Effect video
    console.log(`\n3. Testing transcript fetch for video: ${testVideoId}...`);
    const transcriptResponse = await axios.get(`${BASE_URL}/api/transcript/${testVideoId}?lang=en`);
    console.log('✅ Transcript fetched successfully:');
    console.log('   Segments:', transcriptResponse.data.transcript.length);
    console.log('   Language:', transcriptResponse.data.language);

    // Test 4: Summary Generation
    console.log(`\n4. Testing summary generation for video: ${testVideoId}...`);
    console.log('   This may take a moment...');
    const startTime = Date.now();
    const summaryResponse = await axios.get(`${BASE_URL}/api/summary/${testVideoId}?lang=en`);
    const endTime = Date.now();
    
    console.log('✅ Summary generated successfully:');
    console.log('   Generation time:', `${(endTime - startTime) / 1000}s`);
    console.log('   Summary length:', summaryResponse.data.summary.length, 'characters');
    console.log('   Model used:', summaryResponse.data.model);

    // Test 5: Video Information
    console.log(`\n5. Testing video information fetch for: ${testVideoId}...`);
    const videoInfoResponse = await axios.get(`${BASE_URL}/api/video/${testVideoId}`);
    console.log('✅ Video information fetched:');
    console.log('   Title:', videoInfoResponse.data.snippet.title);
    console.log('   Channel:', videoInfoResponse.data.snippet.channelTitle);

    // Test 6: Available Captions
    console.log(`\n6. Testing available captions for: ${testVideoId}...`);
    const captionsResponse = await axios.get(`${BASE_URL}/api/captions/${testVideoId}`);
    console.log('✅ Available captions fetched:');
    console.log('   Languages available:', captionsResponse.data.length);

    console.log('\n🎉 All integration tests passed successfully!');
    console.log('\n📋 Summary of Features:');
    console.log('   ✅ YouTube API integration');
    console.log('   ✅ Transcript fetching with multiple languages');
    console.log('   ✅ Google Gemini 2.5 Flash Lite integration');
    console.log('   ✅ Automatic AI summary generation');
    console.log('   ✅ Video information and captions');
    console.log('\n🚀 Your YouTube Transcript Generator is fully operational!');
    console.log('\nNext steps:');
    console.log('   1. Open http://localhost:5173/ in your browser');
    console.log('   2. Click on an example video or add your own');
    console.log('   3. Watch the Summary tab automatically generate AI summaries');

  } catch (error) {
    console.error('❌ Integration test failed:', error.message);
    
    if (error.response) {
      console.error('   Status:', error.response.status);
      console.error('   Error Details:', error.response.data);
    } else if (error.code === 'ECONNREFUSED') {
      console.error('   Make sure both servers are running:');
      console.error('   Backend: cd backend && npm start');
      console.error('   Frontend: cd frontend/youtube-transcribe && npm run dev');
    }
    
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  testCompleteIntegration();
}

module.exports = testCompleteIntegration;
