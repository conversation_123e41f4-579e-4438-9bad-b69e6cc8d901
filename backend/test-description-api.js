const axios = require('axios');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

const BASE_URL = 'http://localhost:3000';

async function testDescriptionAPI() {
  console.log('🧪 Testing YouTube Description Generation Feature...\n');

  try {
    // Test with an example video (Dunning-Kruger Effect)
    const testVideoId = 'TT81fe2IobI';
    const testLanguage = 'en';

    console.log(`Testing video: ${testVideoId} (Dunning-Kruger Effect)`);
    console.log(`Language: ${testLanguage}\n`);

    console.log('Testing YouTube Description Generation...');
    console.log('   Creating SEO-optimized description with timestamps');
    console.log('   This may take a moment...\n');

    const requestBody = {
      videoId: testVideoId,
      videoTitle: 'Why Stupid People Think They\'re Smart - The Dunning-Kruger Effect',
      keywords: 'dunning kruger effect, psychology, cognitive bias',
      ctaGoal: 'Subscribe for more psychology content',
      lang: testLanguage
    };

    const startTime = Date.now();
    const response = await axios.post(`${BASE_URL}/api/description`, requestBody);
    const endTime = Date.now();

    if (response.data.success) {
      console.log('✅ YouTube Description generated successfully!');
      console.log(`   Generation time: ${(endTime - startTime) / 1000}s`);
      console.log(`   Description length: ${response.data.description.length} characters`);
      console.log(`   Video Title: ${response.data.videoTitle}`);
      console.log(`   Keywords: ${response.data.keywords}`);
      console.log(`   CTA Goal: ${response.data.ctaGoal}`);
      console.log('\n📝 Generated YouTube Description:');
      console.log('─'.repeat(80));
      console.log(response.data.description);
      console.log('─'.repeat(80));

      // Analyze the output
      const descriptionText = response.data.description;
      const hasTimestamps = descriptionText.includes('⏰ **TIMESTAMPS**');
      const timestampMatches = descriptionText.match(/\d{2}:\d{2}/g);
      const timestampCount = timestampMatches ? timestampMatches.length : 0;
      const hasHashtags = descriptionText.includes('#');
      
      console.log(`\n📊 Analysis:`);
      console.log(`   Contains timestamps section: ${hasTimestamps ? '✅' : '❌'}`);
      console.log(`   Number of timestamps: ${timestampCount}`);
      console.log(`   Contains hashtags: ${hasHashtags ? '✅' : '❌'}`);
      console.log(`   Character count: ${descriptionText.length}`);

    } else {
      console.log('❌ Description generation failed');
      console.log('   Response:', response.data);
    }

    console.log('\n🎉 YouTube Description feature tested successfully!');
    console.log('\n📋 Summary of Features:');
    console.log('   ✅ SEO-optimized conversational description');
    console.log('   ✅ Automatic timestamp generation from transcript');
    console.log('   ✅ Keyword integration');
    console.log('   ✅ Custom call-to-action');
    console.log('   ✅ Hashtag generation');
    console.log('\n🚀 Your YouTube Description generator is ready!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    
    if (error.response) {
      console.error('   Status:', error.response.status);
      console.error('   Error Details:', error.response.data);
    } else if (error.code === 'ECONNREFUSED') {
      console.error('   Make sure the backend server is running:');
      console.error('   Backend: cd backend && npm start');
    }
    
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  testDescriptionAPI();
}

module.exports = testDescriptionAPI;
