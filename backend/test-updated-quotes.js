const axios = require('axios');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

const BASE_URL = 'http://localhost:3000';

async function testUpdatedQuotes() {
  console.log('🧪 Testing Updated Quotes Feature...\n');

  try {
    // Test with an example video (Dunning-Kruger Effect)
    const testVideoId = 'TT81fe2IobI';
    const testLanguage = 'en';

    console.log(`Testing video: ${testVideoId} (Dunning-Kruger Effect)`);
    console.log(`Language: ${testLanguage}\n`);

    console.log('Testing UPDATED Quotes Generation...');
    console.log('   New requirements: At least 2 quotes per category, no speaker attribution');
    console.log('   This may take a moment...\n');

    const startTime = Date.now();
    const quotesResponse = await axios.get(`${BASE_URL}/api/quotes/${testVideoId}?lang=${testLanguage}`);
    const endTime = Date.now();

    if (quotesResponse.data.success) {
      console.log('✅ Updated Quotes generated successfully!');
      console.log(`   Generation time: ${(endTime - startTime) / 1000}s`);
      console.log(`   Quotes length: ${quotesResponse.data.quotes.length} characters`);
      console.log('\n💬 Generated Quotes (Updated Format):');
      console.log('─'.repeat(80));
      console.log(quotesResponse.data.quotes);
      console.log('─'.repeat(80));

      // Analyze the output
      const quotesText = quotesResponse.data.quotes;
      const categories = quotesText.split('**').filter(line => line.includes('.'));
      console.log(`\n📊 Analysis:`);
      console.log(`   Categories found: ${categories.length}`);
      
      // Count quotes per category
      const blockquotes = quotesText.match(/^>/gm);
      const totalQuotes = blockquotes ? blockquotes.length : 0;
      console.log(`   Total quotes extracted: ${totalQuotes}`);
      console.log(`   Average quotes per category: ${(totalQuotes / 7).toFixed(1)}`);
      
      // Check for speaker attribution (should be none)
      const hasAttribution = quotesText.includes('—') || quotesText.includes('Speaker');
      console.log(`   Speaker attribution removed: ${!hasAttribution ? '✅' : '❌'}`);

    } else {
      console.log('❌ Quotes generation failed');
      console.log('   Response:', quotesResponse.data);
    }

    console.log('\n🎉 Updated quotes feature tested successfully!');
    console.log('\n📋 Summary of Updates:');
    console.log('   ✅ At least 2 quotes per category requirement');
    console.log('   ✅ No speaker attribution (clean quotes only)');
    console.log('   ✅ Verbatim extraction maintained');
    console.log('   ✅ 7 categories of quotes');
    console.log('\n🚀 Your Quotes feature is now enhanced!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    
    if (error.response) {
      console.error('   Status:', error.response.status);
      console.error('   Error Details:', error.response.data);
    } else if (error.code === 'ECONNREFUSED') {
      console.error('   Make sure the backend server is running:');
      console.error('   Backend: cd backend && npm start');
    }
    
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  testUpdatedQuotes();
}

module.exports = testUpdatedQuotes;
