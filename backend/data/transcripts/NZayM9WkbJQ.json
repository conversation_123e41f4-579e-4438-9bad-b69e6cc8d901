{"videoId": "NZayM9WkbJQ", "language": "en", "transcript": [{"id": 1, "start": 31.12, "end": 37.92, "formattedStart": "00:31", "formattedEnd": "00:37", "text": "And there we're having the two new flavors \nfrom Bread Bull this uh season it's the pink  "}, {"id": 2, "start": 37.92, "end": 49.68, "formattedStart": "00:37", "formattedEnd": "00:49", "text": "One this must be the summer edition raspberry \nflavors and some uh skills for the times when  "}, {"id": 3, "start": 49.68, "end": 60.64, "formattedStart": "00:49", "formattedEnd": "01:00", "text": "I'm flying and don't want to stay on my \nphone i can show off with some yo-yo yo-yo"}, {"id": 4, "start": 60.64, "end": 71.04, "formattedStart": "01:00", "formattedEnd": "01:11", "text": "I'm good and then you have the spring edition"}, {"id": 5, "start": 71.04, "end": 77.12, "formattedStart": "01:11", "formattedEnd": "01:17", "text": "I will try them tomorrow morning it's a bit \nlate now to do it just before bedtime now it's  "}, {"id": 6, "start": 77.12, "end": 88.24000000000001, "formattedStart": "01:17", "formattedEnd": "01:28", "text": "Uh 8:20 in the evening so it's probably Yeah \nit's sun for another hour hi everyone here  "}, {"id": 7, "start": 88.24, "end": 96.24, "formattedStart": "01:28", "formattedEnd": "01:36", "text": "We are back in Bergen wait of course \na cowboy hat after our man text us  "}, {"id": 8, "start": 96.24, "end": 124.32, "formattedStart": "01:36", "formattedEnd": "02:04", "text": "And uh this video will be a little recap \nfrom the last weekend uh and how my race went"}, {"id": 9, "start": 124.32, "end": 131.6, "formattedStart": "02:04", "formattedEnd": "02:11", "text": "Long story short won the race but uh uh to go \nthrough like a little bit more into the details of  "}, {"id": 10, "start": 131.6, "end": 141.2, "formattedStart": "02:11", "formattedEnd": "02:21", "text": "Uh the race day uh I guess uh the crazy thing with \nit all were that uh even 3 minutes before my alarm  "}, {"id": 11, "start": 141.2, "end": 150.64, "formattedStart": "02:21", "formattedEnd": "02:30", "text": "Went off 4:00 I woke up by myself 3:57 and I felt \nuh quite uh rested already in the morning which  "}, {"id": 12, "start": 150.64, "end": 158, "formattedStart": "02:30", "formattedEnd": "02:38", "text": "Is a pretty good sign I guess and uh it's not too \neasy to feel uh uh that you've got enough sleep  "}, {"id": 13, "start": 158, "end": 166.56, "formattedStart": "02:38", "formattedEnd": "02:46", "text": "Before a race that is starting that early but I \nwas feeling ready to go and um yeah well prepared  "}, {"id": 14, "start": 166.56, "end": 173.52, "formattedStart": "02:46", "formattedEnd": "02:53", "text": "Went over to the race venue and it was still \npretty dark in the morning uh I think the sun  "}, {"id": 15, "start": 173.52, "end": 183.84, "formattedStart": "02:53", "formattedEnd": "03:03", "text": "Was rising like 6:45 or something in the morning \nand the race gun went off 6:25 which mean uh the  "}, {"id": 16, "start": 183.84, "end": 192.16, "formattedStart": "03:03", "formattedEnd": "03:12", "text": "First 20 minutes of the swim was in pretty dark \nconditions and a mistake I did were to only travel  "}, {"id": 17, "start": 192.16, "end": 199.92, "formattedStart": "03:12", "formattedEnd": "03:19", "text": "With dark goggles so yeah it's working great when \nyou're swimming in outdoor pool with the sun above  "}, {"id": 18, "start": 199.92, "end": 205.11999999999998, "formattedStart": "03:19", "formattedEnd": "03:25", "text": "You but in the morning when it's already dark \nit's pretty difficult to all to see on top of  "}, {"id": 19, "start": 205.12, "end": 211.04, "formattedStart": "03:25", "formattedEnd": "03:31", "text": "That so that was one thing that uh I tried to sort \nout like the day before in the expo but they were  "}, {"id": 20, "start": 211.04, "end": 217.2, "formattedStart": "03:31", "formattedEnd": "03:37", "text": "Unfortunately sold out of clear goggles so I was \njust hoping for the best hope that I would be able  "}, {"id": 21, "start": 217.2, "end": 225.11999999999998, "formattedStart": "03:37", "formattedEnd": "03:45", "text": "To see where I was swimming in the group and uh it \nwent uh relatively fine i would say the first K in  "}, {"id": 22, "start": 225.12, "end": 232.56, "formattedStart": "03:45", "formattedEnd": "03:52", "text": "The dark before the sun sort of came up and I was \nable to sort of see where I was in the field and  "}, {"id": 23, "start": 232.56, "end": 240.72, "formattedStart": "03:52", "formattedEnd": "04:00", "text": "Uh I guess the thing is when it's like a 75 60 \nguys big field that's starting together it's  "}, {"id": 24, "start": 240.72, "end": 246.64, "formattedStart": "04:00", "formattedEnd": "04:06", "text": "Like tend to be a little bit bigger groups as well \nand I think we were maybe a group of 15 or 20 guys  "}, {"id": 25, "start": 246.64, "end": 255.6, "formattedStart": "04:06", "formattedEnd": "04:15", "text": "In was like swimming comfortably together and uh I \nfelt that the pace was quite comfortable and quite  "}, {"id": 26, "start": 255.6, "end": 263.2, "formattedStart": "04:15", "formattedEnd": "04:23", "text": "Pleased with that compared to how I was swimming \nin Oceanside because uh that was maybe what I  "}, {"id": 27, "start": 263.2, "end": 268.4, "formattedStart": "04:23", "formattedEnd": "04:28", "text": "Was the least happy with in the race before was \nthat I was struggling way more in the water while  "}, {"id": 28, "start": 268.4, "end": 276.56, "formattedStart": "04:28", "formattedEnd": "04:36", "text": "Um now in Texas i felt that I was actually able to \nrelax and uh swim comfortably and I saw that I had  "}, {"id": 29, "start": 276.56, "end": 284.48, "formattedStart": "04:36", "formattedEnd": "04:44", "text": "Like the front the first swimmers maybe two body \nlengths ahead so I was able to even just settle  "}, {"id": 30, "start": 284.48, "end": 291.92, "formattedStart": "04:44", "formattedEnd": "04:51", "text": "Into a rhythm and wait for transition uh one thing \nin that I was changing my mind in the last moment  "}, {"id": 31, "start": 291.92, "end": 300.72, "formattedStart": "04:51", "formattedEnd": "05:00", "text": "The day before were to decide to put on the calf \nsleeves in uh uh T1 and also put on uh the socks  "}, {"id": 32, "start": 300.72, "end": 307.28000000000003, "formattedStart": "05:00", "formattedEnd": "05:07", "text": "In T1 already and uh uh hoping that I wouldn't \nlose too much time in transition and still be able  "}, {"id": 33, "start": 307.28, "end": 314.32, "formattedStart": "05:07", "formattedEnd": "05:14", "text": "To be sort of in contact with the front a little \nbit the same as I did in Frankfurt last year and  "}, {"id": 34, "start": 314.32, "end": 322.15999999999997, "formattedStart": "05:14", "formattedEnd": "05:22", "text": "Uh had a pretty good transition I would say uh I \nthink I maybe lost 20 seconds or something to the  "}, {"id": 35, "start": 322.16, "end": 328.24, "formattedStart": "05:22", "formattedEnd": "05:28", "text": "Quick guys in transition so the bike course were \nmostly on the highway but it was like I think it  "}, {"id": 36, "start": 328.24, "end": 335.76, "formattedStart": "05:28", "formattedEnd": "05:35", "text": "Was 20 m 32k of riding towards like a loop and \nthen we did that loop twice and then it was  "}, {"id": 37, "start": 335.76, "end": 346.56, "formattedStart": "05:35", "formattedEnd": "05:46", "text": "Uh like 16k back again from that loop and uh for \nthe first loop it was uh uh quite uh sort of calm  "}, {"id": 38, "start": 346.56, "end": 351.2, "formattedStart": "05:46", "formattedEnd": "05:51", "text": "Condition with the wind a little bit had wind on \nthe way out and a little bit tailwind on the way  "}, {"id": 39, "start": 351.2, "end": 361.68, "formattedStart": "05:51", "formattedEnd": "06:01", "text": "Back uh but then uh we was riding quite steady \npace or on and off uh all the way until maybe 60  "}, {"id": 40, "start": 361.68, "end": 368.08, "formattedStart": "06:01", "formattedEnd": "06:08", "text": "70k mark but then I think too many of the guys in \nthe group realized that uh yeah you're saving too  "}, {"id": 41, "start": 368.08, "end": 374.64, "formattedStart": "06:08", "formattedEnd": "06:14", "text": "Much by just sitting in the pack and therefore a \nlot of motivation from the guys up front sort of  "}, {"id": 42, "start": 374.64, "end": 382.4, "formattedStart": "06:14", "formattedEnd": "06:22", "text": "Uh disappeared and for maybe 30 or 40k we were \nall just sitting up and I think we lost maybe  "}, {"id": 43, "start": 382.4, "end": 388.32, "formattedStart": "06:22", "formattedEnd": "06:28", "text": "Two or 3 minutes to the Uber bikers from behind \nwe were actually riding quicker than them for the  "}, {"id": 44, "start": 388.32, "end": 396.64, "formattedStart": "06:28", "formattedEnd": "06:36", "text": "First 50k I think but then suddenly with a blink \nof an eye they caugh up and after 110k the first  "}, {"id": 45, "start": 396.64, "end": 404.08, "formattedStart": "06:36", "formattedEnd": "06:44", "text": "Big move came when <PERSON><PERSON><PERSON> passed the whole \npack and went to the front and that was after the  "}, {"id": 46, "start": 404.08, "end": 410.15999999999997, "formattedStart": "06:44", "formattedEnd": "06:50", "text": "Second lap when we had a lot of the age groupers \non the side and that was like a moment also where  "}, {"id": 47, "start": 410.16, "end": 415.68, "formattedStart": "06:50", "formattedEnd": "06:55", "text": "I sort of lost a little bit of control of the \nfield because I thought that just stay in my  "}, {"id": 48, "start": 415.68, "end": 421.68, "formattedStart": "06:55", "formattedEnd": "07:01", "text": "Position in the pack but then because there were \nso many athletes in front maybe even or only a few  "}, {"id": 49, "start": 421.68, "end": 426.40000000000003, "formattedStart": "07:01", "formattedEnd": "07:06", "text": "Of them were pros maybe five or six but I couldn't \nreally see who was the pro and who was like the  "}, {"id": 50, "start": 426.4, "end": 431.28, "formattedStart": "07:06", "formattedEnd": "07:11", "text": "Age grouper in front so I sort of lost a little \nbit of control of when the gap was opening up  "}, {"id": 51, "start": 431.28, "end": 439.52, "formattedStart": "07:11", "formattedEnd": "07:19", "text": "Uh and then suddenly I was there 45 seconds behind \nthe the front guys again so I had to put in like a  "}, {"id": 52, "start": 439.52, "end": 446.4, "formattedStart": "07:19", "formattedEnd": "07:26", "text": "A surge just to make sure that I was able to jump \nacross the gap and uh on the way up I was able to  "}, {"id": 53, "start": 446.4, "end": 452.23999999999995, "formattedStart": "07:26", "formattedEnd": "07:32", "text": "I saw like some motorbikes up front so I was like \nassuming that that was the head of the field and  "}, {"id": 54, "start": 452.24, "end": 457.68, "formattedStart": "07:32", "formattedEnd": "07:37", "text": "Then on the way up I also recognized that <PERSON> \nwas so I was catching <PERSON> and as I was passing  "}, {"id": 55, "start": 457.68, "end": 463.76, "formattedStart": "07:37", "formattedEnd": "07:43", "text": "Him he was like screaming to me I got a flat so \nI realized that he got the bad luck that I was  "}, {"id": 56, "start": 463.76, "end": 468.32, "formattedStart": "07:43", "formattedEnd": "07:48", "text": "Having in Oceanside but then I was just keeping \nrunning up and managed to sort of cover that  "}, {"id": 57, "start": 468.32, "end": 474.96, "formattedStart": "07:48", "formattedEnd": "07:54", "text": "Small mistake that I had by letting them get away \nand from there on I was riding in control with  "}, {"id": 58, "start": 474.96, "end": 483.03999999999996, "formattedStart": "07:54", "formattedEnd": "08:03", "text": "Uh with both <PERSON><PERSON> and <PERSON><PERSON> and those guys uh \nand felt that yeah I have control but I still saw  "}, {"id": 59, "start": 483.04, "end": 489.6, "formattedStart": "08:03", "formattedEnd": "08:09", "text": "That <PERSON> was there and I knew that he \ncould potentially be a strong threat for the run i  "}, {"id": 60, "start": 489.6, "end": 496.72, "formattedStart": "08:09", "formattedEnd": "08:16", "text": "Tried to save myself for the run and just getting \nthe liquid in and uh uh came off the highway and  "}, {"id": 61, "start": 496.72, "end": 503.92, "formattedStart": "08:16", "formattedEnd": "08:23", "text": "Did the last 10k of or 15k back to transition and \nuh as I was doing a little bit of those 90° turns  "}, {"id": 62, "start": 503.92, "end": 509.84000000000003, "formattedStart": "08:23", "formattedEnd": "08:29", "text": "And sort of changing a little bit of my stride \nI started cramping up in my right quads and I  "}, {"id": 63, "start": 509.84, "end": 519.1999999999999, "formattedStart": "08:29", "formattedEnd": "08:39", "text": "Was just thinking \"No no way it's all Milwaukee \nall over again.\" Uh and I really had to sort of  "}, {"id": 64, "start": 519.2, "end": 525.5200000000001, "formattedStart": "08:39", "formattedEnd": "08:45", "text": "Uh stand up a little bit and uh really put some \npressure with my arm onto the quad to just help  "}, {"id": 65, "start": 525.52, "end": 531.4399999999999, "formattedStart": "08:45", "formattedEnd": "08:51", "text": "With the blood flow and sort of took like a few \neasier minutes where I lost again the touch with  "}, {"id": 66, "start": 531.44, "end": 537.5200000000001, "formattedStart": "08:51", "formattedEnd": "08:57", "text": "The the group and I had to ride the last 10k by \nmyself in a little bit higher cadence making sure  "}, {"id": 67, "start": 537.52, "end": 541.52, "formattedStart": "08:57", "formattedEnd": "09:01", "text": "That I was stretching out the knee a little bit \nmore and just making sure that I got the cramping  "}, {"id": 68, "start": 541.52, "end": 550.48, "formattedStart": "09:01", "formattedEnd": "09:10", "text": "Under control and uh after a few K I thought that \nyeah it's actually getting better but coming off  "}, {"id": 69, "start": 550.48, "end": 555.04, "formattedStart": "09:10", "formattedEnd": "09:15", "text": "The bike will be interesting i was able to get \noff the bike I think like 30 seconds or something  "}, {"id": 70, "start": 555.04, "end": 566.56, "formattedStart": "09:15", "formattedEnd": "09:26", "text": "Behind <PERSON><PERSON> and 10 15 seconds behind uh <PERSON> \nand <PERSON><PERSON> and uh sort of the main main pack of  "}, {"id": 71, "start": 566.56, "end": 574.56, "formattedStart": "09:26", "formattedEnd": "09:34", "text": "The front guys and as I put on the socks in T1 I \nwas able to do a super quick transition and came  "}, {"id": 72, "start": 574.56, "end": 581.5999999999999, "formattedStart": "09:34", "formattedEnd": "09:41", "text": "Out on the run in third position like 20 seconds \ndown from cam and I had <PERSON> even 10 seconds  "}, {"id": 73, "start": 581.6, "end": 587.28, "formattedStart": "09:41", "formattedEnd": "09:47", "text": "Maybe behind me so I was just settling into like \nan comfortable pace i was letting him sort of ease  "}, {"id": 74, "start": 587.28, "end": 592.9599999999999, "formattedStart": "09:47", "formattedEnd": "09:52", "text": "Catch up to me as I was trying to make sure that I \nwas sort of taking the time to get into the rhythm  "}, {"id": 75, "start": 592.96, "end": 598, "formattedStart": "09:52", "formattedEnd": "09:58", "text": "And then I think it's after like 2k he was like \nmove making the pass and he moved into the front  "}, {"id": 76, "start": 598, "end": 606.16, "formattedStart": "09:58", "formattedEnd": "10:06", "text": "We caught <PERSON> and uh <PERSON> I think and \nuh from there on when he was then setting the pace  "}, {"id": 77, "start": 606.16, "end": 612.24, "formattedStart": "10:06", "formattedEnd": "10:12", "text": "I felt quite good i felt the heart rate was quite \nstable after another cute 2k I think it was at the  "}, {"id": 78, "start": 612.24, "end": 617.6800000000001, "formattedStart": "10:12", "formattedEnd": "10:17", "text": "4k mark i went to the front again and tried to \nget like a feeling of the strength between me  "}, {"id": 79, "start": 617.68, "end": 622.7199999999999, "formattedStart": "10:17", "formattedEnd": "10:22", "text": "And <PERSON> and uh I felt from there on that he \nwas breathing a little bit heavier than what I  "}, {"id": 80, "start": 622.72, "end": 628.24, "formattedStart": "10:22", "formattedEnd": "10:28", "text": "Did in the same time I was feeling very fresh \nand I think that was from that moment that I  "}, {"id": 81, "start": 628.24, "end": 634.4, "formattedStart": "10:28", "formattedEnd": "10:34", "text": "Thought that yeah I'm probably going to win the \nrace uh but in the same time I want to get that  "}, {"id": 82, "start": 634.4, "end": 643.92, "formattedStart": "10:34", "formattedEnd": "10:43", "text": "Far into the course before sort of starting to \npush so I wasn't sure if I should uh stay with  "}, {"id": 83, "start": 643.92, "end": 650.7199999999999, "formattedStart": "10:43", "formattedEnd": "10:50", "text": "<PERSON> until 25 30k mark and then do a fast uh \nlast loop or if I should do it on the second loop  "}, {"id": 84, "start": 650.72, "end": 657.9200000000001, "formattedStart": "10:50", "formattedEnd": "10:57", "text": "But I ended up sort of making my move I think \nafter 8K maybe and from there on I was able to  "}, {"id": 85, "start": 657.92, "end": 663.76, "formattedStart": "10:57", "formattedEnd": "11:03", "text": "Just open up the gap more and more until the very \nend and I think the first lap I was averaging like  "}, {"id": 86, "start": 663.76, "end": 673.4399999999999, "formattedStart": "11:03", "formattedEnd": "11:13", "text": "336 pace perk for the whole lap and the same I \nhad for the second lap so until the 28k mark and  "}, {"id": 87, "start": 673.44, "end": 682.5600000000001, "formattedStart": "11:13", "formattedEnd": "11:22", "text": "Uh out there on the third lap when I got into \nmaybe 30 34 35k I did start feeling it a little  "}, {"id": 88, "start": 682.56, "end": 691.5999999999999, "formattedStart": "11:22", "formattedEnd": "11:31", "text": "Bit uh uh in the legs and energy wise and uh uh \nI think I was maybe uh dropping the pace or I was  "}, {"id": 89, "start": 691.6, "end": 698.64, "formattedStart": "11:31", "formattedEnd": "11:38", "text": "Running 10 seconds nok and maybe making it up to \n345 but I was still able to open up the gap yeah  "}, {"id": 90, "start": 698.64, "end": 704.96, "formattedStart": "11:38", "formattedEnd": "11:44", "text": "Pleased with both taking the 5,000 points in terms \nof the Armor Pro series and also to make a little  "}, {"id": 91, "start": 704.96, "end": 710.48, "formattedStart": "11:44", "formattedEnd": "11:50", "text": "Bit of damage in terms of the points that the rest \nof the guys got behind me so uh quite pleased with  "}, {"id": 92, "start": 710.48, "end": 717.12, "formattedStart": "11:50", "formattedEnd": "11:57", "text": "AR Texas and uh and also something that I'm also \npleased with is both how I was able to deal with  "}, {"id": 93, "start": 717.12, "end": 727.04, "formattedStart": "11:57", "formattedEnd": "12:07", "text": "The nutrition uh if you remember back in 2024 we \ncan wind back to Kona few clips there yeah as you  "}, {"id": 94, "start": 727.04, "end": 733.5999999999999, "formattedStart": "12:07", "formattedEnd": "12:13", "text": "Saw uh puking all over the place i think it was \npuking 15 times here on the on the bike in Kona  "}, {"id": 95, "start": 733.6, "end": 740.8000000000001, "formattedStart": "12:13", "formattedEnd": "12:20", "text": "And also puking in Hamburg and the fact I was able \nto sort out the issues I had with the throat and  "}, {"id": 96, "start": 740.8, "end": 746.8, "formattedStart": "12:20", "formattedEnd": "12:26", "text": "Uh uh keep it all in the stomach is something \nI was pleased with and I was also staying very  "}, {"id": 97, "start": 746.8, "end": 754.24, "formattedStart": "12:26", "formattedEnd": "12:34", "text": "Very high on grams of carbs per hour so I think I \nwas averaging like 160 almost uh like on a run I  "}, {"id": 98, "start": 754.24, "end": 762.4, "formattedStart": "12:34", "formattedEnd": "12:42", "text": "Was using one uh 0.7 L bottle with a double mix \nof Morton that I was using from transition and  "}, {"id": 99, "start": 762.4, "end": 768.8, "formattedStart": "12:42", "formattedEnd": "12:48", "text": "Then also had another bottle that I took halfway \ninto the run in the on the second lap I was doing  "}, {"id": 100, "start": 768.8, "end": 780.56, "formattedStart": "12:48", "formattedEnd": "13:00", "text": "Another 0.7 L of double mix morton so uh just \nthere I had um 320 g of carbs based on that and  "}, {"id": 101, "start": 780.56, "end": 788.16, "formattedStart": "13:00", "formattedEnd": "13:08", "text": "Also was able to add on some uh uh morton gels \nas well so uh pretty high on carbs if I wasn't  "}, {"id": 102, "start": 788.16, "end": 795.76, "formattedStart": "13:08", "formattedEnd": "13:15", "text": "Gaining weight on during the race I was probably \nquite close and also was able to keep the heart  "}, {"id": 103, "start": 795.76, "end": 805.2, "formattedStart": "13:15", "formattedEnd": "13:25", "text": "Rate quite steady throughout the race even though \nI was racing in 30 31° I was able to feel uh that  "}, {"id": 104, "start": 805.2, "end": 811.6, "formattedStart": "13:25", "formattedEnd": "13:31", "text": "The sun wasn't too warm it wasn't like cooking too \nmuch and that that also gave me the advantage that  "}, {"id": 105, "start": 811.6, "end": 817.2, "formattedStart": "13:31", "formattedEnd": "13:37", "text": "Instead of running in the outside corner like \nwhere we had some trees uh covering half of the  "}, {"id": 106, "start": 817.2, "end": 823.6800000000001, "formattedStart": "13:37", "formattedEnd": "13:43", "text": "Run course I could still I could always do the \nshortest line because I felt it wasn't a big uh  "}, {"id": 107, "start": 823.68, "end": 829.8399999999999, "formattedStart": "13:43", "formattedEnd": "13:49", "text": "Discomfort by running in the sun so I was always \nable to do the shortness line on course and that's  "}, {"id": 108, "start": 829.84, "end": 836.8000000000001, "formattedStart": "13:49", "formattedEnd": "13:56", "text": "Also something that I'm uh positive take away uh \nfrom this race another thing <PERSON><PERSON> just announced  "}, {"id": 109, "start": 836.8, "end": 844.7199999999999, "formattedStart": "13:56", "formattedEnd": "14:04", "text": "That uh the world championship is going back to \nKona Hawaii to a one day event now for from next  "}, {"id": 110, "start": 844.72, "end": 851.84, "formattedStart": "14:04", "formattedEnd": "14:11", "text": "Year on which means this year is the very last \nopportunity to win an AR world championship in  "}, {"id": 111, "start": 851.84, "end": 858.4, "formattedStart": "14:11", "formattedEnd": "14:18", "text": "Nice so uh a it's cool that it's going back again \nto the island of Hawaii it's great that they are  "}, {"id": 112, "start": 858.4, "end": 864, "formattedStart": "14:18", "formattedEnd": "14:24", "text": "Bringing the male and female together again i \nthink that that was maybe the biggest downside  "}, {"id": 113, "start": 864, "end": 872.16, "formattedStart": "14:24", "formattedEnd": "14:32", "text": "By splitting up the event and having different \nlocations uh uh but yeah N is a still a fantastic  "}, {"id": 114, "start": 872.16, "end": 878.56, "formattedStart": "14:32", "formattedEnd": "14:38", "text": "Course and I'm really motivated by the fact that \nit's potentially the last chance of winning an  "}, {"id": 115, "start": 878.56, "end": 885.8399999999999, "formattedStart": "14:38", "formattedEnd": "14:45", "text": "Armen world championship title in this and uh now \nas you can see behind me I am back in Bergen and  "}, {"id": 116, "start": 885.84, "end": 894.48, "formattedStart": "14:45", "formattedEnd": "14:54", "text": "<PERSON> staying here for another two weeks until \nheading to Axen Province in uh in France for 7.3  "}, {"id": 117, "start": 894.48, "end": 903.04, "formattedStart": "14:54", "formattedEnd": "15:03", "text": "Uh need some more points in the Arman Pro series \nso I didn't get much point in uh Oceanside because  "}, {"id": 118, "start": 903.04, "end": 910.8, "formattedStart": "15:03", "formattedEnd": "15:10", "text": "Of the puncture lost like 12 minutes or so by \nthat which is uh tons of points so I need to back  "}, {"id": 119, "start": 910.8, "end": 919.68, "formattedStart": "15:10", "formattedEnd": "15:19", "text": "Up that performance with a strong race in um Axen \nthen the plan is to do Arman Frankfurt and then uh  "}, {"id": 120, "start": 919.68, "end": 926.8, "formattedStart": "15:19", "formattedEnd": "15:26", "text": "The world championship in Maya and then I should \nhave hopefully five good races with five good  "}, {"id": 121, "start": 926.8, "end": 934.4799999999999, "formattedStart": "15:26", "formattedEnd": "15:34", "text": "Scores that will put me in a great position for \nthe Armen Pro Series currently in second position  "}, {"id": 122, "start": 934.48, "end": 940.08, "formattedStart": "15:34", "formattedEnd": "15:40", "text": "But it's still early a lot of people haven't raced \ntwice even so uh but it's good to get like an idea  "}, {"id": 123, "start": 940.08, "end": 944.88, "formattedStart": "15:40", "formattedEnd": "15:44", "text": "And get a feeling of it and if you like the \nvideo please give it a like and if you haven't  "}, {"id": 124, "start": 944.88, "end": 962, "formattedStart": "15:44", "formattedEnd": "16:02", "text": "Subscribed to the video yet it would be highly \nappreciated if you do uh until next time yeehaw"}]}