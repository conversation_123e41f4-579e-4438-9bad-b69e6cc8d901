{"videoId": "Xm_2Xlm-rOA", "language": "nl-orig", "transcript": [{"id": 1, "start": 0.399, "end": 13.559, "formattedStart": "00:00", "formattedEnd": "00:13", "text": "<PERSON><PERSON>. <PERSON><PERSON> is hij. <PERSON><PERSON> zijn we dan."}, {"id": 2, "start": 13.559, "end": 15.589, "formattedStart": "00:13", "formattedEnd": "00:15", "text": "Ik doe dit jaar mee aan de <PERSON> for"}, {"id": 3, "start": 15.599, "end": 17.51, "formattedStart": "00:15", "formattedEnd": "00:17", "text": "Ik doe dit jaar mee aan de Wings for Life World Run. Een wedstrijd die"}, {"id": 4, "start": 17.51, "end": 17.52, "formattedStart": "00:17", "formattedEnd": "00:17", "text": "Life World Run. <PERSON><PERSON> wedst<PERSON><PERSON><PERSON> die"}, {"id": 5, "start": 17.52, "end": 19.99, "formattedStart": "00:17", "formattedEnd": "00:19", "text": "Life World Run. Een wedstrijd die wereldwijd op hetzelfde moment start met"}, {"id": 6, "start": 19.99, "end": 20, "formattedStart": "00:19", "formattedEnd": "00:20", "text": "wereld<PERSON>jd op hetzelfde moment start met"}, {"id": 7, "start": 20, "end": 22.63, "formattedStart": "00:20", "formattedEnd": "00:22", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> op hetzelfde moment start met een catcher car. De <PERSON> is een"}, {"id": 8, "start": 22.63, "end": 22.64, "formattedStart": "00:22", "formattedEnd": "00:22", "text": "een catcher car. <PERSON> catcher Car is een"}, {"id": 9, "start": 22.64, "end": 24.67, "formattedStart": "00:22", "formattedEnd": "00:24", "text": "een catcher car. De catcher Car is een auto die start na een half uur met"}, {"id": 10, "start": 24.67, "end": 24.68, "formattedStart": "00:24", "formattedEnd": "00:24", "text": "auto die start na een half uur met"}, {"id": 11, "start": 24.68, "end": 27.23, "formattedStart": "00:24", "formattedEnd": "00:27", "text": "auto die start na een half uur met rijden en die gaat elke keer i<PERSON>je"}, {"id": 12, "start": 27.23, "end": 27.24, "formattedStart": "00:27", "formattedEnd": "00:27", "text": "rijden en die gaat elke keer i<PERSON>je"}, {"id": 13, "start": 27.24, "end": 29.07, "formattedStart": "00:27", "formattedEnd": "00:29", "text": "rijden en die gaat elke keer ietsje harder rijden tot en met je wordt"}, {"id": 14, "start": 29.07, "end": 29.08, "formattedStart": "00:29", "formattedEnd": "00:29", "text": "harder rijden tot en met je wordt"}, {"id": 15, "start": 29.08, "end": 30.31, "formattedStart": "00:29", "formattedEnd": "00:30", "text": "harder rijden tot en met je wordt ingehaald."}, {"id": 16, "start": 30.31, "end": 30.32, "formattedStart": "00:30", "formattedEnd": "00:30", "text": "ingehaald."}, {"id": 17, "start": 30.32, "end": 32.31, "formattedStart": "00:30", "formattedEnd": "00:32", "text": "ingehaald. Na de marathon van Rotterdam is dit mijn"}, {"id": 18, "start": 32.31, "end": 32.32, "formattedStart": "00:32", "formattedEnd": "00:32", "text": "Na de marathon van Rotterdam is dit mijn"}, {"id": 19, "start": 32.32, "end": 34.549, "formattedStart": "00:32", "formattedEnd": "00:34", "text": "Na de marathon van Rotterdam is dit mijn eerste wedstrijd. Dus ik ga er nog niet"}, {"id": 20, "start": 34.549, "end": 34.559, "formattedStart": "00:34", "formattedEnd": "00:34", "text": "eerste wedstrijd. Dus ik ga er nog niet"}, {"id": 21, "start": 34.559, "end": 36.35, "formattedStart": "00:34", "formattedEnd": "00:36", "text": "eerste wedstrijd. Dus ik ga er nog niet keihard tegenaan. Maar we gaan lekker"}, {"id": 22, "start": 36.35, "end": 36.36, "formattedStart": "00:36", "formattedEnd": "00:36", "text": "k<PERSON><PERSON> tegenaan. <PERSON>ar we gaan lekker"}, {"id": 23, "start": 36.36, "end": 38.51, "formattedStart": "00:36", "formattedEnd": "00:38", "text": "k<PERSON>hard tegenaan. Maar we gaan lekker een stuk meelopen en ik heb er <PERSON> veel"}, {"id": 24, "start": 38.51, "end": 38.52, "formattedStart": "00:38", "formattedEnd": "00:38", "text": "een stuk meelopen en ik heb er <PERSON> veel"}, {"id": 25, "start": 38.52, "end": 40.75, "formattedStart": "00:38", "formattedEnd": "00:40", "text": "een stuk meelopen en ik heb er heel veel zin in. Goedemorgen jongens. Vandaag is"}, {"id": 26, "start": 40.75, "end": 40.76, "formattedStart": "00:40", "formattedEnd": "00:40", "text": "zin in. Goedemorgen jongens. Vandaag is"}, {"id": 27, "start": 40.76, "end": 43.869, "formattedStart": "00:40", "formattedEnd": "00:43", "text": "zin in. Goedemorgen jongens. <PERSON><PERSON><PERSON> is het zover. Wings for life. We gaan even"}, {"id": 28, "start": 43.869, "end": 43.879, "formattedStart": "00:43", "formattedEnd": "00:43", "text": "het zover. Wings for life. We gaan even"}, {"id": 29, "start": 43.879, "end": 46.069, "formattedStart": "00:43", "formattedEnd": "00:46", "text": "het zover. Wings for life. We gaan even ontbijten. Volgens mij start het rond 1"}, {"id": 30, "start": 46.069, "end": 46.079, "formattedStart": "00:46", "formattedEnd": "00:46", "text": "ontbijten. Volgens mij start het rond 1"}, {"id": 31, "start": 46.079, "end": 49.709, "formattedStart": "00:46", "formattedEnd": "00:49", "text": "ontbijten. Volgens mij start het rond 1 uur. We gaan dus e<PERSON>t Pi<PERSON> van <PERSON>"}, {"id": 32, "start": 49.709, "end": 49.719, "formattedStart": "00:49", "formattedEnd": "00:49", "text": "uur. We gaan dus e<PERSON>t <PERSON>"}, {"id": 33, "start": 49.719, "end": 52.75, "formattedStart": "00:49", "formattedEnd": "00:52", "text": "uur. We gaan dus e<PERSON>t Pi<PERSON> van de P<PERSON> ophalen en da<PERSON><PERSON>. De 210"}, {"id": 34, "start": 52.75, "end": 52.76, "formattedStart": "00:52", "formattedEnd": "00:52", "text": "ophalen en da<PERSON>na <PERSON>. De 210"}, {"id": 35, "start": 52.76, "end": 55.27, "formattedStart": "00:52", "formattedEnd": "00:55", "text": "<PERSON><PERSON><PERSON> en da<PERSON><PERSON>. De 210 marathonloper van het NN running team."}, {"id": 36, "start": 55.27, "end": 55.28, "formattedStart": "00:55", "formattedEnd": "00:55", "text": "marathon<PERSON><PERSON> van het NN running team."}, {"id": 37, "start": 55.28, "end": 56.869, "formattedStart": "00:55", "formattedEnd": "00:56", "text": "marathon<PERSON><PERSON> van het NN running team. <PERSON><PERSON> dat is sowi<PERSON>o harts<PERSON><PERSON>ke leuk. En"}, {"id": 38, "start": 56.869, "end": 56.879, "formattedStart": "00:56", "formattedEnd": "00:56", "text": "Dus dat is sowieso hartstikke leuk. En"}, {"id": 39, "start": 56.879, "end": 57.91, "formattedStart": "00:56", "formattedEnd": "00:57", "text": "Dus dat is sowieso hartstikke leuk. En daarna gaan we gezamenlijk"}, {"id": 40, "start": 57.91, "end": 57.92, "formattedStart": "00:57", "formattedEnd": "00:57", "text": "daarna gaan we gezamenlijk"}, {"id": 41, "start": 57.92, "end": 60.67, "formattedStart": "00:57", "formattedEnd": "01:00", "text": "daarna gaan we gezamenlijk waarschijnlijk eh stukjes lopen. Ik heb"}, {"id": 42, "start": 60.67, "end": 60.68, "formattedStart": "01:00", "formattedEnd": "01:00", "text": "waarschijnlijk eh stukjes lopen. Ik heb"}, {"id": 43, "start": 60.68, "end": 62.028999999999996, "formattedStart": "01:00", "formattedEnd": "01:02", "text": "waarschijnlijk eh stukjes lopen. Ik heb er wel zin in. Ik ben <PERSON> benieuwd naar"}, {"id": 44, "start": 62.028999999999996, "end": 62.039, "formattedStart": "01:02", "formattedEnd": "01:02", "text": "er wel zin in. Ik ben <PERSON> benieuwd naar"}, {"id": 45, "start": 62.039, "end": 64.27, "formattedStart": "01:02", "formattedEnd": "01:04", "text": "er wel zin in. Ik ben <PERSON> benieuwd naar de wedstrijd. We gaan zoteen rijden met"}, {"id": 46, "start": 64.27, "end": 64.28, "formattedStart": "01:04", "formattedEnd": "01:04", "text": "de wedstrijd. We gaan zoteen rijden met"}, {"id": 47, "start": 64.28, "end": 67.99, "formattedStart": "01:04", "formattedEnd": "01:07", "text": "de wedstrijd. We gaan zoteen rijden met de Hyundai Inste hebben geleend voor"}, {"id": 48, "start": 67.99, "end": 68, "formattedStart": "01:07", "formattedEnd": "01:08", "text": "de Hyundai Inste hebben geleend voor"}, {"id": 49, "start": 68, "end": 69.429, "formattedStart": "01:08", "formattedEnd": "01:09", "text": "de Hyundai Inste hebben geleend voor deze wedstrijd om in ieder geval die"}, {"id": 50, "start": 69.429, "end": 69.439, "formattedStart": "01:09", "formattedEnd": "01:09", "text": "deze wedstrijd om in ieder geval die"}, {"id": 51, "start": 69.439, "end": 71.429, "formattedStart": "01:09", "formattedEnd": "01:11", "text": "deze wedstrijd om in ieder geval die mensen op te halen en om naar de"}, {"id": 52, "start": 71.429, "end": 71.439, "formattedStart": "01:11", "formattedEnd": "01:11", "text": "mensen op te halen en om naar de"}, {"id": 53, "start": 71.439, "end": 72.95, "formattedStart": "01:11", "formattedEnd": "01:12", "text": "mensen op te halen en om naar de wedstrijd toe te gaan. Dat is ook"}, {"id": 54, "start": 72.95, "end": 72.96000000000001, "formattedStart": "01:12", "formattedEnd": "01:12", "text": "wed<PERSON><PERSON><PERSON>d toe te gaan. Dat is ook"}, {"id": 55, "start": 72.96000000000001, "end": 75.03, "formattedStart": "01:12", "formattedEnd": "01:15", "text": "wedst<PERSON><PERSON>d toe te gaan. Dat is ook hartstikke tof. Allereerst gaan we even"}, {"id": 56, "start": 75.03, "end": 75.03999999999999, "formattedStart": "01:15", "formattedEnd": "01:15", "text": "hartstikke tof. Allereerst gaan we even"}, {"id": 57, "start": 75.03999999999999, "end": 77.95, "formattedStart": "01:15", "formattedEnd": "01:17", "text": "hartstikke tof. Allereerst gaan we even ontbijten. Goedemorgen. Ik heb er zin"}, {"id": 58, "start": 77.95, "end": 77.96000000000001, "formattedStart": "01:17", "formattedEnd": "01:17", "text": "ontbijten. Goedemorgen. Ik heb er zin"}, {"id": 59, "start": 77.96000000000001, "end": 113.6, "formattedStart": "01:17", "formattedEnd": "01:53", "text": "ontbijten. Goedemorgen. Ik heb er zin in."}, {"id": 60, "start": 113.6, "end": 115.55, "formattedStart": "01:53", "formattedEnd": "01:55", "text": "<PERSON><PERSON>, dit is de auto jongens, de <PERSON>na"}, {"id": 61, "start": 115.56, "end": 118.429, "formattedStart": "01:55", "formattedEnd": "01:58", "text": "<PERSON><PERSON>, dit is de auto jong<PERSON>, de <PERSON><PERSON>. <PERSON><PERSON><PERSON> gaan we <PERSON>jo<PERSON> en <PERSON>m"}, {"id": 62, "start": 118.429, "end": 118.439, "formattedStart": "01:58", "formattedEnd": "01:58", "text": "Inster. <PERSON><PERSON><PERSON> gaan we <PERSON><PERSON> en <PERSON>m"}, {"id": 63, "start": 118.439, "end": 122.59, "formattedStart": "01:58", "formattedEnd": "02:02", "text": "Inster. <PERSON><PERSON><PERSON> gaan we <PERSON><PERSON>rn en Pim ophalen. <PERSON><PERSON>, let's go. Pretty nice. Dit"}, {"id": 64, "start": 122.59, "end": 122.6, "formattedStart": "02:02", "formattedEnd": "02:02", "text": "ophalen. <PERSON><PERSON>, let's go. Pretty nice. Dit"}, {"id": 65, "start": 122.6, "end": 124.51, "formattedStart": "02:02", "formattedEnd": "02:04", "text": "ophalen. <PERSON><PERSON>, let's go. Pretty nice. Dit is een vol elektrische wagen. Als je"}, {"id": 66, "start": 124.51, "end": 124.52, "formattedStart": "02:04", "formattedEnd": "02:04", "text": "is een vol elektrische wagen. Als je"}, {"id": 67, "start": 124.52, "end": 126.59, "formattedStart": "02:04", "formattedEnd": "02:06", "text": "is een vol elektrische wagen. Als je zo'n batterij op laat, kan die gewoon"}, {"id": 68, "start": 126.59, "end": 126.6, "formattedStart": "02:06", "formattedEnd": "02:06", "text": "zo'n batterij op laat, kan die gewoon"}, {"id": 69, "start": 126.6, "end": 129.15, "formattedStart": "02:06", "formattedEnd": "02:09", "text": "zo'n batterij op laat, kan die gewoon 370 km mee. Dus nog wel een aardig"}, {"id": 70, "start": 129.15, "end": 129.16, "formattedStart": "02:09", "formattedEnd": "02:09", "text": "370 km mee. Dus nog wel een aardig"}, {"id": 71, "start": 129.16, "end": 131.15, "formattedStart": "02:09", "formattedEnd": "02:11", "text": "370 km mee. Dus nog wel een aardig afstandje. Het is niet de grote auto,"}, {"id": 72, "start": 131.15, "end": 131.16, "formattedStart": "02:11", "formattedEnd": "02:11", "text": "afstandje. Het is niet de grote auto,"}, {"id": 73, "start": 131.16, "end": 132.55, "formattedStart": "02:11", "formattedEnd": "02:12", "text": "afstandje. Het is niet de grote auto, dus je kan in de stad er nog wel mee"}, {"id": 74, "start": 132.55, "end": 132.56, "formattedStart": "02:12", "formattedEnd": "02:12", "text": "dus je kan in de stad er nog wel mee"}, {"id": 75, "start": 132.56, "end": 134.11, "formattedStart": "02:12", "formattedEnd": "02:14", "text": "dus je kan in de stad er nog wel mee goed in de rondte. Alleen je kan al die"}, {"id": 76, "start": 134.11, "end": 134.12, "formattedStart": "02:14", "formattedEnd": "02:14", "text": "goed in de rondte. <PERSON><PERSON> je kan al die"}, {"id": 77, "start": 134.12, "end": 135.71, "formattedStart": "02:14", "formattedEnd": "02:15", "text": "goed in de rondte. <PERSON><PERSON> je kan al die banken ook inklappen enzo. Dan wordt het"}, {"id": 78, "start": 135.71, "end": 135.72, "formattedStart": "02:15", "formattedEnd": "02:15", "text": "banken ook inklappen enzo. Dan wordt het"}, {"id": 79, "start": 135.72, "end": 137.63, "formattedStart": "02:15", "formattedEnd": "02:17", "text": "banken ook inklappen enzo. Dan wordt het echt een ruimere auto. Dus eh op zich"}, {"id": 80, "start": 137.63, "end": 137.64, "formattedStart": "02:17", "formattedEnd": "02:17", "text": "echt een ruimere auto. Dus eh op zich"}, {"id": 81, "start": 137.64, "end": 139.27, "formattedStart": "02:17", "formattedEnd": "02:19", "text": "echt een ruimere auto. Dus eh op zich relax autootje om eventjes mee eh in de"}, {"id": 82, "start": 139.27, "end": 139.28, "formattedStart": "02:19", "formattedEnd": "02:19", "text": "relax autootje om eventjes mee eh in de"}, {"id": 83, "start": 139.28, "end": 161.2, "formattedStart": "02:19", "formattedEnd": "02:41", "text": "relax autootje om eventjes mee eh in de rondte te gaan."}, {"id": 84, "start": 161.2, "end": 164.55, "formattedStart": "02:41", "formattedEnd": "02:44", "text": "<PERSON>ar is hij, de enige echte. Ja, ja, ja,"}, {"id": 85, "start": 164.56, "end": 166.79, "formattedStart": "02:44", "formattedEnd": "02:46", "text": "<PERSON>ar is hij, de enige echte. Ja, ja, ja, ja, ja. <PERSON><PERSON><PERSON>, ze laten ons niet in"}, {"id": 86, "start": 166.79, "end": 166.8, "formattedStart": "02:46", "formattedEnd": "02:46", "text": "ja, ja. <PERSON><PERSON><PERSON>, ze laten ons niet in"}, {"id": 87, "start": 166.8, "end": 169.309, "formattedStart": "02:46", "formattedEnd": "02:49", "text": "ja, ja. <PERSON><PERSON><PERSON>, ze laten ons niet in de steek hoor met deze autootjes. Jongen"}, {"id": 88, "start": 169.309, "end": 169.31900000000002, "formattedStart": "02:49", "formattedEnd": "02:49", "text": "de steek hoor met deze autootjes. Jongen"}, {"id": 89, "start": 169.31900000000002, "end": 172.239, "formattedStart": "02:49", "formattedEnd": "02:52", "text": "de steek hoor met deze autootjes. Jongen Ja."}, {"id": 90, "start": 172.239, "end": 173.47, "formattedStart": "02:52", "formattedEnd": "02:53", "text": "Met jou? Ja,"}, {"id": 91, "start": 173.48, "end": 178.39, "formattedStart": "02:53", "formattedEnd": "02:58", "text": "Met jou? <PERSON><PERSON>, lekker. <PERSON><PERSON>. Hoe is het?"}, {"id": 92, "start": 178.39, "end": 178.4, "formattedStart": "02:58", "formattedEnd": "02:58", "text": "lekker. <PERSON>rgen. Hoe is het?"}, {"id": 93, "start": 178.4, "end": 181.39, "formattedStart": "02:58", "formattedEnd": "03:01", "text": "lekker. <PERSON><PERSON>. Hoe is het? Goed. <PERSON>a, goed. Goed. <PERSON><PERSON>, zeker. Hey,"}, {"id": 94, "start": 181.39, "end": 181.4, "formattedStart": "03:01", "formattedEnd": "03:01", "text": "Goed. <PERSON><PERSON>, goed. Goed. <PERSON><PERSON>, zeker. Hey,"}, {"id": 95, "start": 181.4, "end": 183.589, "formattedStart": "03:01", "formattedEnd": "03:03", "text": "Goed. <PERSON><PERSON>, goed. Goed. <PERSON><PERSON>, zeker. Hey, mooie bak, man. Hey, een mooi wagentje,"}, {"id": 96, "start": 183.589, "end": 183.599, "formattedStart": "03:03", "formattedEnd": "03:03", "text": "mooie bak, man. Hey, een mooi wagentje,"}, {"id": 97, "start": 183.599, "end": 194.239, "formattedStart": "03:03", "formattedEnd": "03:14", "text": "mooie bak, man. Hey, een mooi wagentje, hè? Of niet? 100% elektrisch. Let's go."}, {"id": 98, "start": 194.239, "end": 203.36, "formattedStart": "03:14", "formattedEnd": "03:23", "text": "We are there. <PERSON><PERSON>,"}, {"id": 99, "start": 203.36, "end": 207.07, "formattedStart": "03:23", "formattedEnd": "03:27", "text": "hopse. N gaan we gaan met zijn jongens"}, {"id": 100, "start": 207.07999999999998, "end": 209.27, "formattedStart": "03:27", "formattedEnd": "03:29", "text": "hopse. N gaan we gaan met zijn jongens een beetje. <PERSON><PERSON>, kijk ik heb een mooi"}, {"id": 101, "start": 209.27, "end": 209.28, "formattedStart": "03:29", "formattedEnd": "03:29", "text": "een beetje. <PERSON>, kijk ik heb een mooi"}, {"id": 102, "start": 209.28, "end": 211.67000000000002, "formattedStart": "03:29", "formattedEnd": "03:31", "text": "een beetje. <PERSON><PERSON>, kijk ik heb een mooi petje voor tijdens de wedstrijd. Die ga"}, {"id": 103, "start": 211.67000000000002, "end": 211.68, "formattedStart": "03:31", "formattedEnd": "03:31", "text": "petje voor tijdens de wedstrijd. Die ga"}, {"id": 104, "start": 211.68, "end": 213.22899999999998, "formattedStart": "03:31", "formattedEnd": "03:33", "text": "petje voor tijdens de wedstrijd. Die ga ik lekker aandoen. Werkt wel mooi met"}, {"id": 105, "start": 213.22899999999998, "end": 213.239, "formattedStart": "03:33", "formattedEnd": "03:33", "text": "ik lekker aandoen. Werkt wel mooi met"}, {"id": 106, "start": 213.239, "end": 214.949, "formattedStart": "03:33", "formattedEnd": "03:34", "text": "ik lekker aandoen. Werkt wel mooi met het jasje zo. <PERSON><PERSON> eh dat is in ieder"}, {"id": 107, "start": 214.949, "end": 214.959, "formattedStart": "03:34", "formattedEnd": "03:34", "text": "het jasje zo. <PERSON><PERSON> eh dat is in ieder"}, {"id": 108, "start": 214.959, "end": 217.429, "formattedStart": "03:34", "formattedEnd": "03:37", "text": "het jasje zo. <PERSON><PERSON> eh dat is in ieder geval nice. We staan hier zo bij"}, {"id": 109, "start": 217.429, "end": 217.439, "formattedStart": "03:37", "formattedEnd": "03:37", "text": "geval nice. We staan hier zo bij"}, {"id": 110, "start": 217.439, "end": 220.75, "formattedStart": "03:37", "formattedEnd": "03:40", "text": "geval nice. We staan hier zo bij de <PERSON>, kijk Feyenoord hoor ik alweer"}, {"id": 111, "start": 220.75, "end": 220.76, "formattedStart": "03:40", "formattedEnd": "03:40", "text": "<PERSON>, kijk Feyenoord hoor ik alweer"}, {"id": 112, "start": 220.76, "end": 222.589, "formattedStart": "03:40", "formattedEnd": "03:42", "text": "<PERSON>, kijk Feyenoord hoor ik alweer geschild. Dus eh ik heb er zin in. We"}, {"id": 113, "start": 222.589, "end": 222.599, "formattedStart": "03:42", "formattedEnd": "03:42", "text": "geschild. Dus eh ik heb er zin in. We"}, {"id": 114, "start": 222.599, "end": 227.11, "formattedStart": "03:42", "formattedEnd": "03:47", "text": "ges<PERSON>. Dus eh ik heb er zin in. We gaan lekker eh er tegenaan. Let's go."}, {"id": 115, "start": 227.11, "end": 227.12, "formattedStart": "03:47", "formattedEnd": "03:47", "text": "gaan lekker eh er tegenaan. Let's go."}, {"id": 116, "start": 227.12, "end": 230.27, "formattedStart": "03:47", "formattedEnd": "03:50", "text": "gaan lekker eh er tegenaan. Let's go. Ja. J<PERSON>, maar ik laat zo hier."}, {"id": 117, "start": 230.27, "end": 230.28, "formattedStart": "03:50", "formattedEnd": "03:50", "text": "<PERSON><PERSON><PERSON> <PERSON><PERSON>, maar ik laat zo hier."}, {"id": 118, "start": 230.28, "end": 241.68, "formattedStart": "03:50", "formattedEnd": "04:01", "text": "<PERSON><PERSON><PERSON> <PERSON><PERSON>, maar ik laat zo hier. Dit is het nummertje jongens."}, {"id": 119, "start": 241.68, "end": 245.149, "formattedStart": "04:01", "formattedEnd": "04:05", "text": "ofzo. Dan 10 km beetje"}, {"id": 120, "start": 245.159, "end": 248.71, "formattedStart": "04:05", "formattedEnd": "04:08", "text": "ofzo. Dan 10 km beetje 4350. We staan nu bij de start en eh we"}, {"id": 121, "start": 248.71, "end": 248.72, "formattedStart": "04:08", "formattedEnd": "04:08", "text": "4350. We staan nu bij de start en eh we"}, {"id": 122, "start": 248.72, "end": 250.309, "formattedStart": "04:08", "formattedEnd": "04:10", "text": "4350. We staan nu bij de start en eh we hebben over 10 minuutjes begint. Dus ik"}, {"id": 123, "start": 250.309, "end": 250.319, "formattedStart": "04:10", "formattedEnd": "04:10", "text": "hebben over 10 minuutjes begint. Dus ik"}, {"id": 124, "start": 250.319, "end": 253.27, "formattedStart": "04:10", "formattedEnd": "04:13", "text": "hebben over 10 minuutjes begint. Dus ik ben wel benieuwd. We gaan het zien."}, {"id": 125, "start": 253.27, "end": 253.28, "formattedStart": "04:13", "formattedEnd": "04:13", "text": "ben wel benieuwd. We gaan het zien."}, {"id": 126, "start": 253.28, "end": 255.869, "formattedStart": "04:13", "formattedEnd": "04:15", "text": "ben wel benieuwd. We gaan het zien. Kijk, kijk. Zij gaan volgens mij zoteen"}, {"id": 127, "start": 255.869, "end": 255.879, "formattedStart": "04:15", "formattedEnd": "04:15", "text": "<PERSON><PERSON>, kijk. Zij gaan volgens mij zoteen"}, {"id": 128, "start": 255.879, "end": 265.32, "formattedStart": "04:15", "formattedEnd": "04:25", "text": "<PERSON><PERSON>, kijk. Zij gaan volgens mij zoteen de catching car rijden."}, {"id": 129, "start": 265.32, "end": 267.55, "formattedStart": "04:25", "formattedEnd": "04:27", "text": "<PERSON><PERSON>, voor de start. We zijn er bijna"}, {"id": 130, "start": 267.56, "end": 270.07, "formattedStart": "04:27", "formattedEnd": "04:30", "text": "<PERSON><PERSON>, voor de start. We zijn er bijna klaar voor. Ze zijn allemaal bezig. Dus"}, {"id": 131, "start": 270.07, "end": 270.08, "formattedStart": "04:30", "formattedEnd": "04:30", "text": "klaar voor. Ze zijn allemaal bezig. Dus"}, {"id": 132, "start": 270.08, "end": 271.749, "formattedStart": "04:30", "formattedEnd": "04:31", "text": "klaar voor. Ze zijn allemaal bezig. Dus eh ik heb er heel veel zin in om even"}, {"id": 133, "start": 271.749, "end": 271.759, "formattedStart": "04:31", "formattedEnd": "04:31", "text": "eh ik heb er heel veel zin in om even"}, {"id": 134, "start": 271.759, "end": 272.95, "formattedStart": "04:31", "formattedEnd": "04:32", "text": "eh ik heb er heel veel zin in om even lekker stuk te"}, {"id": 135, "start": 272.95, "end": 272.96, "formattedStart": "04:32", "formattedEnd": "04:32", "text": "lekker stuk te"}, {"id": 136, "start": 272.96, "end": 275.55, "formattedStart": "04:32", "formattedEnd": "04:35", "text": "lekker stuk te lopen. Let's go jongens. Very"}, {"id": 137, "start": 275.55, "end": 275.56, "formattedStart": "04:35", "formattedEnd": "04:35", "text": "lopen. Let's go jongens. Very"}, {"id": 138, "start": 275.56, "end": 278.07, "formattedStart": "04:35", "formattedEnd": "04:38", "text": "lopen. Let's go jongens. Very excited. We wijzen die kant op. We"}, {"id": 139, "start": 278.07, "end": 278.08, "formattedStart": "04:38", "formattedEnd": "04:38", "text": "excited. We wijzen die kant op. We"}, {"id": 140, "start": 278.08, "end": 283.039, "formattedStart": "04:38", "formattedEnd": "04:43", "text": "excited. We wijzen die kant op. We wijzen die kant op. Blijf zingen."}, {"id": 141, "start": 283.039, "end": 286.189, "formattedStart": "04:43", "formattedEnd": "04:46", "text": "<PERSON><PERSON>, daar is de catching car. Bam. Deze"}, {"id": 142, "start": 286.199, "end": 287.59000000000003, "formattedStart": "04:46", "formattedEnd": "04:47", "text": "<PERSON><PERSON>, daar is de catching car. Bam. Deze gaat dad<PERSON>jk"}, {"id": 143, "start": 287.59000000000003, "end": 287.6, "formattedStart": "04:47", "formattedEnd": "04:47", "text": "gaat <PERSON>"}, {"id": 144, "start": 287.6, "end": 291.8, "formattedStart": "04:47", "formattedEnd": "04:51", "text": "gaat dadelijk pakken. Go, go,"}, {"id": 145, "start": 291.8, "end": 296.029, "formattedStart": "04:51", "formattedEnd": "04:56", "text": "go. We zijn jongens. Let's go. We gaan"}, {"id": 146, "start": 296.039, "end": 298.11, "formattedStart": "04:56", "formattedEnd": "04:58", "text": "go. We zijn jongens. Let's go. We gaan beginnen. Kijk, kijk, kijk, kijk, kijk,"}, {"id": 147, "start": 298.11, "end": 298.12, "formattedStart": "04:58", "formattedEnd": "04:58", "text": "beginnen. Kijk, kijk, kijk, kijk, kijk,"}, {"id": 148, "start": 298.12, "end": 299.07, "formattedStart": "04:58", "formattedEnd": "04:59", "text": "beginnen. Kijk, kijk, kijk, kijk, kijk, kijk,"}, {"id": 149, "start": 299.07, "end": 299.08, "formattedStart": "04:59", "formattedEnd": "04:59", "text": "kijk,"}, {"id": 150, "start": 299.08, "end": 300.99, "formattedStart": "04:59", "formattedEnd": "05:00", "text": "kijk, kijk. Ik ben <PERSON> benieuwd hoe het gaat"}, {"id": 151, "start": 300.99, "end": 301, "formattedStart": "05:00", "formattedEnd": "05:01", "text": "kijk. Ik ben <PERSON> benieuwd hoe het gaat"}, {"id": 152, "start": 301, "end": 308.479, "formattedStart": "05:01", "formattedEnd": "05:08", "text": "kijk. <PERSON><PERSON> ben <PERSON> benieuwd hoe het gaat lopen, jong<PERSON>."}, {"id": 153, "start": 308.479, "end": 311.27, "formattedStart": "05:08", "formattedEnd": "05:11", "text": "<PERSON><PERSON> jong<PERSON>, we lopen lekker. Oh,"}, {"id": 154, "start": 311.28, "end": 314.51, "formattedStart": "05:11", "formattedEnd": "05:14", "text": "<PERSON><PERSON> j<PERSON>, we lopen lekker. Oh, rennen eerste stukje. Ik heb ik ben niet"}, {"id": 155, "start": 314.51, "end": 314.52, "formattedStart": "05:14", "formattedEnd": "05:14", "text": "rennen eerste stukje. Ik heb ik ben niet"}, {"id": 156, "start": 314.52, "end": 318.67, "formattedStart": "05:14", "formattedEnd": "05:18", "text": "rennen eerste stukje. Ik heb ik ben niet zo'n type die ik wel heel blij voor,"}, {"id": 157, "start": 318.67, "end": 318.68, "formattedStart": "05:18", "formattedEnd": "05:18", "text": "zo'n type die ik wel heel blij voor,"}, {"id": 158, "start": 318.68, "end": 321.88, "formattedStart": "05:18", "formattedEnd": "05:21", "text": "zo'n type die ik wel heel blij voor, maar"}, {"id": 159, "start": 321.88, "end": 326.749, "formattedStart": "05:21", "formattedEnd": "05:26", "text": "hey, veel plezier. Jee."}, {"id": 160, "start": 326.759, "end": 328.629, "formattedStart": "05:26", "formattedEnd": "05:28", "text": "hey, veel plezier. <PERSON><PERSON>. <PERSON><PERSON>. We zijn er weer 3 km bijna,"}, {"id": 161, "start": 328.629, "end": 328.639, "formattedStart": "05:28", "formattedEnd": "05:28", "text": "Hoppa. We zijn er weer 3 km bijna,"}, {"id": 162, "start": 328.639, "end": 330.469, "formattedStart": "05:28", "formattedEnd": "05:30", "text": "Hoppa. We zijn er weer 3 km bijna, jongens. Als ik hem heel erg sportief"}, {"id": 163, "start": 330.469, "end": 330.479, "formattedStart": "05:30", "formattedEnd": "05:30", "text": "jongens. Als ik hem heel erg sportief"}, {"id": 164, "start": 330.479, "end": 331.55, "formattedStart": "05:30", "formattedEnd": "05:31", "text": "jongens. Als ik hem heel erg sportief afrond voor"}, {"id": 165, "start": 331.55, "end": 331.56, "formattedStart": "05:31", "formattedEnd": "05:31", "text": "afrond voor"}, {"id": 166, "start": 331.56, "end": 336.189, "formattedStart": "05:31", "formattedEnd": "05:36", "text": "afrond voor mezelf. Ok<PERSON>, we lopen nu 408 ongeveer"}, {"id": 167, "start": 336.189, "end": 336.199, "formattedStart": "05:36", "formattedEnd": "05:36", "text": "mezel<PERSON>. <PERSON><PERSON>, we lopen nu 408 ongeveer"}, {"id": 168, "start": 336.199, "end": 338.749, "formattedStart": "05:36", "formattedEnd": "05:38", "text": "mezel<PERSON>. <PERSON><PERSON>, we lopen nu 408 ongeveer zoiets. <PERSON><PERSON> eh de eerste paar eh"}, {"id": 169, "start": 338.749, "end": 338.759, "formattedStart": "05:38", "formattedEnd": "05:38", "text": "zoiets. <PERSON>s eh de e<PERSON>te paar eh"}, {"id": 170, "start": 338.759, "end": 341.27, "formattedStart": "05:38", "formattedEnd": "05:41", "text": "zoiets. Dus eh de e<PERSON>te paar eh kilometers gaan we gewoon op een eh ja,"}, {"id": 171, "start": 341.27, "end": 341.28, "formattedStart": "05:41", "formattedEnd": "05:41", "text": "kilometers gaan we gewoon op een eh ja,"}, {"id": 172, "start": 341.28, "end": 343.43, "formattedStart": "05:41", "formattedEnd": "05:43", "text": "kilometers gaan we gewoon op een eh ja, niet gek tempo weg. <PERSON><PERSON><PERSON> willen we"}, {"id": 173, "start": 343.43, "end": 343.44, "formattedStart": "05:43", "formattedEnd": "05:43", "text": "niet gek tempo weg. <PERSON><PERSON><PERSON> willen we"}, {"id": 174, "start": 343.44, "end": 344.95, "formattedStart": "05:43", "formattedEnd": "05:44", "text": "niet gek tempo weg. <PERSON><PERSON><PERSON> willen we ietsje versnellen om een soort eh mooie"}, {"id": 175, "start": 344.95, "end": 344.96, "formattedStart": "05:44", "formattedEnd": "05:44", "text": "i<PERSON>je versnellen om een soort eh mooie"}, {"id": 176, "start": 344.96, "end": 346.87, "formattedStart": "05:44", "formattedEnd": "05:46", "text": "ietsje versnellen om een soort eh mooie intervaltraining van te maken. Dus we"}, {"id": 177, "start": 346.87, "end": 346.88, "formattedStart": "05:46", "formattedEnd": "05:46", "text": "intervaltraining van te maken. Du<PERSON> we"}, {"id": 178, "start": 346.88, "end": 348.35, "formattedStart": "05:46", "formattedEnd": "05:48", "text": "intervaltraining van te maken. Dus we gaan het zien. We lopen lekker door de"}, {"id": 179, "start": 348.35, "end": 348.36, "formattedStart": "05:48", "formattedEnd": "05:48", "text": "gaan het zien. We lopen lekker door de"}, {"id": 180, "start": 348.36, "end": 350.95, "formattedStart": "05:48", "formattedEnd": "05:50", "text": "gaan het zien. We lopen lekker door de straten van Breda. Ziet er goed uit."}, {"id": 181, "start": 350.95, "end": 350.96, "formattedStart": "05:50", "formattedEnd": "05:50", "text": "strate<PERSON> van Breda. Ziet er goed uit."}, {"id": 182, "start": 350.96, "end": 353.79, "formattedStart": "05:50", "formattedEnd": "05:53", "text": "strate<PERSON> van Breda. Ziet er goed uit. <PERSON><PERSON>, komen net Kilm 4 door. Vier? Ja."}, {"id": 183, "start": 353.79, "end": 353.8, "formattedStart": "05:53", "formattedEnd": "05:53", "text": "<PERSON><PERSON>, komen net <PERSON>lm 4 door. Vier? Ja."}, {"id": 184, "start": 353.8, "end": 358.07, "formattedStart": "05:53", "formattedEnd": "05:58", "text": "<PERSON><PERSON>, komen net Kilm 4 door. Vier? Ja. 404 op km 4. Nou hey d eh het loopt"}, {"id": 185, "start": 358.07, "end": 358.08, "formattedStart": "05:58", "formattedEnd": "05:58", "text": "404 op km 4. Nou hey d eh het loopt"}, {"id": 186, "start": 358.08, "end": 359.11, "formattedStart": "05:58", "formattedEnd": "05:59", "text": "404 op km 4. Nou hey d eh het loopt allemaal nog"}, {"id": 187, "start": 359.11, "end": 359.12, "formattedStart": "05:59", "formattedEnd": "05:59", "text": "allemaal nog"}, {"id": 188, "start": 359.12, "end": 361.309, "formattedStart": "05:59", "formattedEnd": "06:01", "text": "allemaal nog comfortabel en eh er is nog een lange"}, {"id": 189, "start": 361.309, "end": 361.319, "formattedStart": "06:01", "formattedEnd": "06:01", "text": "comfortabel en eh er is nog een lange"}, {"id": 190, "start": 361.319, "end": 364.11, "formattedStart": "06:01", "formattedEnd": "06:04", "text": "<PERSON>abel en eh er is nog een lange reist voor ons te gaan toch <PERSON>? Heel"}, {"id": 191, "start": 364.11, "end": 364.12, "formattedStart": "06:04", "formattedEnd": "06:04", "text": "reist voor ons te gaan toch <PERSON>? He<PERSON>"}, {"id": 192, "start": 364.12, "end": 365.83, "formattedStart": "06:04", "formattedEnd": "06:05", "text": "reist voor ons te gaan toch <PERSON>? Heel lange reis. Heel lange reis. Maar we"}, {"id": 193, "start": 365.83, "end": 365.84, "formattedStart": "06:05", "formattedEnd": "06:05", "text": "lange reis. Heel lange reis. Maar we"}, {"id": 194, "start": 365.84, "end": 367.629, "formattedStart": "06:05", "formattedEnd": "06:07", "text": "lange reis. Heel lange reis. Maar we hebben camper niet. <PERSON>t kijken joh."}, {"id": 195, "start": 367.629, "end": 367.639, "formattedStart": "06:07", "formattedEnd": "06:07", "text": "hebben camper niet. <PERSON>t kijken joh."}, {"id": 196, "start": 367.639, "end": 371.87, "formattedStart": "06:07", "formattedEnd": "06:11", "text": "hebben camper niet. <PERSON>t kijken joh. Zelf hou niet op zelf."}, {"id": 197, "start": 371.87, "end": 371.88, "formattedStart": "06:11", "formattedEnd": "06:11", "text": "Zelf hou niet op zelf."}, {"id": 198, "start": 371.88, "end": 379.88, "formattedStart": "06:11", "formattedEnd": "06:19", "text": "Zelf hou niet op zelf. Tijd voor een mooi"}, {"id": 199, "start": 379.88, "end": 382.96, "formattedStart": "06:19", "formattedEnd": "06:22", "text": "h."}, {"id": 200, "start": 382.96, "end": 386.96, "formattedStart": "06:22", "formattedEnd": "06:26", "text": "Eerste split in 20 minuten"}, {"id": 201, "start": 386.96, "end": 396.15999999999997, "formattedStart": "06:26", "formattedEnd": "06:36", "text": "jongens. Ki<PERSON>."}, {"id": 202, "start": 396.15999999999997, "end": 397.749, "formattedStart": "06:36", "formattedEnd": "06:37", "text": "<PERSON><PERSON>, eerste vijf zitten erop. We gaan nu"}, {"id": 203, "start": 397.759, "end": 400.909, "formattedStart": "06:37", "formattedEnd": "06:40", "text": "<PERSON><PERSON>, e<PERSON>te vijf zitten erop. We gaan nu ietsje harder. Eh 350 hoorde ik in de"}, {"id": 204, "start": 400.909, "end": 400.919, "formattedStart": "06:40", "formattedEnd": "06:40", "text": "i<PERSON><PERSON> harder. Eh 350 hoorde ik in de"}, {"id": 205, "start": 400.919, "end": 403.79, "formattedStart": "06:40", "formattedEnd": "06:43", "text": "i<PERSON><PERSON> harder. Eh 350 hoorde ik in de wandelgangen. Dus eh wat gaan we doen?"}, {"id": 206, "start": 403.79, "end": 403.8, "formattedStart": "06:43", "formattedEnd": "06:43", "text": "wandelgangen. Dus eh wat gaan we doen?"}, {"id": 207, "start": 403.8, "end": 407.23, "formattedStart": "06:43", "formattedEnd": "06:47", "text": "wandelgangen. Dus eh wat gaan we doen? Gaan we rokokken"}, {"id": 208, "start": 407.23, "end": 407.24, "formattedStart": "06:47", "formattedEnd": "06:47", "text": "Gaan we rokokken"}, {"id": 209, "start": 407.24, "end": 410.43, "formattedStart": "06:47", "formattedEnd": "06:50", "text": "Gaan we rokokken 7 km gewoon zeggen j ik ben nog niet top"}, {"id": 210, "start": 410.43, "end": 410.44, "formattedStart": "06:50", "formattedEnd": "06:50", "text": "7 km gewoon zeggen j ik ben nog niet top"}, {"id": 211, "start": 410.44, "end": 413.07, "formattedStart": "06:50", "formattedEnd": "06:53", "text": "7 km gewoon zeggen j ik ben nog niet top top fit heb ik toch ook geen"}, {"id": 212, "start": 413.07, "end": 413.08, "formattedStart": "06:53", "formattedEnd": "06:53", "text": "top fit heb ik toch ook geen"}, {"id": 213, "start": 413.08, "end": 416.39, "formattedStart": "06:53", "formattedEnd": "06:56", "text": "top fit heb ik toch ook geen verwachtingen. Bam 10 km"}, {"id": 214, "start": 416.39, "end": 416.4, "formattedStart": "06:56", "formattedEnd": "06:56", "text": "verwachtingen. Bam 10 km"}, {"id": 215, "start": 416.4, "end": 421.189, "formattedStart": "06:56", "formattedEnd": "07:01", "text": "verwachtingen. Bam 10 km jongens. We zitten op 39 minuten. <PERSON><PERSON>"}, {"id": 216, "start": 421.189, "end": 421.199, "formattedStart": "07:01", "formattedEnd": "07:01", "text": "jongens. <PERSON> zitten op 39 minuten. <PERSON><PERSON>"}, {"id": 217, "start": 421.199, "end": 424.83, "formattedStart": "07:01", "formattedEnd": "07:04", "text": "jongens. We zitten op 39 minuten. Lekker doorlopen. Wat op kijk BN heeft het"}, {"id": 218, "start": 424.83, "end": 424.84, "formattedStart": "07:04", "formattedEnd": "07:04", "text": "doorlopen. Wat op kijk BN heeft het"}, {"id": 219, "start": 424.84, "end": 426.51, "formattedStart": "07:04", "formattedEnd": "07:06", "text": "doorlopen. Wat op kijk BN heeft het zwaar dat zien we allemaal is je eerste"}, {"id": 220, "start": 426.51, "end": 426.52, "formattedStart": "07:06", "formattedEnd": "07:06", "text": "zwaar dat zien we allemaal is je eerste"}, {"id": 221, "start": 426.52, "end": 429.43, "formattedStart": "07:06", "formattedEnd": "07:09", "text": "zwaar dat zien we allemaal is je eerste 10 km jongens. Och och jullie maar. Het"}, {"id": 222, "start": 429.43, "end": 429.44, "formattedStart": "07:09", "formattedEnd": "07:09", "text": "10 km jongens. Och och jullie maar. Het"}, {"id": 223, "start": 429.44, "end": 433.189, "formattedStart": "07:09", "formattedEnd": "07:13", "text": "10 km jongens. Och och jullie maar. Het is eh even niet man."}, {"id": 224, "start": 433.189, "end": 433.199, "formattedStart": "07:13", "formattedEnd": "07:13", "text": "is eh even niet man."}, {"id": 225, "start": 433.199, "end": 434.95, "formattedStart": "07:13", "formattedEnd": "07:14", "text": "is eh even niet man. <PERSON><PERSON> j<PERSON>, de groep wordt kleiner merk"}, {"id": 226, "start": 434.95, "end": 434.96, "formattedStart": "07:14", "formattedEnd": "07:14", "text": "<PERSON><PERSON>, de groep wordt kleiner merk"}, {"id": 227, "start": 434.96, "end": 436.95, "formattedStart": "07:14", "formattedEnd": "07:16", "text": "<PERSON><PERSON> j<PERSON>, de groep wordt kleiner merk ik. <PERSON>u, we lopen ook wel lekker door"}, {"id": 228, "start": 436.95, "end": 436.96, "formattedStart": "07:16", "formattedEnd": "07:16", "text": "ik. <PERSON><PERSON>, we lopen ook wel lekker door"}, {"id": 229, "start": 436.96, "end": 437.79, "formattedStart": "07:16", "formattedEnd": "07:17", "text": "ik. <PERSON><PERSON>, we lopen ook wel lekker door voor"}, {"id": 230, "start": 437.79, "end": 437.8, "formattedStart": "07:17", "formattedEnd": "07:17", "text": "voor"}, {"id": 231, "start": 437.8, "end": 442.469, "formattedStart": "07:17", "formattedEnd": "07:22", "text": "voor de voor de catching car. Catcher car."}, {"id": 232, "start": 442.469, "end": 442.479, "formattedStart": "07:22", "formattedEnd": "07:22", "text": "de voor de catching car. Catcher car."}, {"id": 233, "start": 442.479, "end": 443.589, "formattedStart": "07:22", "formattedEnd": "07:23", "text": "de voor de catching car. Catcher car. Get"}, {"id": 234, "start": 443.589, "end": 443.599, "formattedStart": "07:23", "formattedEnd": "07:23", "text": "Get"}, {"id": 235, "start": 443.599, "end": 446.029, "formattedStart": "07:23", "formattedEnd": "07:26", "text": "Get allal. Deze mannen lopen nog net kunnen"}, {"id": 236, "start": 446.029, "end": 446.039, "formattedStart": "07:26", "formattedEnd": "07:26", "text": "allal. Deze mannen lopen nog net kunnen"}, {"id": 237, "start": 446.039, "end": 449.469, "formattedStart": "07:26", "formattedEnd": "07:29", "text": "allal. Deze mannen lopen nog net kunnen net aanraken. Ketchup. Ketchup car. Z"}, {"id": 238, "start": 449.469, "end": 449.479, "formattedStart": "07:29", "formattedEnd": "07:29", "text": "net aanraken. Ketchup. Ketchup car. Z"}, {"id": 239, "start": 449.479, "end": 457.36, "formattedStart": "07:29", "formattedEnd": "07:37", "text": "net aanraken. Ketchup. Ketchup car. Z kilometer uit 13. 13 ongeveer. Yes."}, {"id": 240, "start": 457.36, "end": 460.869, "formattedStart": "07:37", "formattedEnd": "07:40", "text": "15 km. Pam. Hoppa."}, {"id": 241, "start": 460.879, "end": 462.55, "formattedStart": "07:40", "formattedEnd": "07:42", "text": "15 km. Pam. Hoppa. 15 km."}, {"id": 242, "start": 462.55, "end": 462.56, "formattedStart": "07:42", "formattedEnd": "07:42", "text": "15 km."}, {"id": 243, "start": 462.56, "end": 465.79, "formattedStart": "07:42", "formattedEnd": "07:45", "text": "15 km. We zitten op 57 minuten"}, {"id": 244, "start": 465.79, "end": 465.8, "formattedStart": "07:45", "formattedEnd": "07:45", "text": "We zitten op 57 minuten"}, {"id": 245, "start": 465.8, "end": 468.27, "formattedStart": "07:45", "formattedEnd": "07:48", "text": "We zitten op 57 minuten jongens. Hoe gaat hij? <PERSON><PERSON>, <PERSON><PERSON><PERSON>"}, {"id": 246, "start": 468.27, "end": 468.28, "formattedStart": "07:48", "formattedEnd": "07:48", "text": "jongens. Hoe gaat hij? <PERSON><PERSON>, <PERSON><PERSON><PERSON>"}, {"id": 247, "start": 468.28, "end": 469.99, "formattedStart": "07:48", "formattedEnd": "07:49", "text": "jongens. Hoe gaat hij? <PERSON><PERSON>, <PERSON><PERSON><PERSON> goed. Ik heb alleen slecht nieuws, maar"}, {"id": 248, "start": 469.99, "end": 470, "formattedStart": "07:49", "formattedEnd": "07:50", "text": "goed. Ik heb alleen slecht nieuws, maar"}, {"id": 249, "start": 470, "end": 472.07, "formattedStart": "07:50", "formattedEnd": "07:52", "text": "goed. Ik heb alleen slecht nieuws, maar beter is het losgega<PERSON>. Ga nu even"}, {"id": 250, "start": 472.07, "end": 472.08, "formattedStart": "07:52", "formattedEnd": "07:52", "text": "beter is het los<PERSON>gaan. Ga nu even"}, {"id": 251, "start": 472.08, "end": 473.95, "formattedStart": "07:52", "formattedEnd": "07:53", "text": "beter is het losgegaan. Ga nu even strikken. Succes<PERSON>, nog met zijn"}, {"id": 252, "start": 473.95, "end": 473.96, "formattedStart": "07:53", "formattedEnd": "07:53", "text": "strikken. Succes. <PERSON>, nog met zijn"}, {"id": 253, "start": 473.96, "end": 476.909, "formattedStart": "07:53", "formattedEnd": "07:56", "text": "strikken. Succes<PERSON>, nog met zijn drie over. Hoppa. 200m. Wie wordt de"}, {"id": 254, "start": 476.909, "end": 476.919, "formattedStart": "07:56", "formattedEnd": "07:56", "text": "drie over. <PERSON><PERSON>. 200m. Wie wordt de"}, {"id": 255, "start": 476.919, "end": 479.83, "formattedStart": "07:56", "formattedEnd": "07:59", "text": "drie over. <PERSON><PERSON>. 200m. Wie wordt de volgende? Wie wordt de volgende?"}, {"id": 256, "start": 479.83, "end": 479.84000000000003, "formattedStart": "07:59", "formattedEnd": "07:59", "text": "volgende? Wie wordt de volgende?"}, {"id": 257, "start": 479.84000000000003, "end": 482.07, "formattedStart": "07:59", "formattedEnd": "08:02", "text": "volgende? Wie wordt de volgende? Ja, maar als je Ho n het is gelijk windn"}, {"id": 258, "start": 482.07, "end": 482.08, "formattedStart": "08:02", "formattedEnd": "08:02", "text": "<PERSON><PERSON>, maar als je Ho n het is gelijk windn"}, {"id": 259, "start": 482.08, "end": 485.35, "formattedStart": "08:02", "formattedEnd": "08:05", "text": "<PERSON><PERSON>, maar als je Ho n het is gelijk windn tegen. Maar we gaan nu 335 p. Dus eh we"}, {"id": 260, "start": 485.35, "end": 485.36, "formattedStart": "08:05", "formattedEnd": "08:05", "text": "tegen. Maar we gaan nu 335 p. Dus eh we"}, {"id": 261, "start": 485.36, "end": 488.44, "formattedStart": "08:05", "formattedEnd": "08:08", "text": "tegen. Maar we gaan nu 335 p. Dus eh we gaan even flink"}, {"id": 262, "start": 488.44, "end": 490.27, "formattedStart": "08:08", "formattedEnd": "08:10", "text": "doorlopen."}, {"id": 263, "start": 490.28, "end": 494.029, "formattedStart": "08:10", "formattedEnd": "08:14", "text": "doorlopen. Feyenoord 16. We gaan nu even 335 p"}, {"id": 264, "start": 494.029, "end": 494.039, "formattedStart": "08:14", "formattedEnd": "08:14", "text": "Feyenoord 16. We gaan nu even 335 p"}, {"id": 265, "start": 494.039, "end": 496.99, "formattedStart": "08:14", "formattedEnd": "08:16", "text": "Feyenoord 16. We gaan nu even 335 p lopen en dan progressief wat harder. Dan"}, {"id": 266, "start": 496.99, "end": 497, "formattedStart": "08:16", "formattedEnd": "08:17", "text": "lopen en dan progressief wat harder. Dan"}, {"id": 267, "start": 497, "end": 498.149, "formattedStart": "08:17", "formattedEnd": "08:18", "text": "lopen en dan progressief wat harder. Dan kijken we hoe ver we"}, {"id": 268, "start": 498.149, "end": 498.159, "formattedStart": "08:18", "formattedEnd": "08:18", "text": "kijken we hoe ver we"}, {"id": 269, "start": 498.159, "end": 500.869, "formattedStart": "08:18", "formattedEnd": "08:20", "text": "kijken we hoe ver we komen. Lopen om naar bijij. Kilom 18"}, {"id": 270, "start": 500.869, "end": 500.879, "formattedStart": "08:20", "formattedEnd": "08:20", "text": "komen. Lopen om naar bijij. Kilom 18"}, {"id": 271, "start": 500.879, "end": 505.67, "formattedStart": "08:20", "formattedEnd": "08:25", "text": "komen. Lopen om naar bijij. Kilom 18 denk ik. 18 ongeveer. Zitten nu op 330p."}, {"id": 272, "start": 505.67, "end": 505.68, "formattedStart": "08:25", "formattedEnd": "08:25", "text": "denk ik. 18 ongeveer. Zitten nu op 330p."}, {"id": 273, "start": 505.68, "end": 507.909, "formattedStart": "08:25", "formattedEnd": "08:27", "text": "denk ik. 18 ongeveer. Zitten nu op 330p. We hebben even een eh blokje tempo"}, {"id": 274, "start": 507.909, "end": 507.919, "formattedStart": "08:27", "formattedEnd": "08:27", "text": "We hebben even een eh blokje tempo"}, {"id": 275, "start": 507.919, "end": 510.83, "formattedStart": "08:27", "formattedEnd": "08:30", "text": "We hebben even een eh blokje tempo ingebouwd. <PERSON><PERSON> leuk."}, {"id": 276, "start": 510.83, "end": 510.84, "formattedStart": "08:30", "formattedEnd": "08:30", "text": "ingebouwd. <PERSON><PERSON> leuk."}, {"id": 277, "start": 510.84, "end": 512.829, "formattedStart": "08:30", "formattedEnd": "08:32", "text": "ingebouwd. <PERSON><PERSON> leuk. <PERSON><PERSON>, we hebben ook een nieuwe logo"}, {"id": 278, "start": 512.829, "end": 512.8389999999999, "formattedStart": "08:32", "formattedEnd": "08:32", "text": "<PERSON><PERSON>, we hebben ook een nieuwe logo"}, {"id": 279, "start": 512.8389999999999, "end": 516.599, "formattedStart": "08:32", "formattedEnd": "08:36", "text": "<PERSON><PERSON>, we hebben ook een nieuwe logo erbij."}, {"id": 280, "start": 516.599, "end": 519.07, "formattedStart": "08:36", "formattedEnd": "08:39", "text": "332 gemiddeld voor de"}, {"id": 281, "start": 519.08, "end": 521.75, "formattedStart": "08:39", "formattedEnd": "08:41", "text": "332 gemiddeld voor de volgende. Ja, het waren 3 km ongeveer en"}, {"id": 282, "start": 521.75, "end": 521.76, "formattedStart": "08:41", "formattedEnd": "08:41", "text": "volgende. Ja, het waren 3 km ongeveer en"}, {"id": 283, "start": 521.76, "end": 522.79, "formattedStart": "08:41", "formattedEnd": "08:42", "text": "volgende. Ja, het waren 3 km ongeveer en we mogen niet weer harder. Dus we gaan"}, {"id": 284, "start": 522.79, "end": 522.8, "formattedStart": "08:42", "formattedEnd": "08:42", "text": "we mogen niet weer harder. Dus we gaan"}, {"id": 285, "start": 522.8, "end": 525.79, "formattedStart": "08:42", "formattedEnd": "08:45", "text": "we mogen niet weer harder. <PERSON><PERSON> we gaan nu naar 32 25. 325 zegt de coach. <PERSON><PERSON>"}, {"id": 286, "start": 525.79, "end": 525.8, "formattedStart": "08:45", "formattedEnd": "08:45", "text": "nu naar 32 25. 325 zegt de coach. <PERSON><PERSON>"}, {"id": 287, "start": 525.8, "end": 527.91, "formattedStart": "08:45", "formattedEnd": "08:47", "text": "nu naar 32 25. 325 zegt de coach. <PERSON><PERSON> zie het aan zijn loop. Wie is het"}, {"id": 288, "start": 527.91, "end": 527.92, "formattedStart": "08:47", "formattedEnd": "08:47", "text": "zie het aan zijn loop. Wie is het"}, {"id": 289, "start": 527.92, "end": 530.47, "formattedStart": "08:47", "formattedEnd": "08:50", "text": "zie het aan zijn loop. Wie is het eigenlijk? We gaan echt even emmiel."}, {"id": 290, "start": 530.47, "end": 530.48, "formattedStart": "08:50", "formattedEnd": "08:50", "text": "eigenlijk? We gaan echt even emmiel."}, {"id": 291, "start": 530.48, "end": 533.15, "formattedStart": "08:50", "formattedEnd": "08:53", "text": "eigenlijk? We gaan echt even emmiel. Emiel. Dit is <PERSON>iel. Emiel. Oh, nee. Is"}, {"id": 292, "start": 533.15, "end": 533.16, "formattedStart": "08:53", "formattedEnd": "08:53", "text": "Emiel. Dit is Emiel. Emiel. Oh, nee. Is"}, {"id": 293, "start": 533.16, "end": 535.79, "formattedStart": "08:53", "formattedEnd": "08:55", "text": "Em<PERSON>. Dit is <PERSON><PERSON>. <PERSON><PERSON>. Oh, nee. Is <PERSON>iel niet. <PERSON><PERSON><PERSON>. Weet ik niet. Is hem."}, {"id": 294, "start": 535.79, "end": 535.8, "formattedStart": "08:55", "formattedEnd": "08:55", "text": "<PERSON><PERSON> niet. <PERSON><PERSON><PERSON>. Weet ik niet. Is hem."}, {"id": 295, "start": 535.8, "end": 539.8389999999999, "formattedStart": "08:55", "formattedEnd": "08:59", "text": "<PERSON><PERSON> niet. <PERSON><PERSON><PERSON>. Weet ik niet. Is hem. Jawel. Emiel."}, {"id": 296, "start": 539.8389999999999, "end": 546.12, "formattedStart": "08:59", "formattedEnd": "09:06", "text": "Kom op"}, {"id": 297, "start": 546.12, "end": 548.59, "formattedStart": "09:06", "formattedEnd": "09:08", "text": "jongen. <PERSON><PERSON> applaus voor de mensen hier"}, {"id": 298, "start": 548.6, "end": 549.79, "formattedStart": "09:08", "formattedEnd": "09:09", "text": "jongen. <PERSON><PERSON> applaus voor de mensen hier langs de kant die lekker aan de koffie"}, {"id": 299, "start": 549.79, "end": 549.8, "formattedStart": "09:09", "formattedEnd": "09:09", "text": "langs de kant die lekker aan de koffie"}, {"id": 300, "start": 549.8, "end": 577.88, "formattedStart": "09:09", "formattedEnd": "09:37", "text": "langs de kant die lekker aan de koffie zitten. Ja toch?"}, {"id": 301, "start": 577.88, "end": 579.59, "formattedStart": "09:37", "formattedEnd": "09:39", "text": "<PERSON><PERSON> is over de halve maen"}, {"id": 302, "start": 579.6, "end": 582.509, "formattedStart": "09:39", "formattedEnd": "09:42", "text": "<PERSON><PERSON> is over de halve maen 221. We gaan bijna de laatste 3 km tempo"}, {"id": 303, "start": 582.509, "end": 582.519, "formattedStart": "09:42", "formattedEnd": "09:42", "text": "221. <PERSON> gaan bijna de la<PERSON>te 3 km tempo"}, {"id": 304, "start": 582.519, "end": 585.389, "formattedStart": "09:42", "formattedEnd": "09:45", "text": "221. We gaan bijna de laatste 3 km tempo eh blok in. Die moet rond de 315 zijn"}, {"id": 305, "start": 585.389, "end": 585.399, "formattedStart": "09:45", "formattedEnd": "09:45", "text": "eh blok in. Die moet rond de 315 zijn"}, {"id": 306, "start": 585.399, "end": 586.99, "formattedStart": "09:45", "formattedEnd": "09:46", "text": "eh blok in. Die moet rond de 315 zijn van de coach."}, {"id": 307, "start": 586.99, "end": 587, "formattedStart": "09:46", "formattedEnd": "09:47", "text": "<PERSON> de coach."}, {"id": 308, "start": 587, "end": 588.11, "formattedStart": "09:47", "formattedEnd": "09:48", "text": "van de coach. We gaan even kijken wat gaat lukken,"}, {"id": 309, "start": 588.11, "end": 588.12, "formattedStart": "09:48", "formattedEnd": "09:48", "text": "We gaan even kijken wat gaat lukken,"}, {"id": 310, "start": 588.12, "end": 589.43, "formattedStart": "09:48", "formattedEnd": "09:49", "text": "We gaan even kijken wat gaat lukken, want ik begin het een beetje z<PERSON>er te"}, {"id": 311, "start": 589.43, "end": 589.44, "formattedStart": "09:49", "formattedEnd": "09:49", "text": "want ik begin het een beetje z<PERSON>er te"}, {"id": 312, "start": 589.44, "end": 594.35, "formattedStart": "09:49", "formattedEnd": "09:54", "text": "want ik begin het een beetje z<PERSON>er te krijgen nu. <PERSON><PERSON> goed, we lopen nu 310."}, {"id": 313, "start": 594.35, "end": 594.36, "formattedStart": "09:54", "formattedEnd": "09:54", "text": "krijgen nu. <PERSON><PERSON> goed, we lopen nu 310."}, {"id": 314, "start": 594.36, "end": 595.829, "formattedStart": "09:54", "formattedEnd": "09:55", "text": "krijgen nu. <PERSON><PERSON> goed, we lopen nu 310. Zit op kilm"}, {"id": 315, "start": 595.829, "end": 595.8389999999999, "formattedStart": "09:55", "formattedEnd": "09:55", "text": "Zit op kilm"}, {"id": 316, "start": 595.8389999999999, "end": 598.829, "formattedStart": "09:55", "formattedEnd": "09:58", "text": "Zit op kilm 23. <PERSON><PERSON>, zullen we wel even doorwikkelen"}, {"id": 317, "start": 598.829, "end": 598.8389999999999, "formattedStart": "09:58", "formattedEnd": "09:58", "text": "23. <PERSON><PERSON>, zullen we wel even doorwikkelen"}, {"id": 318, "start": 598.8389999999999, "end": 612.839, "formattedStart": "09:58", "formattedEnd": "10:12", "text": "23. <PERSON><PERSON>, zullen we wel even doorwikkelen nu,"}, {"id": 319, "start": 612.839, "end": 616.19, "formattedStart": "10:12", "formattedEnd": "10:16", "text": "man. <PERSON><PERSON> lekker."}, {"id": 320, "start": 616.2, "end": 620.509, "formattedStart": "10:16", "formattedEnd": "10:20", "text": "man. <PERSON><PERSON> lekker. Ba<PERSON><PERSON> <PERSON>, lekker. Bam. 25 km op drie"}, {"id": 321, "start": 620.509, "end": 620.519, "formattedStart": "10:20", "formattedEnd": "10:20", "text": "Ba<PERSON><PERSON> <PERSON><PERSON>, lekker. Bam. 25 km op drie"}, {"id": 322, "start": 620.519, "end": 622.67, "formattedStart": "10:20", "formattedEnd": "10:22", "text": "<PERSON><PERSON><PERSON> <PERSON><PERSON>, lekker. Bam. 25 km op drie hoeveel gemiddeld? 340 ofzo. Ongeveer"}, {"id": 323, "start": 622.67, "end": 622.68, "formattedStart": "10:22", "formattedEnd": "10:22", "text": "hoeveel gemiddeld? 340 ofzo. Ongeveer"}, {"id": 324, "start": 622.68, "end": 626.59, "formattedStart": "10:22", "formattedEnd": "10:26", "text": "hoeveel gemiddeld? 340 ofzo. Ongeveer 340 gemiddeld met de laatste 9 km"}, {"id": 325, "start": 626.59, "end": 626.6, "formattedStart": "10:26", "formattedEnd": "10:26", "text": "340 gemiddeld met de laatste 9 km"}, {"id": 326, "start": 626.6, "end": 630.35, "formattedStart": "10:26", "formattedEnd": "10:30", "text": "340 gemiddeld met de laatste 9 km positie vanaf 325 naar 3 15. 335 naar"}, {"id": 327, "start": 630.35, "end": 630.36, "formattedStart": "10:30", "formattedEnd": "10:30", "text": "positie vanaf 325 naar 3 15. 335 naar"}, {"id": 328, "start": 630.36, "end": 633.03, "formattedStart": "10:30", "formattedEnd": "10:33", "text": "positie vanaf 325 naar 3 15. 335 naar 315. <PERSON><PERSON> eh lekker gelopen. Nu gaan we"}, {"id": 329, "start": 633.03, "end": 633.04, "formattedStart": "10:33", "formattedEnd": "10:33", "text": "315. <PERSON><PERSON> eh lekker gelopen. Nu gaan we"}, {"id": 330, "start": 633.04, "end": 634.47, "formattedStart": "10:33", "formattedEnd": "10:34", "text": "315. <PERSON><PERSON> eh lekker gelopen. Nu gaan we even kijken hoe we hoe we het terug gaan"}, {"id": 331, "start": 634.47, "end": 634.48, "formattedStart": "10:34", "formattedEnd": "10:34", "text": "even kijken hoe we hoe we het terug gaan"}, {"id": 332, "start": 634.48, "end": 636.949, "formattedStart": "10:34", "formattedEnd": "10:36", "text": "even kijken hoe we hoe we het terug gaan eh doen, maar zien we zo wel. <PERSON><PERSON>, we"}, {"id": 333, "start": 636.949, "end": 636.9590000000001, "formattedStart": "10:36", "formattedEnd": "10:36", "text": "eh doen, maar zien we zo wel. <PERSON><PERSON>, we"}, {"id": 334, "start": 636.9590000000001, "end": 638.31, "formattedStart": "10:36", "formattedEnd": "10:38", "text": "eh doen, maar zien we zo wel. <PERSON><PERSON>, we lopen de laatste kilometers we even"}, {"id": 335, "start": 638.31, "end": 638.32, "formattedStart": "10:38", "formattedEnd": "10:38", "text": "lopen de laatste kilometers we even"}, {"id": 336, "start": 638.32, "end": 640.47, "formattedStart": "10:38", "formattedEnd": "10:40", "text": "lopen de laatste kilometers we even terug. Zal weer weer bij de startfish"}, {"id": 337, "start": 640.47, "end": 640.48, "formattedStart": "10:40", "formattedEnd": "10:40", "text": "terug. <PERSON>al weer weer bij de startfish"}, {"id": 338, "start": 640.48, "end": 642.59, "formattedStart": "10:40", "formattedEnd": "10:42", "text": "terug. Zal weer weer bij de startfish komen. Dat is heerlijk om zo te lopen."}, {"id": 339, "start": 642.59, "end": 642.6, "formattedStart": "10:42", "formattedEnd": "10:42", "text": "komen. Dat is heerlijk om zo te lopen."}, {"id": 340, "start": 642.6, "end": 645.47, "formattedStart": "10:42", "formattedEnd": "10:45", "text": "komen. Dat is he<PERSON><PERSON>jk om zo te lopen. <PERSON><PERSON>, deze mensen lopen allemaal nog."}, {"id": 341, "start": 645.47, "end": 645.48, "formattedStart": "10:45", "formattedEnd": "10:45", "text": "<PERSON><PERSON>, deze mensen lopen allemaal nog."}, {"id": 342, "start": 645.48, "end": 649.55, "formattedStart": "10:45", "formattedEnd": "10:49", "text": "<PERSON><PERSON>, deze mensen lopen allemaal nog. Dan lopen die jongens tegenstelde"}, {"id": 343, "start": 649.55, "end": 649.56, "formattedStart": "10:49", "formattedEnd": "10:49", "text": "<PERSON> lopen die jongens tegenstelde"}, {"id": 344, "start": 649.56, "end": 652.71, "formattedStart": "10:49", "formattedEnd": "10:52", "text": "<PERSON> lopen die jongens tegenstelde richting. Lopen al die kanjes"}, {"id": 345, "start": 652.71, "end": 652.72, "formattedStart": "10:52", "formattedEnd": "10:52", "text": "richting. Lopen al die kanjes"}, {"id": 346, "start": 652.72, "end": 656.43, "formattedStart": "10:52", "formattedEnd": "10:56", "text": "richting. Lopen al die kanjes nog. Dus wel even lachen om eh even"}, {"id": 347, "start": 656.43, "end": 656.44, "formattedStart": "10:56", "formattedEnd": "10:56", "text": "nog. <PERSON><PERSON> wel even lachen om eh even"}, {"id": 348, "start": 656.44, "end": 657.55, "formattedStart": "10:56", "formattedEnd": "10:57", "text": "nog. Dus wel even lachen om eh even lekker gelopen te hebben. Dus we lopen"}, {"id": 349, "start": 657.55, "end": 657.56, "formattedStart": "10:57", "formattedEnd": "10:57", "text": "lekker gelopen te hebben. Dus we lopen"}, {"id": 350, "start": 657.56, "end": 659.59, "formattedStart": "10:57", "formattedEnd": "10:59", "text": "lekker gelopen te hebben. Dus we lopen nu rustig weer terug naar de start"}, {"id": 351, "start": 659.59, "end": 659.6, "formattedStart": "10:59", "formattedEnd": "10:59", "text": "nu rustig weer terug naar de start"}, {"id": 352, "start": 659.6, "end": 662.23, "formattedStart": "10:59", "formattedEnd": "11:02", "text": "nu rustig weer terug naar de start finish. <PERSON>."}, {"id": 353, "start": 662.23, "end": 662.24, "formattedStart": "11:02", "formattedEnd": "11:02", "text": "finish. <PERSON>."}, {"id": 354, "start": 662.24, "end": 666.2, "formattedStart": "11:02", "formattedEnd": "11:06", "text": "finish. <PERSON>. Hey heet."}, {"id": 355, "start": 666.2, "end": 668.79, "formattedStart": "11:06", "formattedEnd": "11:08", "text": "<PERSON><PERSON>, de auto komt eraan. Ah,"}, {"id": 356, "start": 668.8, "end": 672.43, "formattedStart": "11:08", "formattedEnd": "11:12", "text": "<PERSON><PERSON>, de auto komt eraan. Ah, it's coming. It's coming closer. <PERSON><PERSON>, is"}, {"id": 357, "start": 672.43, "end": 672.44, "formattedStart": "11:12", "formattedEnd": "11:12", "text": "it's coming. It's coming closer. <PERSON><PERSON>, is"}, {"id": 358, "start": 672.44, "end": 674.23, "formattedStart": "11:12", "formattedEnd": "11:14", "text": "it's coming. It's coming closer. <PERSON><PERSON>, is onverbiddelijk, hè. Oh, dat ding is"}, {"id": 359, "start": 674.23, "end": 674.24, "formattedStart": "11:14", "formattedEnd": "11:14", "text": "onverbiddelijk, hè. Oh, dat ding is"}, {"id": 360, "start": 674.24, "end": 682.2, "formattedStart": "11:14", "formattedEnd": "11:22", "text": "onver<PERSON><PERSON><PERSON><PERSON>, hè. Oh, dat ding is hardcore jongens. Oppassen."}, {"id": 361, "start": 682.2, "end": 685.03, "formattedStart": "11:22", "formattedEnd": "11:25", "text": "Komt de auto aan. Hij komt hij aan"}, {"id": 362, "start": 685.04, "end": 692.76, "formattedStart": "11:25", "formattedEnd": "11:32", "text": "Komt de auto aan. Hij komt hij aan jongens."}, {"id": 363, "start": 692.76, "end": 694.269, "formattedStart": "11:32", "formattedEnd": "11:34", "text": "Dan moeten we even een stukje vooruit"}, {"id": 364, "start": 694.279, "end": 697.8, "formattedStart": "11:34", "formattedEnd": "11:37", "text": "Dan moeten we even een stukje vooruit lopen natuurlijk."}, {"id": 365, "start": 697.8, "end": 699.35, "formattedStart": "11:37", "formattedEnd": "11:39", "text": "<PERSON><PERSON>."}, {"id": 366, "start": 699.36, "end": 702.31, "formattedStart": "11:39", "formattedEnd": "11:42", "text": "<PERSON><PERSON><PERSON>, goed gedaan. <PERSON><PERSON> is hij. <PERSON>ar"}, {"id": 367, "start": 702.31, "end": 702.32, "formattedStart": "11:42", "formattedEnd": "11:42", "text": "<PERSON><PERSON>, goed gedaan. <PERSON><PERSON> is hij. <PERSON>ar"}, {"id": 368, "start": 702.32, "end": 705.79, "formattedStart": "11:42", "formattedEnd": "11:45", "text": "<PERSON><PERSON>, goed gedaan. <PERSON><PERSON> is hij. <PERSON>ar zijn we dan. Zit er zit er. Ho, pas op"}, {"id": 369, "start": 705.79, "end": 705.8, "formattedStart": "11:45", "formattedEnd": "11:45", "text": "zijn we dan. Zit er zit er. Ho, pas op"}, {"id": 370, "start": 705.8, "end": 709.15, "formattedStart": "11:45", "formattedEnd": "11:49", "text": "zijn we dan. Zit er zit er. Ho, pas op die camera. Pas op. Ja, ja, ja. Hou ons"}, {"id": 371, "start": 709.15, "end": 709.16, "formattedStart": "11:49", "formattedEnd": "11:49", "text": "die camera. Pas op. Ja, ja, ja. Hou ons"}, {"id": 372, "start": 709.16, "end": 726.36, "formattedStart": "11:49", "formattedEnd": "12:06", "text": "die camera. Pas op. Ja, ja, ja. Hou ons maar in. Hou on maar"}, {"id": 373, "start": 726.36, "end": 730.71, "formattedStart": "12:06", "formattedEnd": "12:10", "text": "in. Catch card. Is voorbij. Het is"}, {"id": 374, "start": 730.72, "end": 733.99, "formattedStart": "12:10", "formattedEnd": "12:13", "text": "in. Catch card. Is voorbij. Het is voorbij. Oh."}, {"id": 375, "start": 733.99, "end": 734, "formattedStart": "12:13", "formattedEnd": "12:14", "text": "voorbij. Oh."}, {"id": 376, "start": 734, "end": 740, "formattedStart": "12:14", "formattedEnd": "12:20", "text": "voorbij. <PERSON>. <PERSON>, handjes, handjes. Hey."}, {"id": 377, "start": 740, "end": 742.71, "formattedStart": "12:20", "formattedEnd": "12:22", "text": "<PERSON><PERSON>."}, {"id": 378, "start": 742.72, "end": 744.389, "formattedStart": "12:22", "formattedEnd": "12:24", "text": "Hoppa. En de"}, {"id": 379, "start": 744.389, "end": 744.399, "formattedStart": "12:24", "formattedEnd": "12:24", "text": "En de"}, {"id": 380, "start": 744.399, "end": 749.23, "formattedStart": "12:24", "formattedEnd": "12:29", "text": "En de laatste. <PERSON>jk, slagveld hier. Slagveld."}, {"id": 381, "start": 749.23, "end": 749.24, "formattedStart": "12:29", "formattedEnd": "12:29", "text": "laatste. Ki<PERSON>, slagveld hier. Slagveld."}, {"id": 382, "start": 749.24, "end": 751.03, "formattedStart": "12:29", "formattedEnd": "12:31", "text": "laatste. <PERSON><PERSON>, slagveld hier. Slagveld. <PERSON><PERSON>, we gaan weer richting de Wings for"}, {"id": 383, "start": 751.03, "end": 751.04, "formattedStart": "12:31", "formattedEnd": "12:31", "text": "<PERSON><PERSON>, we gaan weer richting de Wings for"}, {"id": 384, "start": 751.04, "end": 752.35, "formattedStart": "12:31", "formattedEnd": "12:32", "text": "<PERSON><PERSON>, we gaan weer richting de Wings for Life"}, {"id": 385, "start": 752.35, "end": 752.36, "formattedStart": "12:32", "formattedEnd": "12:32", "text": "Life"}, {"id": 386, "start": 752.36, "end": 755.79, "formattedStart": "12:32", "formattedEnd": "12:35", "text": "Life village. 35"}, {"id": 387, "start": 755.79, "end": 755.8, "formattedStart": "12:35", "formattedEnd": "12:35", "text": "village. 35"}, {"id": 388, "start": 755.8, "end": 758.47, "formattedStart": "12:35", "formattedEnd": "12:38", "text": "village. 35 km. Als je blij <PERSON> met je prestatie,"}, {"id": 389, "start": 758.47, "end": 758.48, "formattedStart": "12:38", "formattedEnd": "12:38", "text": "km. <PERSON>s je blij bent met je prestatie,"}, {"id": 390, "start": 758.48, "end": 761.269, "formattedStart": "12:38", "formattedEnd": "12:41", "text": "km. Als je blij bent met je prestatie, zek even een du. En ze is zelf ook nog"}, {"id": 391, "start": 761.269, "end": 761.279, "formattedStart": "12:41", "formattedEnd": "12:41", "text": "zek even een du. En ze is zelf ook nog"}, {"id": 392, "start": 761.279, "end": 763.269, "formattedStart": "12:41", "formattedEnd": "12:43", "text": "zek even een du. En ze is zelf ook nog aan het lopen. Zi<PERSON> was volgens mij de"}, {"id": 393, "start": 763.269, "end": 763.279, "formattedStart": "12:43", "formattedEnd": "12:43", "text": "aan het lopen. <PERSON><PERSON><PERSON> was volgens mij de"}, {"id": 394, "start": 763.279, "end": 768.15, "formattedStart": "12:43", "formattedEnd": "12:48", "text": "aan het lopen. <PERSON><PERSON><PERSON> was volgens mij de winnaar van 2015 of 2006. Op dit moment"}, {"id": 395, "start": 768.15, "end": 768.16, "formattedStart": "12:48", "formattedEnd": "12:48", "text": "winna<PERSON> van 2015 of 2006. Op dit moment"}, {"id": 396, "start": 768.16, "end": 770.389, "formattedStart": "12:48", "formattedEnd": "12:50", "text": "winnaar van 2015 of 2006. Op dit moment de nummer de Nederlandse nummer tw van"}, {"id": 397, "start": 770.389, "end": 770.399, "formattedStart": "12:50", "formattedEnd": "12:50", "text": "de nummer de Nederlandse nummer tw van"}, {"id": 398, "start": 770.399, "end": 772.23, "formattedStart": "12:50", "formattedEnd": "12:52", "text": "de nummer de Nederlandse nummer tw van de Rotterdam Marathon en de Nederlandse"}, {"id": 399, "start": 772.23, "end": 772.24, "formattedStart": "12:52", "formattedEnd": "12:52", "text": "de Rotterdam Marathon en de Nederlandse"}, {"id": 400, "start": 772.24, "end": 774.59, "formattedStart": "12:52", "formattedEnd": "12:54", "text": "de Rotterdam Marathon en de Nederlandse nummer vier van de Rotterdam Marathon"}, {"id": 401, "start": 774.59, "end": 774.6, "formattedStart": "12:54", "formattedEnd": "12:54", "text": "nummer vier van de Rotterdam Marathon"}, {"id": 402, "start": 774.6, "end": 778.189, "formattedStart": "12:54", "formattedEnd": "12:58", "text": "nummer vier van de Rotterdam Marathon <PERSON><PERSON>, <PERSON><PERSON> Emiel"}, {"id": 403, "start": 778.189, "end": 778.199, "formattedStart": "12:58", "formattedEnd": "12:58", "text": "<PERSON><PERSON>, <PERSON><PERSON> en <PERSON>"}, {"id": 404, "start": 778.199, "end": 779.829, "formattedStart": "12:58", "formattedEnd": "12:59", "text": "<PERSON><PERSON>, <PERSON><PERSON> en <PERSON><PERSON>."}, {"id": 405, "start": 779.829, "end": 779.8389999999999, "formattedStart": "12:59", "formattedEnd": "12:59", "text": "Berghout."}, {"id": 406, "start": 779.8389999999999, "end": 781.949, "formattedStart": "12:59", "formattedEnd": "13:01", "text": "Berghout. Yes."}, {"id": 407, "start": 781.949, "end": 781.959, "formattedStart": "13:01", "formattedEnd": "13:01", "text": "Yes."}, {"id": 408, "start": 781.959, "end": 783.829, "formattedStart": "13:01", "formattedEnd": "13:03", "text": "Yes. <PERSON><PERSON><PERSON><PERSON><PERSON>."}, {"id": 409, "start": 783.829, "end": 783.839, "formattedStart": "13:03", "formattedEnd": "13:03", "text": "Dan<PERSON><PERSON><PERSON><PERSON>."}, {"id": 410, "start": 783.839, "end": 789.279, "formattedStart": "13:03", "formattedEnd": "13:09", "text": "Dankjewel. Hey, kijk hij staat erop hoor."}, {"id": 411, "start": 789.279, "end": 791.949, "formattedStart": "13:09", "formattedEnd": "13:11", "text": "Daar is hij. Daar zit hij in een tunnel"}, {"id": 412, "start": 791.959, "end": 794.87, "formattedStart": "13:11", "formattedEnd": "13:14", "text": "Daar is hij. Daar zit hij in een tunnel en eh daar daar wil je alleen maar <PERSON>,"}, {"id": 413, "start": 794.87, "end": 794.88, "formattedStart": "13:14", "formattedEnd": "13:14", "text": "en eh daar daar wil je alleen maar <PERSON>,"}, {"id": 414, "start": 794.88, "end": 797.55, "formattedStart": "13:14", "formattedEnd": "13:17", "text": "en eh daar daar wil je alleen maar <PERSON>, de medaille is binnen. Bam. Dus eh we"}, {"id": 415, "start": 797.55, "end": 797.56, "formattedStart": "13:17", "formattedEnd": "13:17", "text": "de medaille is binnen. Bam. Dus eh we"}, {"id": 416, "start": 797.56, "end": 800.91, "formattedStart": "13:17", "formattedEnd": "13:20", "text": "de medaille is binnen. Bam. Dus eh we hebben net een mooi interview<PERSON><PERSON> gehad."}, {"id": 417, "start": 800.91, "end": 800.92, "formattedStart": "13:20", "formattedEnd": "13:20", "text": "hebben net een mooi interview<PERSON><PERSON> gehad."}, {"id": 418, "start": 800.92, "end": 802.99, "formattedStart": "13:20", "formattedEnd": "13:22", "text": "hebben net een mooi interviewtje gehad. foto. Oké, het is"}, {"id": 419, "start": 802.99, "end": 803, "formattedStart": "13:22", "formattedEnd": "13:23", "text": "foto. Oké, het is"}, {"id": 420, "start": 803, "end": 811.079, "formattedStart": "13:23", "formattedEnd": "13:31", "text": "foto. Oké, het is tijd voor protein."}, {"id": 421, "start": 811.079, "end": 813.55, "formattedStart": "13:31", "formattedEnd": "13:33", "text": "<PERSON>u jong<PERSON>, hoe vonden jullie het gaan?"}, {"id": 422, "start": 813.56, "end": 815.15, "formattedStart": "13:33", "formattedEnd": "13:35", "text": "<PERSON>u jong<PERSON>, hoe vonden jullie het gaan? Ik vond het wel lekker. Ja toch? We"}, {"id": 423, "start": 815.15, "end": 815.16, "formattedStart": "13:35", "formattedEnd": "13:35", "text": "Ik vond het wel lekker. Ja toch? We"}, {"id": 424, "start": 815.16, "end": 816.43, "formattedStart": "13:35", "formattedEnd": "13:36", "text": "<PERSON>k vond het wel lekker. Ja toch? We mogen niet klagen zo naar de marathon"}, {"id": 425, "start": 816.43, "end": 816.44, "formattedStart": "13:36", "formattedEnd": "13:36", "text": "mogen niet klagen zo naar de marathon"}, {"id": 426, "start": 816.44, "end": 818.23, "formattedStart": "13:36", "formattedEnd": "13:38", "text": "mogen niet klagen zo naar de marathon denk ik. <PERSON> week was het tropisch."}, {"id": 427, "start": 818.23, "end": 818.24, "formattedStart": "13:38", "formattedEnd": "13:38", "text": "denk ik. <PERSON> was het tropisch."}, {"id": 428, "start": 818.24, "end": 819.87, "formattedStart": "13:38", "formattedEnd": "13:39", "text": "denk ik. <PERSON> week was het tropisch. hittetrainingen nog gehad, maar van<PERSON><PERSON>"}, {"id": 429, "start": 819.87, "end": 819.88, "formattedStart": "13:39", "formattedEnd": "13:39", "text": "hittetrainingen nog gehad, maar van<PERSON>"}, {"id": 430, "start": 819.88, "end": 821.59, "formattedStart": "13:39", "formattedEnd": "13:41", "text": "hittetrainingen nog gehad, maar van<PERSON><PERSON> was het eh perfect zo. Het was een"}, {"id": 431, "start": 821.59, "end": 821.6, "formattedStart": "13:41", "formattedEnd": "13:41", "text": "was het eh perfect zo. Het was een"}, {"id": 432, "start": 821.6, "end": 823.189, "formattedStart": "13:41", "formattedEnd": "13:43", "text": "was het eh perfect zo. Het was een bijzondere bijzondere wedstrijd,"}, {"id": 433, "start": 823.189, "end": 823.199, "formattedStart": "13:43", "formattedEnd": "13:43", "text": "bijzondere bijzondere wedstrijd,"}, {"id": 434, "start": 823.199, "end": 824.629, "formattedStart": "13:43", "formattedEnd": "13:44", "text": "bijzondere bijzondere wedstrijd, bijzonder concept zo. Maar wel leuk om"}, {"id": 435, "start": 824.629, "end": 824.639, "formattedStart": "13:44", "formattedEnd": "13:44", "text": "bijzonder concept zo. Maar wel leuk om"}, {"id": 436, "start": 824.639, "end": 826.31, "formattedStart": "13:44", "formattedEnd": "13:46", "text": "bijzonder concept zo. Maar wel leuk om het op deze manier eh te doen. Dus ook"}, {"id": 437, "start": 826.31, "end": 826.32, "formattedStart": "13:46", "formattedEnd": "13:46", "text": "het op deze manier eh te doen. Dus ook"}, {"id": 438, "start": 826.32, "end": 828.069, "formattedStart": "13:46", "formattedEnd": "13:48", "text": "het op deze manier eh te doen. Dus ook leuk dat we met zijn drieën zo eh konden"}, {"id": 439, "start": 828.069, "end": 828.079, "formattedStart": "13:48", "formattedEnd": "13:48", "text": "leuk dat we met zijn drieën zo eh konden"}, {"id": 440, "start": 828.079, "end": 829.629, "formattedStart": "13:48", "formattedEnd": "13:49", "text": "leuk dat we met zijn drieën zo eh konden lopen. <PERSON>r. <PERSON>k heb daar wel van"}, {"id": 441, "start": 829.629, "end": 829.639, "formattedStart": "13:49", "formattedEnd": "13:49", "text": "lopen. <PERSON><PERSON>. <PERSON>k heb daar wel van"}, {"id": 442, "start": 829.639, "end": 831.11, "formattedStart": "13:49", "formattedEnd": "13:51", "text": "lopen. <PERSON><PERSON>. <PERSON>k heb daar wel van g<PERSON>. Dus eh we gaan nu lekker"}, {"id": 443, "start": 831.11, "end": 831.12, "formattedStart": "13:51", "formattedEnd": "13:51", "text": "genoten. Dus eh we gaan nu lekker"}, {"id": 444, "start": 831.12, "end": 834.55, "formattedStart": "13:51", "formattedEnd": "13:54", "text": "genoten. Dus eh we gaan nu lekker richting huis en eh zit een mooie"}, {"id": 445, "start": 834.55, "end": 834.56, "formattedStart": "13:54", "formattedEnd": "13:54", "text": "richting huis en eh zit een mooie"}, {"id": 446, "start": 834.56, "end": 836.55, "formattedStart": "13:54", "formattedEnd": "13:56", "text": "richting huis en eh zit een mooie training/wings for"}, {"id": 447, "start": 836.55, "end": 836.56, "formattedStart": "13:56", "formattedEnd": "13:56", "text": "training/wings for"}, {"id": 448, "start": 836.56, "end": 840.829, "formattedStart": "13:56", "formattedEnd": "14:00", "text": "training/wings for life event erop. Mooi. Nou is dat even"}, {"id": 449, "start": 840.829, "end": 840.839, "formattedStart": "14:00", "formattedEnd": "14:00", "text": "life event erop. <PERSON><PERSON>. Nou is dat even"}, {"id": 450, "start": 840.839, "end": 843.15, "formattedStart": "14:00", "formattedEnd": "14:03", "text": "life event erop. <PERSON><PERSON>. Nou is dat even afsluiten. Top toch? Ja, he<PERSON><PERSON><PERSON>."}, {"id": 451, "start": 843.15, "end": 843.16, "formattedStart": "14:03", "formattedEnd": "14:03", "text": "afsluiten. Top toch? Ja, he<PERSON><PERSON><PERSON>."}, {"id": 452, "start": 843.16, "end": 844.71, "formattedStart": "14:03", "formattedEnd": "14:04", "text": "afsluiten. Top toch? J<PERSON>, heerlijk. Heerlijk. Bedankt jongens. Moet je"}, {"id": 453, "start": 844.71, "end": 844.72, "formattedStart": "14:04", "formattedEnd": "14:04", "text": "Heerlijk. Bedankt jongens. Moet je"}, {"id": 454, "start": 844.72, "end": 846.71, "formattedStart": "14:04", "formattedEnd": "14:06", "text": "Heerlijk. Bedankt jongens. Moet je kijken wat er op die bus staat. Hey,"}, {"id": 455, "start": 846.71, "end": 846.72, "formattedStart": "14:06", "formattedEnd": "14:06", "text": "kijken wat er op die bus staat. Hey,"}, {"id": 456, "start": 846.72, "end": 848.189, "formattedStart": "14:06", "formattedEnd": "14:08", "text": "kijken wat er op die bus staat. Hey, daar zit hij gewoon."}, {"id": 457, "start": 848.189, "end": 848.199, "formattedStart": "14:08", "formattedEnd": "14:08", "text": "daar zit hij gewoon."}, {"id": 458, "start": 848.199, "end": 849.87, "formattedStart": "14:08", "formattedEnd": "14:09", "text": "daar zit hij gewoon. Bedankt. Het was gezellig man. <PERSON><PERSON><PERSON>o"}, {"id": 459, "start": 849.87, "end": 849.88, "formattedStart": "14:09", "formattedEnd": "14:09", "text": "Bedan<PERSON>. Het was gezellig man. <PERSON><PERSON><PERSON><PERSON>"}, {"id": 460, "start": 849.88, "end": 852.23, "formattedStart": "14:09", "formattedEnd": "14:12", "text": "Bedan<PERSON>. Het was gezellig man. Sowieso top. Leuk. Tot de volgende. Yo. Later."}, {"id": 461, "start": 852.23, "end": 852.24, "formattedStart": "14:12", "formattedEnd": "14:12", "text": "top. Leuk. Tot de volgende. Yo. Later."}, {"id": 462, "start": 852.24, "end": 854.91, "formattedStart": "14:12", "formattedEnd": "14:14", "text": "top. Leuk. Tot de volgende. Yo. Later. <PERSON>. Ik ga jou uitlaten. Was gezellig."}, {"id": 463, "start": 854.91, "end": 854.92, "formattedStart": "14:14", "formattedEnd": "14:14", "text": "<PERSON><PERSON> <PERSON>k ga jou u<PERSON>n. Was gezellig."}, {"id": 464, "start": 854.92, "end": 856.829, "formattedStart": "14:14", "formattedEnd": "14:16", "text": "<PERSON>. <PERSON>k ga jou u<PERSON>. Was gezellig. <PERSON><PERSON><PERSON><PERSON><PERSON> man. Thanks sowieso. Was een"}, {"id": 465, "start": 856.829, "end": 856.8389999999999, "formattedStart": "14:16", "formattedEnd": "14:16", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> man. Thanks sowieso. Was een"}, {"id": 466, "start": 856.8389999999999, "end": 858.55, "formattedStart": "14:16", "formattedEnd": "14:18", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> man. Thanks sowieso. Was een leuk eh leuk tripje. Het was een rit"}, {"id": 467, "start": 858.55, "end": 858.56, "formattedStart": "14:18", "formattedEnd": "14:18", "text": "leuk eh leuk tripje. Het was een rit"}, {"id": 468, "start": 858.56, "end": 861.269, "formattedStart": "14:18", "formattedEnd": "14:21", "text": "leuk eh leuk tripje. Het was een rit zonder hobbos. <PERSON><PERSON> <PERSON>. Alle bezoeken"}, {"id": 469, "start": 861.269, "end": 861.279, "formattedStart": "14:21", "formattedEnd": "14:21", "text": "zonder hobbos. Yo. Ho. Alle bezoeken"}, {"id": 470, "start": 861.279, "end": 864.829, "formattedStart": "14:21", "formattedEnd": "14:24", "text": "zonder hobbos. <PERSON><PERSON> <PERSON>. Alle bezoeken afgezet. We gaan nu richting huis en eh"}, {"id": 471, "start": 864.829, "end": 864.8389999999999, "formattedStart": "14:24", "formattedEnd": "14:24", "text": "afgezet. We gaan nu richting huis en eh"}, {"id": 472, "start": 864.8389999999999, "end": 867.67, "formattedStart": "14:24", "formattedEnd": "14:27", "text": "afgezet. We gaan nu richting huis en eh ja, he<PERSON><PERSON><PERSON>e heerlijke dag gelopen."}, {"id": 473, "start": 867.67, "end": 867.68, "formattedStart": "14:27", "formattedEnd": "14:27", "text": "ja, he<PERSON><PERSON><PERSON>e heerlijke dag gelopen."}, {"id": 474, "start": 867.68, "end": 869.749, "formattedStart": "14:27", "formattedEnd": "14:29", "text": "ja, he<PERSON><PERSON><PERSON>e heerlijke dag gelopen. <PERSON><PERSON><PERSON> training, mooie wedstrijd. Pim en"}, {"id": 475, "start": 869.749, "end": 869.759, "formattedStart": "14:29", "formattedEnd": "14:29", "text": "<PERSON><PERSON>e training, mooie wedstrijd. Pim en"}, {"id": 476, "start": 869.759, "end": 871.03, "formattedStart": "14:29", "formattedEnd": "14:31", "text": "<PERSON><PERSON>e training, mooie wedstrijd. <PERSON><PERSON> en Bjorn, bedankt voor de gezelligheid. Dat"}, {"id": 477, "start": 871.03, "end": 871.04, "formattedStart": "14:31", "formattedEnd": "14:31", "text": "<PERSON><PERSON><PERSON>, bedankt voor de gezelligheid. Dat"}, {"id": 478, "start": 871.04, "end": 872.71, "formattedStart": "14:31", "formattedEnd": "14:32", "text": "<PERSON><PERSON><PERSON>, bedankt voor de gezelligheid. Dat is sowieso nice. En eh we gaan nu lekker"}, {"id": 479, "start": 872.71, "end": 872.72, "formattedStart": "14:32", "formattedEnd": "14:32", "text": "is sowieso nice. En eh we gaan nu lekker"}, {"id": 480, "start": 872.72, "end": 875.35, "formattedStart": "14:32", "formattedEnd": "14:35", "text": "is sowieso nice. En eh we gaan nu lekker eh op naar huis."}, {"id": 481, "start": 875.35, "end": 875.36, "formattedStart": "14:35", "formattedEnd": "14:35", "text": "eh op naar huis."}, {"id": 482, "start": 875.36, "end": 877.269, "formattedStart": "14:35", "formattedEnd": "14:37", "text": "eh op naar huis. <PERSON>tof om een mooie training van deze"}, {"id": 483, "start": 877.269, "end": 877.279, "formattedStart": "14:37", "formattedEnd": "14:37", "text": "<PERSON>tof om een mooie training van deze"}, {"id": 484, "start": 877.279, "end": 878.91, "formattedStart": "14:37", "formattedEnd": "14:38", "text": "Supertof om een mooie training van deze wedstrijd te maken. Uiteindelijk hebben"}, {"id": 485, "start": 878.91, "end": 878.92, "formattedStart": "14:38", "formattedEnd": "14:38", "text": "wedstrijd te maken. Uiteindelijk hebben"}, {"id": 486, "start": 878.92, "end": 881.91, "formattedStart": "14:38", "formattedEnd": "14:41", "text": "wedstrijd te maken. Uiteindelijk hebben we 30 km gelopen. Heel tof om de sfeer"}, {"id": 487, "start": 881.91, "end": 881.92, "formattedStart": "14:41", "formattedEnd": "14:41", "text": "we 30 km gelopen. <PERSON><PERSON> tof om de s<PERSON>er"}, {"id": 488, "start": 881.92, "end": 883.629, "formattedStart": "14:41", "formattedEnd": "14:43", "text": "we 30 km gelopen. <PERSON><PERSON> tof om de s<PERSON> van deze wedstrijd te voelen en gewoon"}, {"id": 489, "start": 883.629, "end": 883.639, "formattedStart": "14:43", "formattedEnd": "14:43", "text": "van deze wedstrijd te voelen en gewoon"}, {"id": 490, "start": 883.639, "end": 885.389, "formattedStart": "14:43", "formattedEnd": "14:45", "text": "van deze wedstrijd te voelen en gewoon rustig van start te kunnen gaan. Dat is"}, {"id": 491, "start": 885.389, "end": 885.399, "formattedStart": "14:45", "formattedEnd": "14:45", "text": "rustig van start te kunnen gaan. Dat is"}, {"id": 492, "start": 885.399, "end": 887.509, "formattedStart": "14:45", "formattedEnd": "14:47", "text": "rustig van start te kunnen gaan. Dat is ook wel eens leuk. <PERSON>s eh hartstikke"}, {"id": 493, "start": 887.509, "end": 887.519, "formattedStart": "14:47", "formattedEnd": "14:47", "text": "ook wel eens leuk. Dus eh hartstikke"}, {"id": 494, "start": 887.519, "end": 889.15, "formattedStart": "14:47", "formattedEnd": "14:49", "text": "ook wel eens leuk. Dus eh hartstikke top. We gaan weer volgende week door met"}, {"id": 495, "start": 889.15, "end": 889.16, "formattedStart": "14:49", "formattedEnd": "14:49", "text": "top. We gaan weer volgende week door met"}, {"id": 496, "start": 889.16, "end": 891.189, "formattedStart": "14:49", "formattedEnd": "14:51", "text": "top. We gaan weer volgende week door met de trainingen en bedankt voor het kijken"}, {"id": 497, "start": 891.189, "end": 891.199, "formattedStart": "14:51", "formattedEnd": "14:51", "text": "de trainingen en bedankt voor het kijken"}, {"id": 498, "start": 891.199, "end": 892.99, "formattedStart": "14:51", "formattedEnd": "14:52", "text": "de trainingen en bedankt voor het kijken van de video. Like en subscribe en dan"}, {"id": 499, "start": 892.99, "end": 893, "formattedStart": "14:52", "formattedEnd": "14:53", "text": "van de video. Like en subscribe en dan"}, {"id": 500, "start": 893, "end": 895, "formattedStart": "14:53", "formattedEnd": "14:55", "text": "van de video. Like en subscribe en dan zie ik jullie bij de volgende. Ne."}]}