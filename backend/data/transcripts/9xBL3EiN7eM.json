{"videoId": "9xBL3EiN7eM", "language": "de-orig", "transcript": [{"id": 1, "start": 0.02, "end": 2.35, "formattedStart": "00:00", "formattedEnd": "00:02", "text": "angekommen in einer neuen welt"}, {"id": 2, "start": 2.36, "end": 5.12, "formattedStart": "00:02", "formattedEnd": "00:05", "text": "angekommen in einer neuen welt kitschiges ehefrau grace ist mächtig"}, {"id": 3, "start": 5.12, "end": 5.13, "formattedStart": "00:05", "formattedEnd": "00:05", "text": "kitschiges ehefrau grace ist mächtig"}, {"id": 4, "start": 5.13, "end": 6.26, "formattedStart": "00:05", "formattedEnd": "00:06", "text": "kitschiges ehefrau grace ist mächtig stolz auf ihren mann"}, {"id": 5, "start": 6.26, "end": 6.27, "formattedStart": "00:06", "formattedEnd": "00:06", "text": "stolz auf ihren mann"}, {"id": 6, "start": 6.27, "end": 7.82, "formattedStart": "00:06", "formattedEnd": "00:07", "text": "stolz auf ihren mann genau wie sein langj<PERSON><PERSON>iger trainer"}, {"id": 7, "start": 7.82, "end": 7.83, "formattedStart": "00:07", "formattedEnd": "00:07", "text": "genau wie sein lang<PERSON><PERSON><PERSON><PERSON> trainer"}, {"id": 8, "start": 7.83, "end": 10.459, "formattedStart": "00:07", "formattedEnd": "00:10", "text": "genau wie sein langj<PERSON>hriger trainer pet<PERSON> sander bilder der puren freude"}, {"id": 9, "start": 10.459, "end": 10.469, "formattedStart": "00:10", "formattedEnd": "00:10", "text": "petrik sander bilder der puren freude"}, {"id": 10, "start": 10.469, "end": 13.52, "formattedStart": "00:10", "formattedEnd": "00:13", "text": "petrik sander bilder der puren freude etwas mehr als zwei stunden zuvor pure"}, {"id": 11, "start": 13.52, "end": 13.53, "formattedStart": "00:13", "formattedEnd": "00:13", "text": "etwas mehr als zwei stunden zuvor pure"}, {"id": 12, "start": 13.53, "end": 18.68, "formattedStart": "00:13", "formattedEnd": "00:18", "text": "etwas mehr als zwei stunden zuvor pure konzentration"}, {"id": 13, "start": 18.68, "end": 20.99, "formattedStart": "00:18", "formattedEnd": "00:20", "text": "ich bin hier an den start gegangen um"}, {"id": 14, "start": 21, "end": 23.09, "formattedStart": "00:21", "formattedEnd": "00:23", "text": "ich bin hier an den start gegangen um unter zwei stunden zu laufen dann darfst"}, {"id": 15, "start": 23.09, "end": 23.1, "formattedStart": "00:23", "formattedEnd": "00:23", "text": "unter zwei stunden zu laufen dann darfst"}, {"id": 16, "start": 23.1, "end": 24.65, "formattedStart": "00:23", "formattedEnd": "00:24", "text": "unter zwei stunden zu laufen dann darfst du nicht abwägen nach dem motto du hast"}, {"id": 17, "start": 24.65, "end": 24.66, "formattedStart": "00:24", "formattedEnd": "00:24", "text": "du nicht abwägen nach dem motto du hast"}, {"id": 18, "start": 24.66, "end": 26.33, "formattedStart": "00:24", "formattedEnd": "00:26", "text": "du nicht abwägen nach dem motto du hast eine 50 50-chance"}, {"id": 19, "start": 26.33, "end": 26.34, "formattedStart": "00:26", "formattedEnd": "00:26", "text": "eine 50 50-chance"}, {"id": 20, "start": 26.34, "end": 29.419, "formattedStart": "00:26", "formattedEnd": "00:29", "text": "eine 50 50-chance nein davon muss total überzeugend zeit"}, {"id": 21, "start": 29.419, "end": 29.429, "formattedStart": "00:29", "formattedEnd": "00:29", "text": "nein davon muss total überzeugend zeit"}, {"id": 22, "start": 29.429, "end": 33.56, "formattedStart": "00:29", "formattedEnd": "00:33", "text": "nein davon muss total überzeugend zeit beginnt um 8 uhr 15 am samstagmorgen 10"}, {"id": 23, "start": 33.56, "end": 33.57, "formattedStart": "00:33", "formattedEnd": "00:33", "text": "beginnt um 8 uhr 15 am samstagmorgen 10"}, {"id": 24, "start": 33.57, "end": 35.93, "formattedStart": "00:33", "formattedEnd": "00:35", "text": "beginnt um 8 uhr 15 am samstagmorgen 10 grad der marathon-weltrekordler sucht"}, {"id": 25, "start": 35.93, "end": 35.94, "formattedStart": "00:35", "formattedEnd": "00:35", "text": "grad der marathon-weltrekordler sucht"}, {"id": 26, "start": 35.94, "end": 38, "formattedStart": "00:35", "formattedEnd": "00:38", "text": "grad der marathon-weltrekordler sucht eine neue herausforderung und ein"}, {"id": 27, "start": 38, "end": 38.01, "formattedStart": "00:38", "formattedEnd": "00:38", "text": "eine neue herausforderung und ein"}, {"id": 28, "start": 38.01, "end": 39.52, "formattedStart": "00:38", "formattedEnd": "00:39", "text": "eine neue herausforderung und ein sponsor investiert einen zweistelligen"}, {"id": 29, "start": 39.52, "end": 39.53, "formattedStart": "00:39", "formattedEnd": "00:39", "text": "sponsor investiert einen zweistelligen"}, {"id": 30, "start": 39.53, "end": 42.38, "formattedStart": "00:39", "formattedEnd": "00:42", "text": "sponsor investiert einen zweistelligen millionenbetrag bei diesem laborversuch"}, {"id": 31, "start": 42.38, "end": 42.39, "formattedStart": "00:42", "formattedEnd": "00:42", "text": "millionenbetrag bei diesem laborversuch"}, {"id": 32, "start": 42.39, "end": 44.479, "formattedStart": "00:42", "formattedEnd": "00:44", "text": "millionenbetrag bei diesem laborversuch geht es gibt schon darum zu zeigen was"}, {"id": 33, "start": 44.479, "end": 44.489, "formattedStart": "00:44", "formattedEnd": "00:44", "text": "geht es gibt schon darum zu zeigen was"}, {"id": 34, "start": 44.489, "end": 46.49, "formattedStart": "00:44", "formattedEnd": "00:46", "text": "geht es gibt schon darum zu zeigen was der menschliche körper zu leisten"}, {"id": 35, "start": 46.49, "end": 46.5, "formattedStart": "00:46", "formattedEnd": "00:46", "text": "der menschliche körper zu leisten"}, {"id": 36, "start": 46.5, "end": 48.01, "formattedStart": "00:46", "formattedEnd": "00:48", "text": "der menschliche körper zu leisten imstande ist"}, {"id": 37, "start": 48.01, "end": 48.02, "formattedStart": "00:48", "formattedEnd": "00:48", "text": "imstande ist"}, {"id": 38, "start": 48.02, "end": 50.93, "formattedStart": "00:48", "formattedEnd": "00:50", "text": "imstande ist 42,195 kilometer liegen vor kipchoge und"}, {"id": 39, "start": 50.93, "end": 50.94, "formattedStart": "00:50", "formattedEnd": "00:50", "text": "42,195 kilometer liegen vor kipchoge und"}, {"id": 40, "start": 50.94, "end": 53.959, "formattedStart": "00:50", "formattedEnd": "00:53", "text": "42,195 kilometer liegen vor kipchoge und seinem team aus 41 tempomachern die sich"}, {"id": 41, "start": 53.959, "end": 53.969, "formattedStart": "00:53", "formattedEnd": "00:53", "text": "seinem team aus 41 tempomachern die sich"}, {"id": 42, "start": 53.969, "end": 55.819, "formattedStart": "00:53", "formattedEnd": "00:55", "text": "seinem team aus 41 tempomachern die sich während des rennens abwechseln auch"}, {"id": 43, "start": 55.819, "end": 55.829, "formattedStart": "00:55", "formattedEnd": "00:55", "text": "während des rennens abwechseln auch"}, {"id": 44, "start": 55.829, "end": 57.979, "formattedStart": "00:55", "formattedEnd": "00:57", "text": "während des rennens abwechseln auch deshalb wird dieser lauf nicht als"}, {"id": 45, "start": 57.979, "end": 57.989, "formattedStart": "00:57", "formattedEnd": "00:57", "text": "deshalb wird dieser lauf nicht als"}, {"id": 46, "start": 57.989, "end": 59, "formattedStart": "00:57", "formattedEnd": "00:59", "text": "deshalb wird dieser lauf nicht als weltrekord gewertet"}, {"id": 47, "start": 59, "end": 59.01, "formattedStart": "00:59", "formattedEnd": "00:59", "text": "weltrekord gewertet"}, {"id": 48, "start": 59.01, "end": 62, "formattedStart": "00:59", "formattedEnd": "01:02", "text": "weltrekord gewertet die strecke einen rundkurs 9,6 kilometer"}, {"id": 49, "start": 62, "end": 62.01, "formattedStart": "01:02", "formattedEnd": "01:02", "text": "die strecke einen rundkurs 9,6 kilometer"}, {"id": 50, "start": 62.01, "end": 63.979, "formattedStart": "01:02", "formattedEnd": "01:03", "text": "die strecke einen rundkurs 9,6 kilometer auf der prater hauptallee vier mal hin"}, {"id": 51, "start": 63.979, "end": 63.989, "formattedStart": "01:03", "formattedEnd": "01:03", "text": "auf der prater hauptallee vier mal hin"}, {"id": 52, "start": 63.989, "end": 66.469, "formattedStart": "01:03", "formattedEnd": "01:06", "text": "auf der prater hauptallee vier mal hin und zurück extrem flach frisch geteert"}, {"id": 53, "start": 66.469, "end": 66.479, "formattedStart": "01:06", "formattedEnd": "01:06", "text": "und zurück extrem flach frisch geteert"}, {"id": 54, "start": 66.479, "end": 68.929, "formattedStart": "01:06", "formattedEnd": "01:08", "text": "und zurück extrem flach frisch geteert zu dem laser linien auf der straße die"}, {"id": 55, "start": 68.929, "end": 68.939, "formattedStart": "01:08", "formattedEnd": "01:08", "text": "zu dem laser linien auf der straße die"}, {"id": 56, "start": 68.939, "end": 71.899, "formattedStart": "01:08", "formattedEnd": "01:11", "text": "zu dem laser linien auf der straße die das tempo vorgeben ein höllentempo zwei"}, {"id": 57, "start": 71.899, "end": 71.909, "formattedStart": "01:11", "formattedEnd": "01:11", "text": "das tempo vorgeben ein höllentempo zwei"}, {"id": 58, "start": 71.909, "end": 74.27, "formattedStart": "01:11", "formattedEnd": "01:14", "text": "das tempo vorgeben ein höllentempo zwei minuten 50 sekunden pro kilometer"}, {"id": 59, "start": 74.27, "end": 74.28, "formattedStart": "01:14", "formattedEnd": "01:14", "text": "minuten 50 sekunden pro kilometer"}, {"id": 60, "start": 74.28, "end": 77.51, "formattedStart": "01:14", "formattedEnd": "01:17", "text": "minuten 50 sekunden pro kilometer es verläuft alles nach plan 15 kilometer"}, {"id": 61, "start": 77.51, "end": 77.52, "formattedStart": "01:17", "formattedEnd": "01:17", "text": "es verläuft alles nach plan 15 kilometer"}, {"id": 62, "start": 77.52, "end": 79.999, "formattedStart": "01:17", "formattedEnd": "01:19", "text": "es verläuft alles nach plan 15 kilometer absolviert die marschroute passt gibt"}, {"id": 63, "start": 79.999, "end": 80.009, "formattedStart": "01:19", "formattedEnd": "01:20", "text": "absolviert die marschroute passt gibt"}, {"id": 64, "start": 80.009, "end": 82.01, "formattedStart": "01:20", "formattedEnd": "01:22", "text": "absolviert die marschroute passt gibt zuge der olympiasieger im marathon wird"}, {"id": 65, "start": 82.01, "end": 82.02, "formattedStart": "01:22", "formattedEnd": "01:22", "text": "zuge der olympiasieger im marathon wird"}, {"id": 66, "start": 82.02, "end": 84.53, "formattedStart": "01:22", "formattedEnd": "01:24", "text": "zuge der olympiasieger im marathon wird regelmäßig verpflegt direkt von seinen"}, {"id": 67, "start": 84.53, "end": 84.53999999999999, "formattedStart": "01:24", "formattedEnd": "01:24", "text": "regelmäß<PERSON> verpflegt direkt von seinen"}, {"id": 68, "start": 84.53999999999999, "end": 86.87, "formattedStart": "01:24", "formattedEnd": "01:26", "text": "regelmä<PERSON><PERSON> verpflegt direkt von seinen helfern auch das ist normalerweise bei"}, {"id": 69, "start": 86.87, "end": 86.88, "formattedStart": "01:26", "formattedEnd": "01:26", "text": "he<PERSON><PERSON> auch das ist normalerweise bei"}, {"id": 70, "start": 86.88, "end": 90.62, "formattedStart": "01:26", "formattedEnd": "01:30", "text": "he<PERSON><PERSON> auch das ist normalerweise bei einem marathon nicht erlaubt tausende"}, {"id": 71, "start": 90.62, "end": 90.63, "formattedStart": "01:30", "formattedEnd": "01:30", "text": "einem marathon nicht erlaubt tausende"}, {"id": 72, "start": 90.63, "end": 92.84, "formattedStart": "01:30", "formattedEnd": "01:32", "text": "einem marathon nicht erlaubt tausende zuschauer pushen kipchoge und der mann"}, {"id": 73, "start": 92.84, "end": 92.85, "formattedStart": "01:32", "formattedEnd": "01:32", "text": "zuschauer pushen kipchoge und der mann"}, {"id": 74, "start": 92.85, "end": 94.899, "formattedStart": "01:32", "formattedEnd": "01:34", "text": "zuschauer pushen kipchoge und der mann zeigt keinerlei ermüdungserscheinungen"}, {"id": 75, "start": 94.899, "end": 94.90899999999999, "formattedStart": "01:34", "formattedEnd": "01:34", "text": "zeigt keinerlei ermüdungserscheinungen"}, {"id": 76, "start": 94.90899999999999, "end": 97.75999999999999, "formattedStart": "01:34", "formattedEnd": "01:37", "text": "zeigt keinerlei ermüdungserscheinungen die tempomacher perfekt auf den"}, {"id": 77, "start": 97.75999999999999, "end": 97.77000000000001, "formattedStart": "01:37", "formattedEnd": "01:37", "text": "die tempomacher perfekt auf den"}, {"id": 78, "start": 97.77000000000001, "end": 99.999, "formattedStart": "01:37", "formattedEnd": "01:39", "text": "die tempomacher perfekt auf den 34-jährigen abgestimmt ein"}, {"id": 79, "start": 99.999, "end": 100.009, "formattedStart": "01:39", "formattedEnd": "01:40", "text": "34-j<PERSON><PERSON>igen abgestimmt ein"}, {"id": 80, "start": 100.009, "end": 102.97999999999999, "formattedStart": "01:40", "formattedEnd": "01:42", "text": "34-jährigen abgestimmt ein ausgeklügeltes wechselsystem mv stil"}, {"id": 81, "start": 102.97999999999999, "end": 102.99000000000001, "formattedStart": "01:42", "formattedEnd": "01:42", "text": "ausgeklügeltes wechselsystem mv stil"}, {"id": 82, "start": 102.99000000000001, "end": 105.289, "formattedStart": "01:42", "formattedEnd": "01:45", "text": "ausgeklügeltes wechselsystem mv stil angeordnet so kann er im windschatten"}, {"id": 83, "start": 105.289, "end": 105.299, "formattedStart": "01:45", "formattedEnd": "01:45", "text": "angeordnet so kann er im windschatten"}, {"id": 84, "start": 105.299, "end": 109.75999999999999, "formattedStart": "01:45", "formattedEnd": "01:49", "text": "angeordnet so kann er im windschatten laufen wird schon die wechsel waren so"}, {"id": 85, "start": 109.75999999999999, "end": 109.77000000000001, "formattedStart": "01:49", "formattedEnd": "01:49", "text": "laufen wird schon die wechsel waren so"}, {"id": 86, "start": 109.77000000000001, "end": 111.85900000000001, "formattedStart": "01:49", "formattedEnd": "01:51", "text": "laufen wird schon die wechsel waren so vorbildlich hier und da gab es mal eine"}, {"id": 87, "start": 111.85900000000001, "end": 111.869, "formattedStart": "01:51", "formattedEnd": "01:51", "text": "vor<PERSON><PERSON><PERSON> hier und da gab es mal eine"}, {"id": 88, "start": 111.869, "end": 113.749, "formattedStart": "01:51", "formattedEnd": "01:53", "text": "vorbil<PERSON><PERSON> hier und da gab es mal eine kleine schlosserei aber die tempomacher"}, {"id": 89, "start": 113.749, "end": 113.759, "formattedStart": "01:53", "formattedEnd": "01:53", "text": "kleine schlosserei aber die tempomacher"}, {"id": 90, "start": 113.759, "end": 117.22999999999999, "formattedStart": "01:53", "formattedEnd": "01:57", "text": "kleine schlosserei aber die tempomacher sind so ruhig geblieben und konnte immer"}, {"id": 91, "start": 117.22999999999999, "end": 117.24000000000001, "formattedStart": "01:57", "formattedEnd": "01:57", "text": "sind so ruhig geblieben und konnte immer"}, {"id": 92, "start": 117.24000000000001, "end": 120.59, "formattedStart": "01:57", "formattedEnd": "02:00", "text": "sind so ruhig geblieben und konnte immer ein tempo laufen das ganze set up hat"}, {"id": 93, "start": 120.59, "end": 120.6, "formattedStart": "02:00", "formattedEnd": "02:00", "text": "ein tempo laufen das ganze set up hat"}, {"id": 94, "start": 120.6, "end": 124.92, "formattedStart": "02:00", "formattedEnd": "02:04", "text": "ein tempo laufen das ganze set up hat gepasst"}, {"id": 95, "start": 124.92, "end": 127.07, "formattedStart": "02:04", "formattedEnd": "02:07", "text": "dann auf der langen zielgeraden die"}, {"id": 96, "start": 127.08, "end": 128.749, "formattedStart": "02:07", "formattedEnd": "02:08", "text": "dann auf der langen zielgeraden die anerkennung für den star"}, {"id": 97, "start": 128.749, "end": 128.75900000000001, "formattedStart": "02:08", "formattedEnd": "02:08", "text": "anerkennung für den star"}, {"id": 98, "start": 128.75900000000001, "end": 131.12, "formattedStart": "02:08", "formattedEnd": "02:11", "text": "anerkennung für den star die weltklasse tempomacher ziehen sich"}, {"id": 99, "start": 131.12, "end": 131.13, "formattedStart": "02:11", "formattedEnd": "02:11", "text": "die weltklasse tempomacher ziehen sich"}, {"id": 100, "start": 131.13, "end": 134.27, "formattedStart": "02:11", "formattedEnd": "02:14", "text": "die weltklasse tempomacher ziehen sich zurück der ausnahme athlet aus kenia hat"}, {"id": 101, "start": 134.27, "end": 134.28, "formattedStart": "02:14", "formattedEnd": "02:14", "text": "zurück der ausnahme athlet aus kenia hat"}, {"id": 102, "start": 134.28, "end": 136.94, "formattedStart": "02:14", "formattedEnd": "02:16", "text": "zurück der ausnahme athlet aus kenia hat gezeigt die grenzen werden im kopf"}, {"id": 103, "start": 136.94, "end": 136.95, "formattedStart": "02:16", "formattedEnd": "02:16", "text": "gezeigt die grenzen werden im kopf"}, {"id": 104, "start": 136.95, "end": 138.01, "formattedStart": "02:16", "formattedEnd": "02:18", "text": "gezeigt die grenzen werden im kopf verschoben"}, {"id": 105, "start": 138.01, "end": 138.02, "formattedStart": "02:18", "formattedEnd": "02:18", "text": "verschoben"}, {"id": 106, "start": 138.02, "end": 140.93, "formattedStart": "02:18", "formattedEnd": "02:20", "text": "verschoben ich habe mir total vertraut mein wille"}, {"id": 107, "start": 140.93, "end": 140.94, "formattedStart": "02:20", "formattedEnd": "02:20", "text": "ich habe mir total vertraut mein wille"}, {"id": 108, "start": 140.94, "end": 143.93, "formattedStart": "02:20", "formattedEnd": "02:23", "text": "ich habe mir total vertraut mein wille war ungebrochen ich wusste heute ist der"}, {"id": 109, "start": 143.93, "end": 143.94, "formattedStart": "02:23", "formattedEnd": "02:23", "text": "war ungebrochen ich wusste heute ist der"}, {"id": 110, "start": 143.94, "end": 150.35, "formattedStart": "02:23", "formattedEnd": "02:30", "text": "war ungebrochen ich wusste heute ist der tag die entbehrungen der letzten monate"}, {"id": 111, "start": 150.35, "end": 150.36, "formattedStart": "02:30", "formattedEnd": "02:30", "text": "tag die entbehrungen der letzten monate"}, {"id": 112, "start": 150.36, "end": 152, "formattedStart": "02:30", "formattedEnd": "02:32", "text": "tag die entbehrungen der letzten monate haben sich gelohnt er lud gibt so"}, {"id": 113, "start": 152, "end": 152.01, "formattedStart": "02:32", "formattedEnd": "02:32", "text": "haben sich gelohnt er lud gibt so"}, {"id": 114, "start": 152.01, "end": 154.22, "formattedStart": "02:32", "formattedEnd": "02:34", "text": "haben sich gelohnt er lud gibt so beschreibt in wien geschichte bleibt"}, {"id": 115, "start": 154.22, "end": 154.23, "formattedStart": "02:34", "formattedEnd": "02:34", "text": "beschreibt in wien geschichte bleibt"}, {"id": 116, "start": 154.23, "end": 156.97, "formattedStart": "02:34", "formattedEnd": "02:36", "text": "beschreibt in wien geschichte bleibt deutlich unter der zwei stunden marke"}, {"id": 117, "start": 156.97, "end": 156.98, "formattedStart": "02:36", "formattedEnd": "02:36", "text": "deutlich unter der zwei stunden marke"}, {"id": 118, "start": 156.98, "end": 160.38, "formattedStart": "02:36", "formattedEnd": "02:40", "text": "deutlich unter der zwei stunden marke [<PERSON><PERSON><PERSON>]"}, {"id": 119, "start": 160.38, "end": 160.39, "formattedStart": "02:40", "formattedEnd": "02:40", "text": "[Applaus]"}, {"id": 120, "start": 160.39, "end": 162.39, "formattedStart": "02:40", "formattedEnd": "02:42", "text": "[A<PERSON><PERSON>] [<PERSON><PERSON>]"}]}