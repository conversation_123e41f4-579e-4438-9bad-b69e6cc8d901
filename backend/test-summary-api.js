const axios = require('axios');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

const BASE_URL = 'http://localhost:3000';

async function testSummaryAPI() {
  console.log('🧪 Testing Summary API Integration...\n');

  try {
    // Test with an example video (Dunning-Kruger Effect)
    const testVideoId = 'TT81fe2IobI';
    const testLanguage = 'en';

    console.log(`1. Testing summary generation for video: ${testVideoId}`);
    console.log(`   Language: ${testLanguage}`);
    console.log('   This may take a moment...\n');

    const startTime = Date.now();
    const response = await axios.get(`${BASE_URL}/api/summary/${testVideoId}?lang=${testLanguage}`);
    const endTime = Date.now();

    if (response.data.success) {
      console.log('✅ Summary generated successfully!');
      console.log(`   Generation time: ${(endTime - startTime) / 1000}s`);
      console.log(`   Video ID: ${response.data.videoId}`);
      console.log(`   Language: ${response.data.language}`);
      console.log(`   Transcript segments: ${response.data.transcriptLength}`);
      console.log(`   Model: ${response.data.model}`);
      console.log('\n📝 Generated Summary:');
      console.log('─'.repeat(60));
      console.log(response.data.summary);
      console.log('─'.repeat(60));
    } else {
      console.log('❌ Summary generation failed');
      console.log('   Response:', response.data);
    }

    console.log('\n🎉 Summary API test completed successfully!');
    console.log('\nThe summary feature is now ready to use in your YouTube Transcript Generator.');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    
    if (error.response) {
      console.error('   Status:', error.response.status);
      console.error('   Error Details:', error.response.data);
    } else if (error.code === 'ECONNREFUSED') {
      console.error('   Make sure the server is running on port 3000');
      console.error('   Run: cd backend && npm start');
    }
    
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  testSummaryAPI();
}

module.exports = testSummaryAPI;
