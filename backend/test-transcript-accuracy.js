const axios = require('axios');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

const BASE_URL = 'http://localhost:3000';

async function testTranscriptAccuracy() {
  console.log('🧪 Testing Transcript Accuracy & Cloze Removal...\n');

  try {
    // Test with an example video
    const testVideoId = 'TT81fe2IobI';
    const testLanguage = 'en';

    console.log(`Testing video: ${testVideoId} (Dunning-Kruger Effect)`);
    console.log(`Language: ${testLanguage}\n`);

    console.log('Generating improved Anki flash cards...');
    console.log('Expected improvements:');
    console.log('  ✅ No cloze cards');
    console.log('  ✅ Content verified against transcript');
    console.log('  ✅ Transcript quotes included');
    console.log('  ✅ Accurate timestamps\n');

    const requestBody = {
      videoId: testVideoId,
      lang: testLanguage
    };

    const startTime = Date.now();
    const response = await axios.post(`${BASE_URL}/api/anki-cards`, requestBody);
    const endTime = Date.now();

    if (response.data.success) {
      console.log('✅ Cards generated successfully!');
      console.log(`   Generation time: ${(endTime - startTime) / 1000}s`);
      console.log(`   Number of cards: ${response.data.cards.length}`);

      const cards = response.data.cards;

      // Analyze card types
      const cardTypes = {};
      cards.forEach(card => {
        cardTypes[card.type] = (cardTypes[card.type] || 0) + 1;
      });

      console.log('\n📊 Card Type Distribution:');
      console.log(`   Q&A cards: ${cardTypes.qa || 0}`);
      console.log(`   Process cards: ${cardTypes.process || 0}`);
      console.log(`   True/False cards: ${cardTypes.truefalse || 0}`);
      console.log(`   Cloze cards: ${cardTypes.cloze || 0} (should be 0)`);

      // Validate no cloze cards
      const clozeCards = cards.filter(card => card.type === 'cloze');
      if (clozeCards.length === 0) {
        console.log('✅ No cloze cards found - requirement met!');
      } else {
        console.log(`❌ Found ${clozeCards.length} cloze cards - should be 0`);
      }

      // Check for transcript quotes
      const cardsWithQuotes = cards.filter(card => card.transcriptQuote && card.transcriptQuote.length > 0);
      console.log(`\n📝 Transcript Verification:`);
      console.log(`   Cards with transcript quotes: ${cardsWithQuotes.length}/${cards.length}`);
      
      if (cardsWithQuotes.length > 0) {
        console.log('✅ Transcript quotes included - content verification enabled!');
      } else {
        console.log('⚠️ No transcript quotes found - may indicate AI didn\'t follow instructions');
      }

      // Analyze card content quality
      console.log('\n🔍 Content Quality Analysis:');
      console.log('─'.repeat(80));
      
      cards.forEach((card, index) => {
        console.log(`\n${index + 1}. [${card.type.toUpperCase()}] ${card.difficulty}`);
        console.log(`   Q: ${card.question}`);
        console.log(`   A: ${card.answer.substring(0, 100)}${card.answer.length > 100 ? '...' : ''}`);
        console.log(`   ⏰ ${Math.floor(card.timestamp / 60)}:${(card.timestamp % 60).toString().padStart(2, '0')}`);
        console.log(`   🏷️ ${card.tags.join(', ')}`);
        
        if (card.transcriptQuote) {
          console.log(`   📜 Quote: "${card.transcriptQuote.substring(0, 80)}${card.transcriptQuote.length > 80 ? '...' : ''}"`);
        }
        
        if (card.context) {
          console.log(`   📝 Context: ${card.context}`);
        }

        // Check for potential hallucination indicators
        const suspiciousWords = ['according to research', 'studies show', 'experts say', 'it is known that'];
        const hasSuspiciousContent = suspiciousWords.some(phrase => 
          card.question.toLowerCase().includes(phrase) || 
          card.answer.toLowerCase().includes(phrase)
        );
        
        if (hasSuspiciousContent) {
          console.log(`   ⚠️ Potentially hallucinated content detected`);
        } else {
          console.log(`   ✅ Content appears transcript-based`);
        }
      });
      
      console.log('─'.repeat(80));

      // Test CSV export format
      console.log('\n📄 Testing CSV Export Format:');
      
      const sampleCard = cards[0];
      if (sampleCard) {
        const front = `"${sampleCard.question.replace(/"/g, '""')}"`;
        const back = `"${sampleCard.answer.replace(/"/g, '""')}"`;
        const tags = `"${sampleCard.tags.join(' ')} ${sampleCard.type} ${sampleCard.difficulty}"`;
        const sourceVideo = `"Test Video ${testVideoId}"`;
        const minutes = Math.floor(sampleCard.timestamp / 60);
        const seconds = Math.floor(sampleCard.timestamp % 60);
        const timestamp = `"${minutes}:${seconds.toString().padStart(2, '0')}"`;
        const videoUrl = `"https://youtube.com/watch?v=${testVideoId}&t=${Math.floor(sampleCard.timestamp)}s"`;
        
        const cardTypeMap = {
          'qa': 'basic_qa',
          'process': 'process',
          'truefalse': 'true_false'
        };
        const cardType = `"${cardTypeMap[sampleCard.type] || 'basic_qa'}"`;
        const difficulty = `"${sampleCard.difficulty}"`;
        const createdDate = `"${new Date().toISOString().split('T')[0]}"`;
        const userNotes = '""';
        
        const csvRow = `${front},${back},${tags},${sourceVideo},${timestamp},${videoUrl},${cardType},${difficulty},${createdDate},${userNotes}`;
        
        console.log('Sample CSV row:');
        console.log(csvRow);
        console.log('✅ CSV format compatible with Anki import');
      }

      // Final validation summary
      console.log('\n🎯 Validation Summary:');
      console.log(`✅ Total cards generated: ${cards.length}`);
      console.log(`✅ Cloze cards removed: ${clozeCards.length === 0 ? 'Yes' : 'No'}`);
      console.log(`✅ Transcript quotes included: ${cardsWithQuotes.length > 0 ? 'Yes' : 'No'}`);
      console.log(`✅ Valid timestamps: ${cards.every(c => c.timestamp >= 0) ? 'Yes' : 'No'}`);
      console.log(`✅ All required fields present: ${cards.every(c => c.question && c.answer && c.type) ? 'Yes' : 'No'}`);
      
      const allValidTypes = cards.every(c => ['qa', 'process', 'truefalse'].includes(c.type));
      console.log(`✅ Only allowed card types: ${allValidTypes ? 'Yes' : 'No'}`);

      if (clozeCards.length === 0 && cardsWithQuotes.length > 0 && allValidTypes) {
        console.log('\n🎉 All improvements successfully implemented!');
      } else {
        console.log('\n⚠️ Some improvements may need additional work');
      }

    } else {
      console.log('❌ Card generation failed');
      console.log('   Response:', response.data);
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    
    if (error.response) {
      console.log('   Status:', error.response.status);
      console.log('   Data:', error.response.data);
    }
  }
}

// Run the test
testTranscriptAccuracy().then(() => {
  console.log('\n🏁 Transcript accuracy test completed');
});
