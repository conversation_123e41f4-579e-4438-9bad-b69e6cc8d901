/**
 * <PERSON><PERSON>t to install yt-dlp
 * 
 * This script checks if yt-dlp is installed and installs it if not.
 * It supports macOS, Linux, and Windows.
 */

const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');
const os = require('os');
const https = require('https');
const { promisify } = require('util');

const execPromise = promisify(exec);

// Determine the platform
const platform = os.platform();

async function checkYtDlpInstalled() {
  try {
    const { stdout } = await execPromise('yt-dlp --version');
    console.log(`yt-dlp is already installed (version: ${stdout.trim()})`);
    return true;
  } catch (error) {
    console.log('yt-dlp is not installed or not in PATH');
    return false;
  }
}

async function installYtDlp() {
  console.log('Installing yt-dlp...');

  try {
    if (platform === 'darwin') {
      // macOS - use Homebrew if available, otherwise download binary
      try {
        await execPromise('brew --version');
        console.log('Homebrew is installed, using it to install yt-dlp');
        await execPromise('brew install yt-dlp');
      } catch (error) {
        console.log('Homebrew not found, downloading binary directly');
        await downloadYtDlpBinary();
      }
    } else if (platform === 'linux') {
      // Linux - try apt-get first, then pip, then download binary
      try {
        await execPromise('apt-get --version');
        console.log('apt-get is available, using it to install yt-dlp');
        await execPromise('sudo apt-get update && sudo apt-get install -y yt-dlp');
      } catch (error) {
        try {
          await execPromise('pip --version');
          console.log('pip is available, using it to install yt-dlp');
          await execPromise('pip install yt-dlp');
        } catch (error) {
          console.log('Neither apt-get nor pip is available, downloading binary directly');
          await downloadYtDlpBinary();
        }
      }
    } else if (platform === 'win32') {
      // Windows - download binary
      console.log('Windows detected, downloading binary directly');
      await downloadYtDlpBinary();
    } else {
      console.error(`Unsupported platform: ${platform}`);
      return false;
    }

    // Verify installation
    const installed = await checkYtDlpInstalled();
    if (installed) {
      console.log('yt-dlp was successfully installed!');
      return true;
    } else {
      console.error('Failed to install yt-dlp');
      return false;
    }
  } catch (error) {
    console.error('Error installing yt-dlp:', error.message);
    return false;
  }
}

async function downloadYtDlpBinary() {
  // Determine the correct binary URL based on platform
  let url;
  let destPath;

  if (platform === 'win32') {
    url = 'https://github.com/yt-dlp/yt-dlp/releases/latest/download/yt-dlp.exe';
    destPath = path.join(os.homedir(), 'yt-dlp.exe');
  } else {
    url = 'https://github.com/yt-dlp/yt-dlp/releases/latest/download/yt-dlp';
    destPath = '/usr/local/bin/yt-dlp';
  }

  console.log(`Downloading yt-dlp from ${url} to ${destPath}`);

  return new Promise((resolve, reject) => {
    const file = fs.createWriteStream(destPath);
    
    https.get(url, (response) => {
      response.pipe(file);
      
      file.on('finish', async () => {
        file.close();
        
        // Make the file executable on Unix-like systems
        if (platform !== 'win32') {
          try {
            await execPromise(`chmod +x ${destPath}`);
          } catch (error) {
            console.error('Error making yt-dlp executable:', error.message);
            reject(error);
            return;
          }
        }
        
        console.log('Download completed');
        resolve();
      });
    }).on('error', (error) => {
      fs.unlink(destPath, () => {}); // Delete the file on error
      console.error('Error downloading yt-dlp:', error.message);
      reject(error);
    });
  });
}

async function main() {
  const installed = await checkYtDlpInstalled();
  
  if (!installed) {
    const success = await installYtDlp();
    if (!success) {
      console.log('\nManual installation instructions:');
      console.log('- macOS: brew install yt-dlp');
      console.log('- Linux: sudo apt-get install yt-dlp or pip install yt-dlp');
      console.log('- Windows: Download from https://github.com/yt-dlp/yt-dlp/releases and add to PATH');
      console.log('\nFor more information, visit: https://github.com/yt-dlp/yt-dlp#installation');
    }
  }
}

// Run the script
main().catch(error => {
  console.error('Unhandled error:', error);
  process.exit(1);
});
