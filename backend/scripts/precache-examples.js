#!/usr/bin/env node

/**
 * Pre-cache Example Videos Script
 * 
 * This script pre-caches all transcripts and generated content for example videos
 * to ensure fast loading times for users.
 */

const exampleCacheService = require('../services/exampleVideoCacheService');

async function main() {
  console.log('🚀 Starting example video pre-caching process...');
  console.log('This may take several minutes to complete.');
  console.log('');

  try {
    // Run the complete pre-cache process
    await exampleCacheService.runCompletePrecache();
    
    console.log('');
    console.log('✅ Pre-caching completed successfully!');
    console.log('Example videos will now load much faster for users.');
    
  } catch (error) {
    console.error('❌ Error during pre-caching:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = main;
