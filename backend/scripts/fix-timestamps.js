/**
 * <PERSON><PERSON><PERSON> to fix timestamps in cached transcripts
 * 
 * This script reads all cached transcript files and updates the timestamps
 * to use the correct scaling factor.
 */

const fs = require('fs').promises;
const path = require('path');

// Cache directory
const TRANSCRIPTS_DIR = path.join(__dirname, '../data/transcripts');

/**
 * Format time in HH:MM:SS format
 * @param {number} seconds - Time in seconds
 * @returns {string} Formatted time string
 */
function formatTime(seconds) {
  // Ensure seconds is a valid number
  if (typeof seconds !== 'number' || isNaN(seconds)) {
    seconds = 0;
  }
  
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  // Always include hours for consistent formatting
  return [
    hours.toString().padStart(2, '0'),
    minutes.toString().padStart(2, '0'),
    secs.toString().padStart(2, '0')
  ].join(':');
}

/**
 * Fix timestamps in a transcript
 * @param {object} transcript - Transcript object
 * @returns {object} Fixed transcript
 */
function fixTranscript(transcript) {
  // Check if we need to scale the timestamps
  // If all timestamps are very small (< 1), they're likely in a 0-1 range instead of seconds
  const needsScaling = transcript.transcript.length > 0 && 
                      transcript.transcript.every(item => item.start < 1);
  
  // Calculate scaling factor based on video length
  // For most YouTube videos, 4 minutes (240 seconds) is a reasonable default
  const scalingFactor = needsScaling ? 240 : 1;
  
  console.log(`Fixing transcript for ${transcript.videoId}, scaling factor: ${scalingFactor}`);
  
  // Apply scaling to timestamps if needed
  transcript.transcript.forEach(item => {
    item.start = item.start * scalingFactor;
    item.end = item.end * scalingFactor;
    item.formattedStart = formatTime(item.start);
    item.formattedEnd = formatTime(item.end);
  });
  
  return transcript;
}

/**
 * Fix all cached transcripts
 */
async function fixAllTranscripts() {
  try {
    // Ensure the transcripts directory exists
    try {
      await fs.mkdir(TRANSCRIPTS_DIR, { recursive: true });
    } catch (err) {
      if (err.code !== 'EEXIST') throw err;
    }
    
    // Get all transcript files
    const files = await fs.readdir(TRANSCRIPTS_DIR);
    const jsonFiles = files.filter(file => file.endsWith('.json'));
    
    console.log(`Found ${jsonFiles.length} transcript files`);
    
    // Process each file
    for (const file of jsonFiles) {
      const filePath = path.join(TRANSCRIPTS_DIR, file);
      
      try {
        // Read the transcript
        const data = await fs.readFile(filePath, 'utf8');
        const transcript = JSON.parse(data);
        
        // Fix the transcript
        const fixedTranscript = fixTranscript(transcript);
        
        // Write the fixed transcript back to the file
        await fs.writeFile(
          filePath,
          JSON.stringify(fixedTranscript, null, 2)
        );
        
        console.log(`Fixed transcript for ${transcript.videoId}`);
      } catch (error) {
        console.error(`Error processing ${file}:`, error.message);
      }
    }
    
    console.log('All transcripts fixed successfully');
  } catch (error) {
    console.error('Error fixing transcripts:', error.message);
  }
}

// Run the script
fixAllTranscripts().catch(error => {
  console.error('Unhandled error:', error);
  process.exit(1);
});
