const fs = require('fs').promises;
const path = require('path');
const { YoutubeTranscript } = require('youtube-transcript');

// Path to example videos data
const exampleVideosPath = path.join(__dirname, '../data/example-videos.json');
const transcriptsDir = path.join(__dirname, '../data/transcripts');

// Format time in HH:MM:SS format
function formatTime(seconds) {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  return [
    hours > 0 ? hours.toString().padStart(2, '0') : null,
    minutes.toString().padStart(2, '0'),
    secs.toString().padStart(2, '0')
  ].filter(Boolean).join(':');
}

async function cacheTranscripts() {
  try {
    // Read example videos data
    const exampleVideosData = JSON.parse(await fs.readFile(exampleVideosPath, 'utf8'));
    const videos = exampleVideosData.videos;

    console.log(`Found ${videos.length} example videos to cache transcripts for.`);

    // Ensure transcripts directory exists
    try {
      await fs.mkdir(transcriptsDir, { recursive: true });
    } catch (err) {
      if (err.code !== 'EEXIST') throw err;
    }

    // Fetch and cache transcripts for each video
    for (const video of videos) {
      const videoId = video.id;
      const transcriptPath = path.join(transcriptsDir, `${videoId}.json`);

      console.log(`Processing video: ${video.title} (${videoId})`);

      try {
        // Check if transcript already exists
        try {
          await fs.access(transcriptPath);
          console.log(`Transcript for ${videoId} already exists, skipping.`);
          continue;
        } catch (err) {
          // File doesn't exist, continue to fetch
        }

        // Fetch transcript using youtube-transcript package with English language specified
        const transcriptList = await YoutubeTranscript.fetchTranscript(videoId, {
          lang: 'en'  // Explicitly request English transcripts
        });

        if (!transcriptList || transcriptList.length === 0) {
          console.error(`No transcript found for video ${videoId}`);
          continue;
        }

        // Format the transcript data
        const formattedTranscript = transcriptList.map((item, index) => {
          // YouTube transcript API returns time in milliseconds
          // We need to convert to seconds for our application
          const startSeconds = item.offset / 1000;
          const durationSeconds = item.duration / 1000;
          const endSeconds = startSeconds + durationSeconds;

          return {
            id: index + 1,
            start: startSeconds,
            end: endSeconds,
            formattedStart: formatTime(startSeconds),
            formattedEnd: formatTime(endSeconds),
            text: item.text
          };
        });

        // Save formatted transcript to file
        const transcriptData = {
          videoId,
          language: 'en',
          transcript: formattedTranscript
        };

        await fs.writeFile(transcriptPath, JSON.stringify(transcriptData, null, 2));
        console.log(`Cached transcript for ${videoId} successfully.`);
      } catch (error) {
        console.error(`Error caching transcript for ${videoId}:`, error.message);
      }
    }

    console.log('Transcript caching completed.');
  } catch (error) {
    console.error('Error in cacheTranscripts:', error);
  }
}

// Run the script
cacheTranscripts();
