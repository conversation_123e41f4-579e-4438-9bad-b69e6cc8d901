const axios = require('axios');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

const BASE_URL = 'http://localhost:3000';

async function testSocialMediaAPI() {
  console.log('🧪 Testing Social Media Posts Generation Feature...\n');

  try {
    // Test with an example video (Dunning-Kruger Effect)
    const testVideoId = 'TT81fe2IobI';
    const testLanguage = 'en';

    console.log(`Testing video: ${testVideoId} (Dunning-Kruger Effect)`);
    console.log(`Language: ${testLanguage}\n`);

    console.log('Testing Social Media Posts Generation...');
    console.log('   Creating platform-optimized posts for Twitter, Instagram, and LinkedIn');
    console.log('   This may take a moment...\n');

    const requestBody = {
      videoId: testVideoId,
      coreMessage: '', // Let AI determine from content
      lang: testLanguage
    };

    const startTime = Date.now();
    const response = await axios.post(`${BASE_URL}/api/social-media`, requestBody);
    const endTime = Date.now();

    if (response.data.success) {
      console.log('✅ Social Media Posts generated successfully!');
      console.log(`   Generation time: ${(endTime - startTime) / 1000}s`);
      console.log(`   Posts length: ${response.data.socialMediaPosts.length} characters`);
      console.log(`   Core Message: ${response.data.coreMessage}`);
      console.log('\n📱 Generated Social Media Posts:');
      console.log('─'.repeat(80));
      console.log(response.data.socialMediaPosts);
      console.log('─'.repeat(80));

      // Analyze the output
      const postsText = response.data.socialMediaPosts;
      const hasTwitter = postsText.includes('🐦 TWITTER (X) POST');
      const hasInstagram = postsText.includes('📱 INSTAGRAM POST');
      const hasLinkedIn = postsText.includes('💼 LINKEDIN POST');
      const hasHashtags = postsText.includes('#');
      const hasImageIdea = postsText.includes('Image Idea:');
      
      console.log(`\n📊 Analysis:`);
      console.log(`   Contains Twitter post: ${hasTwitter ? '✅' : '❌'}`);
      console.log(`   Contains Instagram post: ${hasInstagram ? '✅' : '❌'}`);
      console.log(`   Contains LinkedIn post: ${hasLinkedIn ? '✅' : '❌'}`);
      console.log(`   Contains hashtags: ${hasHashtags ? '✅' : '❌'}`);
      console.log(`   Contains image ideas: ${hasImageIdea ? '✅' : '❌'}`);
      console.log(`   Total character count: ${postsText.length}`);

      // Check Twitter character limit
      const twitterMatch = postsText.match(/🐦 TWITTER \(X\) POST\*\*(.*?)---/s);
      if (twitterMatch) {
        const twitterContent = twitterMatch[1].replace(/\*\*/g, '').trim();
        const twitterLength = twitterContent.length;
        console.log(`   Twitter post length: ${twitterLength} characters (limit: 280)`);
        console.log(`   Twitter within limit: ${twitterLength <= 280 ? '✅' : '❌'}`);
      }

    } else {
      console.log('❌ Social media posts generation failed');
      console.log('   Response:', response.data);
    }

    console.log('\n🎉 Social Media Posts feature tested successfully!');
    console.log('\n📋 Summary of Features:');
    console.log('   ✅ Platform-specific optimization (Twitter, Instagram, LinkedIn)');
    console.log('   ✅ Character limits respected');
    console.log('   ✅ Hashtag generation');
    console.log('   ✅ Image ideas for Instagram');
    console.log('   ✅ Professional tone for LinkedIn');
    console.log('   ✅ Conversational tone for Twitter');
    console.log('\n🚀 Your Social Media generator is ready!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    
    if (error.response) {
      console.error('   Status:', error.response.status);
      console.error('   Error Details:', error.response.data);
    } else if (error.code === 'ECONNREFUSED') {
      console.error('   Make sure the backend server is running:');
      console.error('   Backend: cd backend && npm start');
    }
    
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  testSocialMediaAPI();
}

module.exports = testSocialMediaAPI;
