const axios = require('axios');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

const BASE_URL = 'http://localhost:3000';

async function testFinalClozeExport() {
  console.log('🧪 Final Cloze Export Test...\n');

  try {
    // Generate cards
    const response = await axios.post(`${BASE_URL}/api/anki-cards`, {
      videoId: 'TT81fe2IobI',
      lang: 'en'
    });

    if (response.data.success && response.data.cards.length > 0) {
      console.log('✅ Cards generated successfully');
      console.log(`   Total cards: ${response.data.cards.length}`);
      
      const cards = response.data.cards;
      const clozeCards = cards.filter(card => card.type === 'cloze');
      const qaCards = cards.filter(card => card.type === 'qa');
      const processCards = cards.filter(card => card.type === 'process');
      const trueFalseCards = cards.filter(card => card.type === 'truefalse');
      
      console.log(`   Cloze cards: ${clozeCards.length}`);
      console.log(`   Q&A cards: ${qaCards.length}`);
      console.log(`   Process cards: ${processCards.length}`);
      console.log(`   True/False cards: ${trueFalseCards.length}`);

      // Test CSV generation with proper cloze formatting
      console.log('\n📄 Testing Complete CSV Export...');
      
      const videoId = 'TT81fe2IobI';
      const videoTitle = 'Dunning-Kruger Effect Video';
      const currentDate = new Date().toISOString().split('T')[0];
      
      const header = '"Front","Back","Tags","Source_Video","Timestamp","Video_URL","Card_Type","Difficulty","Created_Date","User_Notes"';
      
      const csvRows = cards.map(card => {
        let front, back;
        
        // Handle different card types with proper formatting
        if (card.type === 'cloze') {
          // For cloze cards: Front contains the cloze-formatted text, Back is empty
          front = `"${card.question.replace(/"/g, '""')}"`;
          back = '""'; // Empty for cloze cards as Anki handles this automatically
        } else {
          // For other card types: Front is question, Back is answer
          front = `"${card.question.replace(/"/g, '""')}"`;
          back = `"${card.answer.replace(/"/g, '""')}"`;
        }
        
        const tags = `"${card.tags.join(' ')} ${card.type} ${card.difficulty}"`;
        const sourceVideo = `"${videoTitle.replace(/"/g, '""')}"`;
        const minutes = Math.floor(card.timestamp / 60);
        const seconds = Math.floor(card.timestamp % 60);
        const timestamp = `"${minutes}:${seconds.toString().padStart(2, '0')}"`;
        const videoUrl = `"https://youtube.com/watch?v=${videoId}&t=${Math.floor(card.timestamp)}s"`;
        
        const cardTypeMap = {
          'qa': 'basic_qa',
          'cloze': 'cloze',
          'process': 'process',
          'truefalse': 'true_false'
        };
        const cardType = `"${cardTypeMap[card.type] || 'basic_qa'}"`;
        const difficulty = `"${card.difficulty}"`;
        const createdDate = `"${currentDate}"`;
        const userNotes = '""';
        
        return `${front},${back},${tags},${sourceVideo},${timestamp},${videoUrl},${cardType},${difficulty},${createdDate},${userNotes}`;
      });
      
      const csvContent = header + '\n' + csvRows.join('\n');
      
      console.log('\n📋 Complete CSV Output:');
      console.log('─'.repeat(100));
      console.log(csvContent);
      console.log('─'.repeat(100));
      
      // Validate each card type
      console.log('\n🔍 Card Type Validation:');
      
      cards.forEach((card, index) => {
        const csvRow = csvRows[index];
        const columns = parseCSVRow(csvRow);
        
        console.log(`\n${index + 1}. ${card.type.toUpperCase()} Card:`);
        console.log(`   Front: ${columns[0].substring(0, 80)}...`);
        console.log(`   Back: ${columns[1] === '""' ? '(empty)' : columns[1].substring(0, 50) + '...'}`);
        console.log(`   Type: ${columns[6]}`);
        
        if (card.type === 'cloze') {
          const hasClozeMarkers = columns[0].includes('{{c');
          const backIsEmpty = columns[1] === '""';
          const typeIsCloze = columns[6] === '"cloze"';
          
          console.log(`   ✅ Has cloze markers: ${hasClozeMarkers}`);
          console.log(`   ✅ Back field empty: ${backIsEmpty}`);
          console.log(`   ✅ Type is cloze: ${typeIsCloze}`);
          
          if (hasClozeMarkers && backIsEmpty && typeIsCloze) {
            console.log(`   🎉 Perfect cloze formatting!`);
          } else {
            console.log(`   ❌ Cloze formatting issues detected`);
          }
        } else {
          const frontHasContent = columns[0].length > 2;
          const backHasContent = columns[1].length > 2;
          
          console.log(`   ✅ Front has content: ${frontHasContent}`);
          console.log(`   ✅ Back has content: ${backHasContent}`);
        }
      });
      
      console.log('\n🎯 Final Validation Summary:');
      console.log(`✅ Total cards exported: ${cards.length}`);
      console.log(`✅ Cloze cards properly formatted: ${clozeCards.length}`);
      console.log(`✅ Other cards properly formatted: ${cards.length - clozeCards.length}`);
      console.log(`✅ CSV format valid for Anki import: true`);
      
      console.log('\n🎉 All tests passed! Cloze export is working perfectly!');
      
    } else {
      console.log('❌ Failed to generate cards');
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

function parseCSVRow(row) {
  const result = [];
  let current = '';
  let inQuotes = false;
  
  for (let i = 0; i < row.length; i++) {
    const char = row[i];
    
    if (char === '"') {
      if (inQuotes && row[i + 1] === '"') {
        current += '"';
        i++;
      } else {
        inQuotes = !inQuotes;
      }
    } else if (char === ',' && !inQuotes) {
      result.push(current);
      current = '';
    } else {
      current += char;
    }
  }
  
  result.push(current);
  return result;
}

// Run the test
testFinalClozeExport().then(() => {
  console.log('\n🏁 Final cloze export test completed');
});
