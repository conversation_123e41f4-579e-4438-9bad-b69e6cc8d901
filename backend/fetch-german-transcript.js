/**
 * <PERSON><PERSON>t to fetch and save a German transcript for analysis
 */

const { exec } = require('child_process');
const util = require('util');
const fs = require('fs').promises;
const path = require('path');

// Convert exec to Promise-based
const execPromise = util.promisify(exec);

async function fetchGermanTranscript() {
  const videoId = 'Gv2fzC96Z40';
  const lang = 'de'; // German
  const tempDir = path.join(__dirname, 'temp');

  try {
    // Create temp directory if it doesn't exist
    await fs.mkdir(tempDir, { recursive: true });

    // Fetch German transcript using yt-dlp
    console.log(`Fetching German transcript for ${videoId}...`);
    const command = `yt-dlp --write-auto-sub --sub-lang ${lang} --skip-download https://www.youtube.com/watch?v=${videoId} -o "${tempDir}/%(id)s.%(ext)s"`;

    const { stdout, stderr } = await execPromise(command);
    console.log(stdout);
    if (stderr) console.error(stderr);

    // Find the subtitle file
    const files = await fs.readdir(tempDir);
    const subtitleFile = files.find(file =>
      file.startsWith(videoId) &&
      file.includes(`.${lang}.`) &&
      (file.endsWith('.vtt') || file.endsWith('.srt'))
    );

    if (!subtitleFile) {
      console.error('No German subtitle file found');
      return;
    }

    console.log(`Found subtitle file: ${subtitleFile}`);

    // Read the subtitle file
    const subtitlePath = path.join(tempDir, subtitleFile);
    const subtitleContent = await fs.readFile(subtitlePath, 'utf8');

    // Save the raw subtitle content for analysis
    const rawOutputPath = path.join(__dirname, 'german-transcript-raw.vtt');
    await fs.writeFile(rawOutputPath, subtitleContent);
    console.log(`Raw German transcript saved to ${rawOutputPath}`);

    // Parse the VTT file to see what's happening
    const parsedItems = parseVttSubtitles(subtitleContent);

    // Save the parsed items for analysis
    const parsedOutputPath = path.join(__dirname, 'german-transcript-parsed.json');
    await fs.writeFile(
      parsedOutputPath,
      JSON.stringify(parsedItems, null, 2)
    );
    console.log(`Parsed German transcript saved to ${parsedOutputPath}`);

    // Create a cleaned version of the transcript
    const cleanedItems = cleanTranscriptItems(parsedItems);

    // Save the cleaned items for analysis
    const cleanedOutputPath = path.join(__dirname, 'german-transcript-cleaned.json');
    await fs.writeFile(
      cleanedOutputPath,
      JSON.stringify(cleanedItems, null, 2)
    );
    console.log(`Cleaned German transcript saved to ${cleanedOutputPath}`);

  } catch (error) {
    console.error('Error:', error);
  }
}

/**
 * Parse VTT subtitle file
 * @param {string} vttContent - Content of the VTT file
 * @returns {Array} Array of transcript items
 */
function parseVttSubtitles(vttContent) {
  // Split the content into blocks (each subtitle entry)
  const blocks = vttContent.trim().split(/\n\s*\n/);
  const transcriptItems = [];

  for (let i = 0; i < blocks.length; i++) {
    const block = blocks[i].trim();

    // Skip the WEBVTT header block
    if (block === 'WEBVTT' || block.startsWith('WEBVTT ')) {
      continue;
    }

    // Split the block into lines
    const lines = block.split(/\n/);

    // Find the line with the timestamp
    const timestampLineIndex = lines.findIndex(line => line.includes('-->'));

    if (timestampLineIndex === -1) {
      continue; // Skip blocks without timestamps
    }

    // Extract the timestamp
    const timestampLine = lines[timestampLineIndex];
    const times = timestampLine.split('-->').map(t => t.trim());
    const startTime = parseVttTimestamp(times[0]);
    const endTime = parseVttTimestamp(times[1]);

    // Extract the text (all lines after the timestamp line)
    const textLines = lines.slice(timestampLineIndex + 1);

    // Skip empty text blocks
    if (textLines.length === 0) {
      continue;
    }

    // Join all text lines with a space
    let text = textLines.join(' ');

    // Add to transcript items
    transcriptItems.push({
      start: startTime,
      end: endTime,
      text: text,
      rawText: text // Keep the raw text for analysis
    });
  }

  // Sort by start time to ensure proper sequence
  transcriptItems.sort((a, b) => a.start - b.start);

  return transcriptItems;
}

/**
 * Parse VTT timestamp to seconds
 * @param {string} timestamp - VTT timestamp (HH:MM:SS.mmm)
 * @returns {number} Time in seconds
 */
function parseVttTimestamp(timestamp) {
  // Handle both HH:MM:SS.mmm and MM:SS.mmm formats
  const parts = timestamp.split(':');
  let hours = 0, minutes = 0, seconds = 0;

  if (parts.length === 3) {
    hours = parseInt(parts[0]);
    minutes = parseInt(parts[1]);
    seconds = parseFloat(parts[2]);
  } else if (parts.length === 2) {
    minutes = parseInt(parts[0]);
    seconds = parseFloat(parts[1]);
  }

  return hours * 3600 + minutes * 60 + seconds;
}

/**
 * Clean transcript items by removing timestamp tags and fixing duplicates
 * @param {Array} items - Transcript items to clean
 * @returns {Array} Cleaned transcript items
 */
function cleanTranscriptItems(items) {
  if (!items || items.length === 0) return [];

  const cleanedItems = [];
  let previousText = '';

  for (let i = 0; i < items.length; i++) {
    const item = {...items[i]};

    // 1. Remove timestamp tags and <c> tags from text
    item.text = item.text
      .replace(/<\d+:\d+:\d+\.\d+>|<\d+:\d+\.\d+>/g, '') // Remove timestamp tags
      .replace(/<\/?c>/g, '') // Remove <c> and </c> tags
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim();

    // 2. Skip empty items
    if (!item.text) continue;

    // 3. Skip if this is a duplicate of the previous text
    if (item.text === previousText) continue;

    // 4. Add to cleaned items
    cleanedItems.push(item);
    previousText = item.text;
  }

  // 5. Fix overlapping segments
  for (let i = 0; i < cleanedItems.length - 1; i++) {
    // If the current segment's end time is after the next segment's start time
    if (cleanedItems[i].end > cleanedItems[i + 1].start) {
      // Adjust the current segment's end time to match the next segment's start time
      cleanedItems[i].end = cleanedItems[i + 1].start;
    }
  }

  // 6. Renumber the IDs
  cleanedItems.forEach((item, index) => {
    item.id = index + 1;
  });

  return cleanedItems;
}

// Run the script
fetchGermanTranscript().catch(console.error);
