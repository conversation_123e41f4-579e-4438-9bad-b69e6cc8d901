{"success": true, "videoId": "Gv2fzC96Z40", "language": "en", "cards": [{"id": "card_1", "type": "qa", "question": "What was <PERSON>'s age when he started his first company?", "answer": "<PERSON> started his first company when he was 16 years old.", "timestamp": 18, "difficulty": "beginner", "tags": ["david park", "entrepreneurship", "age"], "context": "<PERSON>'s early entrepreneurial journey.", "transcriptQuote": "I started my first company when I was 16 I'm 27 now", "selected": true}, {"id": "card_2", "type": "qa", "question": "What was the initial reaction to the line 'nothing is darker than a butthole' generated by GPT-2?", "answer": "<PERSON> and his co-founder found the line 'nothing is darker than a butthole' to be the funniest and profound.", "timestamp": 245, "difficulty": "intermediate", "tags": ["gpt-2", "ai", "content generation"], "context": "The inspiration for creating the AI app Jenny.", "transcriptQuote": "gpt2 spit out this line that said nothing is darker than a butthole and obviously as two people enamored with this Tech we were raving we're like this is the funniest line ever also it's profound", "selected": true}, {"id": "card_3", "type": "qa", "question": "What was <PERSON>'s living situation while working on his startup?", "answer": "<PERSON> was working from his parents' house in his bedroom.", "timestamp": 325, "difficulty": "beginner", "tags": ["david park", "startup life", "living situation"], "context": "The early struggles of <PERSON>'s startup.", "transcriptQuote": "so I was working from my parents house just in the bedroom", "selected": true}, {"id": "card_4", "type": "qa", "question": "What was the revenue plateau David Park's company hit before refining their product?", "answer": "<PERSON>'s company hit a plateau and couldn't get past $2,000 a month in revenue.", "timestamp": 508, "difficulty": "intermediate", "tags": ["revenue", "startup growth", "plateau"], "context": "The challenges faced by the startup before a breakthrough.", "transcriptQuote": "they hit a plateau they try everything but they can't get past $2, 000 a month in Revenue", "selected": true}, {"id": "card_5", "type": "process", "question": "What advice did <PERSON> receive about talking to users to improve his product?", "answer": "<PERSON> learned to ask users more difficult questions like why they dislike the product or what they love about other people's products, rather than just asking why they like his product.", "timestamp": 523, "difficulty": "intermediate", "tags": ["user feedback", "product development", "customer interviews"], "context": "The strategy <PERSON> used to refine his product based on user feedback.", "transcriptQuote": "it really came down to the boring stuff of just talk to your users ask the more difficult questions of you know you shouldn't be asking why do you like my product you should be asking why do you dislike my product like what like what do you love about other people's products", "selected": true}, {"id": "card_6", "type": "qa", "question": "How much money did <PERSON> receive from <PERSON>?", "answer": "<PERSON> received a $100,000 check from <PERSON>.", "timestamp": 624, "difficulty": "beginner", "tags": ["funding", "investment", "jason cala<PERSON>"], "context": "The unexpected investment received by <PERSON>'s startup.", "transcriptQuote": "and so I got a $100, 000 check from <PERSON>", "selected": true}, {"id": "card_7", "type": "qa", "question": "What was the primary reason <PERSON> and his co-founder decided to move to Malaysia after receiving funding?", "answer": "They decided to move to Malaysia to cut down their burn rate by a quarter and extend their runway, as they didn't fully trust themselves with the money.", "timestamp": 641, "difficulty": "advanced", "tags": ["financial management", "runway", "cost reduction"], "context": "The financial decisions made after securing funding.", "transcriptQuote": "the first thing we do is my co-founder and I we book a flight to Malaysia because at the very least it'll cut down our Burn by 1/4th we basically just want to extend our Runway because we kind of didn't trust ourselves with the money", "selected": true}, {"id": "card_8", "type": "<PERSON><PERSON><PERSON>e", "question": "True or False: <PERSON> felt successful immediately after receiving the $100,000 check from <PERSON>.", "answer": "False. <PERSON> states, 'it was a crazy moment getting the check but I didn't feel successful at all because now the real job of getting my hands dirty was actually getting started.'", "timestamp": 647, "difficulty": "intermediate", "tags": ["success", "mindset", "entrepreneurship"], "context": "<PERSON>'s feelings about success after receiving funding.", "transcriptQuote": "it was a crazy moment getting the check but I didn't feel successful at all because now the real job of getting my hands dirty was actually getting started", "selected": true}], "metadata": {"transcriptLength": 386, "videoDuration": "14:02", "videoDurationSeconds": 842, "targetCardCount": 10, "actualCardCount": 8, "validationRate": 80, "model": "models/gemini-2.5-flash-lite-preview-06-17"}, "cached": true, "cachedAt": "2025-07-04T08:47:49.840Z", "contentType": "anki-cards"}