{"success": true, "videoId": "KpVPST_P4W8", "language": "en", "cards": [{"id": "card_1", "type": "qa", "question": "What is the total monthly revenue generated by Ure's four online businesses?", "answer": "Ure's four online businesses currently generate $60,000 USD per month.", "timestamp": 15, "difficulty": "beginner", "tags": ["revenue", "business"], "context": "Introduction to <PERSON><PERSON>'s business success.", "transcriptQuote": "Ich betreibe vier verschiedene Unternehmen, die derzeit 60. 000 US-Dollar pro Monat einbringen.", "selected": true}, {"id": "card_2", "type": "qa", "question": "What was <PERSON><PERSON>'s first business called and what did it do?", "answer": "Ure's first business is called Rummer, which is a device that plugs into a car's OBD2 port.", "timestamp": 264, "difficulty": "beginner", "tags": ["business", "product", "<PERSON><PERSON><PERSON>"], "context": "Description of Ure's first business venture.", "transcriptQuote": "Der erste ist Rummer. Rummer ist also ein Gerät, das so aussieht und das Sie in den OBD2-Anschluss Ihres Autos stecken.", "selected": true}, {"id": "card_3", "type": "qa", "question": "How many units did Rummer sell in its first year?", "answer": "Rummer sold 6,000 units in its first year.", "timestamp": 333, "difficulty": "beginner", "tags": ["sales", "<PERSON><PERSON><PERSON>", "business"], "context": "Sales figures for the Rummer device.", "transcriptQuote": "Ich habe dieses Geschäft vor etwa zwei Jahren gegründet und letztes Jahr haben wir 6. 000 Einheiten verkauft.", "selected": true}, {"id": "card_4", "type": "qa", "question": "What is Parakeet AI?", "answer": "Parakeet AI is a real-time AI interview assistant, primarily used for job interviews.", "timestamp": 485, "difficulty": "beginner", "tags": ["business", "AI", "Parakeet AI"], "context": "Description of the Parakeet AI business.", "transcriptQuote": "Das zweite Unternehmen, das ich leite, heißt Parakeet AI. Es handelt sich im Grunde um einen KI-Interviewassistenten in Echtzeit, der hauptsächlich für Vorstellungsgespräche verwendet wird.", "selected": true}, {"id": "card_5", "type": "qa", "question": "What is <PERSON><PERSON><PERSON><PERSON>?", "answer": "Optivase is a B2B SaaS company that functions as an A/B testing tool for web flow.", "timestamp": 594, "difficulty": "beginner", "tags": ["business", "SaaS", "Optivase"], "context": "Description of the Optivase business.", "transcriptQuote": "Das dritte von mir mitgegründete Unternehmen heißt Optivase. Es handelt sich um ein B2B-SAS-Unternehmen und im Wesentlichen um ein AB-Testtool für den Web-Flow.", "selected": true}, {"id": "card_6", "type": "qa", "question": "What is the fourth company Ure founded called and what does it do?", "answer": "The fourth company is called Parakeet AI Apply Agent, which is an AI agent that applies for jobs on your behalf.", "timestamp": 744, "difficulty": "beginner", "tags": ["business", "AI", "Parakeet AI"], "context": "Description of Ure's fourth business venture.", "transcriptQuote": "Das vierte Unternehmen, das ich gerade gegründet habe, heißt Parakeet AI Apply Agent. Es handelt sich um einen KI-Agenten, der sich für Sie auf Stellen bewirbt.", "selected": true}, {"id": "card_7", "type": "qa", "question": "What did <PERSON><PERSON> initially lack when he started building products?", "answer": "<PERSON><PERSON> initially lacked knowledge on how to market his products or what marketing meant.", "timestamp": 753, "difficulty": "intermediate", "tags": ["marketing", "skills", "entrepreneurship"], "context": "<PERSON><PERSON>'s reflection on his early challenges.", "transcriptQuote": "Aber ich hatte keine wirkliche Vorstellung davon, wie ich sie vermarkten sollte oder was Marketing überhaupt bedeutet.", "selected": true}, {"id": "card_8", "type": "process", "question": "What is the process <PERSON><PERSON> follows when starting product development?", "answer": "When starting product development, Ure focuses on the MVP (Minimum Viable Product), ensuring it's good enough to make the first sale.", "timestamp": 747, "difficulty": "intermediate", "tags": ["product development", "MVP", "process"], "context": "Ure's approach to building products.", "transcriptQuote": "Wenn ich mit der Entwicklung eines Produkts beginne, konzentriere ich mich vor allem darauf, das MVP ist, richtig? MVP steht also für Minimum Viable Product (Minimum Viable Product) und der lebensfähige Teil ist sehr wichtig.", "selected": true}, {"id": "card_9", "type": "qa", "question": "According to <PERSON><PERSON>, what is a key strategy for gaining attention for a product?", "answer": "<PERSON><PERSON> suggests focusing on organic short-form content, creating fun short videos for platforms like TikTok, Instagram, and YouTube.", "timestamp": 1004, "difficulty": "intermediate", "tags": ["marketing", "content creation", "strategy"], "context": "Ure's successful marketing strategy for Rummer.", "transcriptQuote": "<PERSON><PERSON><PERSON> habe ich beispielsweise so viele Dinge ausprobiert und was dann wirklich gut funktioniert hat, war organischer Kurzinhalt. Wir machen lustige Kurzvideos für Tik Tok, Instagram und YouTube.", "selected": true}, {"id": "card_10", "type": "<PERSON><PERSON><PERSON>e", "question": "True or False: <PERSON><PERSON> believes that paid advertising channels like Facebook and Google Ads are the most effective for new online businesses.", "answer": "False. Ure suggests that most other marketing channels are saturated, making it difficult for newcomers, and recommends focusing on organic short-form content.", "timestamp": 633, "difficulty": "intermediate", "tags": ["marketing", "advertising", "strategy"], "context": "<PERSON><PERSON>'s advice on marketing channels.", "transcriptQuote": "Die meisten anderen Marketingkanäle sind so ges<PERSON>, dass es für Sie als Neueinsteiger sehr schwer sein wird, den Durchbruch zu schaffen und etwas Profitables zu schaffen. Aber mit organischen Kurzinhalten können Si<PERSON>, wenn Sie ein Telefon haben, ein Tik To<PERSON> erstellen, das Millionen Aufrufe erhält...", "selected": true}, {"id": "card_11", "type": "process", "question": "How does <PERSON><PERSON> and his co-founder approach content creation for new ideas?", "answer": "His co-founder writes five to ten ideas for hooks, main bodies, and endings, and they then try all the different combinations to create many videos.", "timestamp": 1047, "difficulty": "advanced", "tags": ["content creation", "process", "collaboration"], "context": "The process of generating content ideas.", "transcriptQuote": "Er erstellt sozusagen diese Inhaltsstapel, nicht wahr? Also schrieb er fünf bis zehn Ideen für Aufhänger, Hauptteil und <PERSON><PERSON><PERSON> auf. So würden wir beispielsweise drei <PERSON>, fün<PERSON> und drei Endungen machen. Wenn Sie nun alle verschiedenen Kombinationen ausprobieren und die drei Hooks mit fünf <PERSON> und drei Enden kombinieren, können Sie eine Menge Videos daraus machen.", "selected": true}], "metadata": {"transcriptLength": 454, "videoDuration": "17:33", "videoDurationSeconds": 1053.4, "targetCardCount": 11, "actualCardCount": 11, "validationRate": 100, "model": "models/gemini-2.5-flash-lite-preview-06-17"}, "cached": true, "cachedAt": "2025-07-04T08:18:18.942Z", "contentType": "anki-cards"}