{"success": true, "videoId": "TT81fe2IobI", "language": "en", "cards": [{"id": "card_1", "type": "qa", "question": "What is the <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> effect, according to the speaker?", "answer": "The Dunning-<PERSON>rueger effect finds that people who are bad at something tend to believe that they are actually good at it, and people who are good at something tend to believe that they are bad at it.", "timestamp": 13, "difficulty": "beginner", "tags": ["psychology", "cognitive bias"], "context": "Explaining the <PERSON><PERSON><PERSON><PERSON> effect.", "transcriptQuote": "The Dunning Krueger effect this effect finds that people who are bad at something tend to believe that they're actually good at it and people who are good at something tend to believe that they are bad at it", "selected": true}, {"id": "card_2", "type": "qa", "question": "According to the transcript, how much more likely are elderly people who believe they are better drivers to make unsafe driving errors?", "answer": "Elderly people who believe they're better drivers than most are actually four times more likely to make unsafe driving errors.", "timestamp": 31, "difficulty": "beginner", "tags": ["driving", "elderly", "safety"], "context": "Providing an example of the <PERSON><PERSON><PERSON><PERSON> effect in driving.", "transcriptQuote": "elderly people who believe they're better drivers than most are actually four times more likely to make unsafe driving air", "selected": true}, {"id": "card_3", "type": "qa", "question": "What are the four quadrants of knowledge mentioned in the transcript?", "answer": "The four quadrants of knowledge are know knowns, known unknowns, unknown knowns, and unknown unknowns.", "timestamp": 13, "difficulty": "intermediate", "tags": ["knowledge", "cognition"], "context": "Breaking down knowledge into categories to understand the <PERSON><PERSON><PERSON><PERSON><PERSON> effect.", "transcriptQuote": "so there are know knowns things that we know that we know like I know I know how to ride a bike there are known unknowns things that we know that we don't know for example I have no [ __ ] clue how quantum physics works and then there are Unknown Known things that you forgot you knew or you don't realize that you know like you still remember how to drive to the supermarket from your childhood home you just forgot that you knew that and then there are unknown stuff that you don't know that you don't know", "selected": true}, {"id": "card_4", "type": "qa", "question": "What happens to the 'horizon of knowledge' as the size of one's knowledge circle grows larger?", "answer": "As the size of your circle grows larger, the Horizon of your knowledge also grows larger.", "timestamp": 16, "difficulty": "intermediate", "tags": ["knowledge", "learning", "horizon"], "context": "Explaining the relationship between knowledge and awareness of what is unknown.", "transcriptQuote": "as the size of your circle grows larger the Horizon of your knowledge also grows larger", "selected": true}, {"id": "card_5", "type": "<PERSON><PERSON><PERSON>e", "question": "True or False: Educating people about their cognitive biases makes them less susceptible to them.", "answer": "False. Research has shown that educating people about their cognitive biases doesn't really make them any less susceptible to cognitive biases.", "timestamp": 38, "difficulty": "intermediate", "tags": ["cognitive bias", "education", "susceptibility"], "context": "Discussing the difficulty of overcoming cognitive biases.", "transcriptQuote": "research has shown that educating people about their cognitive biases doesn't really make them any less susceptible to cognitive biases", "selected": true}, {"id": "card_6", "type": "process", "question": "How does the speaker describe the process of learning basketball and its impact on perceived knowledge?", "answer": "When you know nothing, basketball seems simple. As you learn more, you discover nuances like shooting mechanics and different shots, becoming aware of what you don't know. Further learning involves defensive schemes and screens, making previous knowledge unconscious and automatic.", "timestamp": 51, "difficulty": "intermediate", "tags": ["learning", "basketball", "knowledge"], "context": "Using basketball as an example to illustrate the <PERSON><PERSON><PERSON><PERSON><PERSON> effect.", "transcriptQuote": "if you know nothing about basketball it seems simple enough you throw a ball into the net you know what you know and don't know what you don't know but as you start to learn more about basketball you discover that there are a lot of nuances how you shoot the ball the mechanics of your elbow wrist and forearm how you position the ball in your hand understanding the different shots a fadeaway a jumper a layup a finger roll an Aly oop you're beginning to become aware of all the things you don't know and there's a lot that you don't know let's say you spend another year working on basketball you've mastered a bunch of different shots and learn the shoot with good form now you're getting into the weeds of defensive schemes hand checking picks and rolls setting various kinds of screens at this point you're no longer even thinking about your shooting form or how to hit a free throw you've forgotten you know this stuff it's unconscious it's automatic it's the stuff you know but you forgot you know", "selected": true}, {"id": "card_7", "type": "qa", "question": "Why do people's beliefs often not change when challenged, according to the speaker?", "answer": "Most people's beliefs are not based on logic or reason; they are based on identity and group affiliation. When presented with contradicting data, their thought process is that they are being attacked or their tribe is being attacked.", "timestamp": 28, "difficulty": "advanced", "tags": ["beliefs", "psychology", "identity"], "context": "Explaining why challenging beliefs is often ineffective.", "transcriptQuote": "most people's beliefs are not based on logic or Reason most people's beliefs are based on identity and group affiliation and so when you show them contradicting data their thought process isn't oh I need to update my prior assumptions about the world their thought process is like I'm being attacked my tribe is being attacked", "selected": true}, {"id": "card_8", "type": "process", "question": "What is the speaker's suggested approach for dealing with upsetting or angering information online?", "answer": "Instead of jumping to conclusions, sit back and consider: 'I don't know what the [ __ ] am I saying'.", "timestamp": 15, "difficulty": "beginner", "tags": ["online behavior", "critical thinking", "humility"], "context": "Offering advice on how to react to online content.", "transcriptQuote": "instead of jumping to conclusions about that person or that cause maybe sit back and say I don't know what the [ __ ] am I saying", "selected": true}], "metadata": {"transcriptLength": 285, "videoDuration": "10:32", "videoDurationSeconds": 632.82, "targetCardCount": 8, "actualCardCount": 8, "validationRate": 100, "model": "models/gemini-2.5-flash-lite-preview-06-17"}, "cached": true, "cachedAt": "2025-07-04T08:17:43.587Z", "contentType": "anki-cards"}