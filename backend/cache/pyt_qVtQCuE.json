{"videoId": "pyt_qVtQCuE", "language": "en", "transcript": [{"id": 1, "start": 2.07, "end": 2.08, "formattedStart": "00:02", "formattedEnd": "00:02", "text": "World of Warcraft is a game of endless"}, {"id": 2, "start": 4.15, "end": 4.16, "formattedStart": "00:04", "formattedEnd": "00:04", "text": "possibilities. Thousands of items,"}, {"id": 3, "start": 6.95, "end": 6.96, "formattedStart": "00:06", "formattedEnd": "00:06", "text": "achievements, mounts, transmog, toys,"}, {"id": 4, "start": 9.509, "end": 9.519, "formattedStart": "00:09", "formattedEnd": "00:09", "text": "titles, pets, and so much more. But what"}, {"id": 5, "start": 11.19, "end": 11.2, "formattedStart": "00:11", "formattedEnd": "00:11", "text": "if I told you I'm about to take on the"}, {"id": 6, "start": 13.03, "end": 13.04, "formattedStart": "00:13", "formattedEnd": "00:13", "text": "most insane challenge Azeroth has to"}, {"id": 7, "start": 15.91, "end": 15.92, "formattedStart": "00:15", "formattedEnd": "00:15", "text": "offer? One expansion, one goal, 100%"}, {"id": 8, "start": 17.75, "end": 17.76, "formattedStart": "00:17", "formattedEnd": "00:17", "text": "completion. The twist. I'm locking"}, {"id": 9, "start": 19.67, "end": 19.68, "formattedStart": "00:19", "formattedEnd": "00:19", "text": "myself into a single expansion. No"}, {"id": 10, "start": 21.99, "end": 22, "formattedStart": "00:21", "formattedEnd": "00:22", "text": "skipping ahead, no shortcuts until I've"}, {"id": 11, "start": 23.59, "end": 23.6, "formattedStart": "00:23", "formattedEnd": "00:23", "text": "collected everything. That means every"}, {"id": 12, "start": 25.59, "end": 25.6, "formattedStart": "00:25", "formattedEnd": "00:25", "text": "achievement, every mount, every pet,"}, {"id": 13, "start": 27.67, "end": 27.68, "formattedStart": "00:27", "formattedEnd": "00:27", "text": "every toy, every quest, every rare"}, {"id": 14, "start": 29.349, "end": 29.359, "formattedStart": "00:29", "formattedEnd": "00:29", "text": "spawn. If it's part of the expansion,"}, {"id": 15, "start": 31.029, "end": 31.039, "formattedStart": "00:31", "formattedEnd": "00:31", "text": "it's on my list. It's going to be"}, {"id": 16, "start": 32.79, "end": 32.8, "formattedStart": "00:32", "formattedEnd": "00:32", "text": "grueling. It's going to be epic, and"}, {"id": 17, "start": 34.229, "end": 34.239, "formattedStart": "00:34", "formattedEnd": "00:34", "text": "it's probably going to break me. But"}, {"id": 18, "start": 35.83, "end": 35.84, "formattedStart": "00:35", "formattedEnd": "00:35", "text": "hey, someone's got to do it, right? So,"}, {"id": 19, "start": 37.91, "end": 37.92, "formattedStart": "00:37", "formattedEnd": "00:37", "text": "grab your Hearstone, stock up on snacks,"}, {"id": 20, "start": 39.51, "end": 39.52, "formattedStart": "00:39", "formattedEnd": "00:39", "text": "and get ready to join me on this"}, {"id": 21, "start": 41.43, "end": 41.44, "formattedStart": "00:41", "formattedEnd": "00:41", "text": "ultimate journey as I prove that no"}, {"id": 22, "start": 43.35, "end": 43.36, "formattedStart": "00:43", "formattedEnd": "00:43", "text": "expansion can hold me back until I've"}, {"id": 23, "start": 45.19, "end": 45.2, "formattedStart": "00:45", "formattedEnd": "00:45", "text": "completed it all. Welcome to World of"}, {"id": 24, "start": 47.75, "end": 47.76, "formattedStart": "00:47", "formattedEnd": "00:47", "text": "Warcraft expansion locked."}, {"id": 25, "start": 50.15, "end": 50.16, "formattedStart": "00:50", "formattedEnd": "00:50", "text": "Hey everyone, welcome back to expansion"}, {"id": 26, "start": 52.31, "end": 52.32, "formattedStart": "00:52", "formattedEnd": "00:52", "text": "locked. In our previous episode, I"}, {"id": 27, "start": 53.59, "end": 53.6, "formattedStart": "00:53", "formattedEnd": "00:53", "text": "finally collected the core knight"}, {"id": 28, "start": 56.31, "end": 56.32, "formattedStart": "00:56", "formattedEnd": "00:56", "text": "transmog set for cloth, plate, and mail."}, {"id": 29, "start": 57.83, "end": 57.84, "formattedStart": "00:57", "formattedEnd": "00:57", "text": "Collected three out of the four"}, {"id": 30, "start": 60.069, "end": 60.079, "formattedStart": "01:00", "formattedEnd": "01:00", "text": "quartered ancient rings towards crafting"}, {"id": 31, "start": 62.389, "end": 62.399, "formattedStart": "01:02", "formattedEnd": "01:02", "text": "the ng garam stone ring. Leveled my"}, {"id": 32, "start": 65.27, "end": 65.28, "formattedStart": "01:05", "formattedEnd": "01:05", "text": "warrior alt to level 80. Spent 10, 000"}, {"id": 33, "start": 67.83, "end": 67.84, "formattedStart": "01:07", "formattedEnd": "01:07", "text": "extra to upgrade the animma conductor to"}, {"id": 34, "start": 70.55, "end": 70.56, "formattedStart": "01:10", "formattedEnd": "01:10", "text": "level three on my warrior to farm the"}, {"id": 35, "start": 72.71000000000001, "end": 72.72, "formattedStart": "01:12", "formattedEnd": "01:12", "text": "unrelenting so that the ax will drop."}, {"id": 36, "start": 74.789, "end": 74.799, "formattedStart": "01:14", "formattedEnd": "01:14", "text": "Started playing PvP com for the first"}, {"id": 37, "start": 76.71000000000001, "end": 76.72, "formattedStart": "01:16", "formattedEnd": "01:16", "text": "time for marks of honor. Collected two"}, {"id": 38, "start": 79.03, "end": 79.03999999999999, "formattedStart": "01:19", "formattedEnd": "01:19", "text": "PvP soul shapes. Finished my first"}, {"id": 39, "start": 80.87, "end": 80.88, "formattedStart": "01:20", "formattedEnd": "01:20", "text": "Shaolance vendor, the court of night."}, {"id": 40, "start": 83.27, "end": 83.28, "formattedStart": "01:23", "formattedEnd": "01:23", "text": "And finally bought my first PvP weapon"}, {"id": 41, "start": 87.19, "end": 87.2, "formattedStart": "01:27", "formattedEnd": "01:27", "text": "set, which gives us a total of 31. 40%"}, {"id": 42, "start": 89.59, "end": 89.6, "formattedStart": "01:29", "formattedEnd": "01:29", "text": "collected in the Shadowlands expansion."}, {"id": 43, "start": 91.27, "end": 91.28, "formattedStart": "01:31", "formattedEnd": "01:31", "text": "But there's still a lot to do. So, let's"}, {"id": 44, "start": 93.59, "end": 93.6, "formattedStart": "01:33", "formattedEnd": "01:33", "text": "get into it. So, here we are back in the"}, {"id": 45, "start": 95.35, "end": 95.36, "formattedStart": "01:35", "formattedEnd": "01:35", "text": "Shadowlands, and I'm still trying to"}, {"id": 46, "start": 98.39, "end": 98.4, "formattedStart": "01:38", "formattedEnd": "01:38", "text": "complete the Night Fay Covenant zone,"}, {"id": 47, "start": 101.27000000000001, "end": 101.28, "formattedStart": "01:41", "formattedEnd": "01:41", "text": "which we are around 85% complete on the"}, {"id": 48, "start": 103.50999999999999, "end": 103.52000000000001, "formattedStart": "01:43", "formattedEnd": "01:43", "text": "actual wall zone, and the Heart of the"}, {"id": 49, "start": 105.91, "end": 105.92, "formattedStart": "01:45", "formattedEnd": "01:45", "text": "Forest is 35% complete. In the next"}, {"id": 50, "start": 107.27000000000001, "end": 107.28, "formattedStart": "01:47", "formattedEnd": "01:47", "text": "episode, I'm going to give you a rundown"}, {"id": 51, "start": 108.95, "end": 108.96000000000001, "formattedStart": "01:48", "formattedEnd": "01:48", "text": "of everything I have left to finish in"}, {"id": 52, "start": 110.95, "end": 110.96000000000001, "formattedStart": "01:50", "formattedEnd": "01:50", "text": "this zone. So, it gives you a better"}, {"id": 53, "start": 113.429, "end": 113.439, "formattedStart": "01:53", "formattedEnd": "01:53", "text": "understanding of what's actually left to"}, {"id": 54, "start": 115.35, "end": 115.36, "formattedStart": "01:55", "formattedEnd": "01:55", "text": "do before I can move on to the next"}, {"id": 55, "start": 117.429, "end": 117.439, "formattedStart": "01:57", "formattedEnd": "01:57", "text": "covenant zone."}, {"id": 56, "start": 119.50999999999999, "end": 119.52000000000001, "formattedStart": "01:59", "formattedEnd": "01:59", "text": "But right now is the weekly reset and"}, {"id": 57, "start": 121.83, "end": 121.84, "formattedStart": "02:01", "formattedEnd": "02:01", "text": "it's time to do what I do every week,"}, {"id": 58, "start": 123.91, "end": 123.92, "formattedStart": "02:03", "formattedEnd": "02:03", "text": "which is daily callings, the command"}, {"id": 59, "start": 125.59, "end": 125.6, "formattedStart": "02:05", "formattedEnd": "02:05", "text": "table, and check on the <PERSON>'s"}, {"id": 60, "start": 128.389, "end": 128.399, "formattedStart": "02:08", "formattedEnd": "02:08", "text": "Conservatory and do my weekly raids and"}, {"id": 61, "start": 131.19, "end": 131.2, "formattedStart": "02:11", "formattedEnd": "02:11", "text": "weekly quests in Coria and Zer Mortis."}, {"id": 62, "start": 132.63, "end": 132.64, "formattedStart": "02:12", "formattedEnd": "02:12", "text": "Whilst doing all my dailies and"}, {"id": 63, "start": 134.55, "end": 134.56, "formattedStart": "02:14", "formattedEnd": "02:14", "text": "weeklies, it's time for another attempt"}, {"id": 64, "start": 137.67000000000002, "end": 137.68, "formattedStart": "02:17", "formattedEnd": "02:17", "text": "at the Necroay egg. 18Th attempt on the"}, {"id": 65, "start": 139.43, "end": 139.44, "formattedStart": "02:19", "formattedEnd": "02:19", "text": "Necaray egg."}, {"id": 66, "start": 143.51, "end": 143.52, "formattedStart": "02:23", "formattedEnd": "02:23", "text": "Will we get the egg?"}, {"id": 67, "start": 146.22899999999998, "end": 146.239, "formattedStart": "02:26", "formattedEnd": "02:26", "text": "No, just grateful offerings. Damn it."}, {"id": 68, "start": 147.91, "end": 147.92000000000002, "formattedStart": "02:27", "formattedEnd": "02:27", "text": "And again, we just get grateful"}, {"id": 69, "start": 149.82999999999998, "end": 149.84, "formattedStart": "02:29", "formattedEnd": "02:29", "text": "offerings. But that's not the only thing"}, {"id": 70, "start": 152.15, "end": 152.16, "formattedStart": "02:32", "formattedEnd": "02:32", "text": "I get a chance to open this episode."}, {"id": 71, "start": 157.43, "end": 157.44, "formattedStart": "02:37", "formattedEnd": "02:37", "text": "Attempt one of the undying army. Um,"}, {"id": 72, "start": 160.55, "end": 160.56, "formattedStart": "02:40", "formattedEnd": "02:40", "text": "Paragon chest."}, {"id": 73, "start": 163.35, "end": 163.36, "formattedStart": "02:43", "formattedEnd": "02:43", "text": "Oh my god."}, {"id": 74, "start": 165.50900000000001, "end": 171.82999999999998, "formattedStart": "02:45", "formattedEnd": "02:51", "text": "First attempt. We just got the rather close."}, {"id": 75, "start": 180.949, "end": 180.959, "formattedStart": "03:00", "formattedEnd": "03:00", "text": "There we go, boys."}, {"id": 76, "start": 200.869, "end": 200.879, "formattedStart": "03:20", "formattedEnd": "03:20", "text": "I can't believe I got that. And it's a"}, {"id": 77, "start": 202.22899999999998, "end": 204.239, "formattedStart": "03:22", "formattedEnd": "03:24", "text": "cool mount as well. I actually like this mount."}, {"id": 78, "start": 206.309, "end": 206.319, "formattedStart": "03:26", "formattedEnd": "03:26", "text": "Honestly, I could not believe my luck to"}, {"id": 79, "start": 208.39, "end": 208.4, "formattedStart": "03:28", "formattedEnd": "03:28", "text": "get the Undying Army Paragon chest mount"}, {"id": 80, "start": 211.03, "end": 211.04, "formattedStart": "03:31", "formattedEnd": "03:31", "text": "on the first try. And that's not the"}, {"id": 81, "start": 212.949, "end": 212.959, "formattedStart": "03:32", "formattedEnd": "03:32", "text": "last paragon chest. We actually get to"}, {"id": 82, "start": 215.75, "end": 215.76, "formattedStart": "03:35", "formattedEnd": "03:35", "text": "open our first"}, {"id": 83, "start": 217.99, "end": 218, "formattedStart": "03:37", "formattedEnd": "03:38", "text": "supply chest. Well, sorry, Paragon chest"}, {"id": 84, "start": 219.50900000000001, "end": 221.82999999999998, "formattedStart": "03:39", "formattedEnd": "03:41", "text": "with the ascendant. Let's see what we get."}, {"id": 85, "start": 224.149, "end": 224.159, "formattedStart": "03:44", "formattedEnd": "03:44", "text": "Mark of Purity."}, {"id": 86, "start": 225.82999999999998, "end": 225.84, "formattedStart": "03:45", "formattedEnd": "03:45", "text": "A toy."}, {"id": 87, "start": 231.75, "end": 231.76, "formattedStart": "03:51", "formattedEnd": "03:51", "text": "Identify yourself as one of Bastians"}, {"id": 88, "start": 233.99, "end": 234, "formattedStart": "03:53", "formattedEnd": "03:54", "text": "chosen and earning the right to speak an"}, {"id": 89, "start": 236.63, "end": 236.64, "formattedStart": "03:56", "formattedEnd": "03:56", "text": "image of <PERSON><PERSON><PERSON> purity. Okay."}, {"id": 90, "start": 239.35, "end": 239.36, "formattedStart": "03:59", "formattedEnd": "03:59", "text": "Sure. As I continue doing my dailies in"}, {"id": 91, "start": 241.03, "end": 241.04, "formattedStart": "04:01", "formattedEnd": "04:01", "text": "Arworld, do you remember the last"}, {"id": 92, "start": 243.91, "end": 243.92, "formattedStart": "04:03", "formattedEnd": "04:03", "text": "episode where I spent an extra 10, 000 on"}, {"id": 93, "start": 246.39, "end": 246.4, "formattedStart": "04:06", "formattedEnd": "04:06", "text": "my warrior to maybe get a chance for the"}, {"id": 94, "start": 248.71, "end": 248.72, "formattedStart": "04:08", "formattedEnd": "04:08", "text": "great acts of unrelenting to drop from"}, {"id": 95, "start": 251.429, "end": 255.50900000000001, "formattedStart": "04:11", "formattedEnd": "04:15", "text": "Ve the Unrelenting rare? Well, this happened."}, {"id": 96, "start": 255.519, "end": 257.519, "formattedStart": "04:15", "formattedEnd": "04:17", "text": "Right."}, {"id": 97, "start": 262.95, "end": 262.96, "formattedStart": "04:22", "formattedEnd": "04:22", "text": "Yes. Get in. [ __ ] Get in. Oh my god."}, {"id": 98, "start": 266.55, "end": 266.56, "formattedStart": "04:26", "formattedEnd": "04:26", "text": "We got the [ __ ] axe."}, {"id": 99, "start": 270.79, "end": 270.8, "formattedStart": "04:30", "formattedEnd": "04:30", "text": "[ __ ] Let's go. No more of this dude."}, {"id": 100, "start": 272.39, "end": 272.4, "formattedStart": "04:32", "formattedEnd": "04:32", "text": "Don't have to do any more fairly"}, {"id": 101, "start": 275.909, "end": 275.919, "formattedStart": "04:35", "formattedEnd": "04:35", "text": "unrelenting. Let's [ __ ] go."}, {"id": 102, "start": 279.27, "end": 279.28, "formattedStart": "04:39", "formattedEnd": "04:39", "text": "Oh my god. Yes. Have a look at this"}, {"id": 103, "start": 282.95, "end": 282.96, "formattedStart": "04:42", "formattedEnd": "04:42", "text": "weapon. There you go. We've got the"}, {"id": 104, "start": 286.95, "end": 286.96, "formattedStart": "04:46", "formattedEnd": "04:46", "text": "great ax of unrelenting pursuit."}, {"id": 105, "start": 291.35, "end": 291.36, "formattedStart": "04:51", "formattedEnd": "04:51", "text": "The unrelentancing rare. Well, I"}, {"id": 106, "start": 293.189, "end": 293.199, "formattedStart": "04:53", "formattedEnd": "04:53", "text": "definitely think the 10, 000 armor we"}, {"id": 107, "start": 295.11, "end": 295.12, "formattedStart": "04:55", "formattedEnd": "04:55", "text": "spent was worth it because we got the"}, {"id": 108, "start": 297.11, "end": 297.12, "formattedStart": "04:57", "formattedEnd": "04:57", "text": "item to drop. Now that all the dailies"}, {"id": 109, "start": 298.71, "end": 298.72, "formattedStart": "04:58", "formattedEnd": "04:58", "text": "and weeklies are done for the week, I"}, {"id": 110, "start": 299.83, "end": 299.84000000000003, "formattedStart": "04:59", "formattedEnd": "04:59", "text": "thought it would be a good opportunity"}, {"id": 111, "start": 302.31, "end": 302.32, "formattedStart": "05:02", "formattedEnd": "05:02", "text": "to head to Tazesh the mega dungeon for"}, {"id": 112, "start": 304.31, "end": 304.32, "formattedStart": "05:04", "formattedEnd": "05:04", "text": "another attempt at the cartels master"}, {"id": 113, "start": 311.029, "end": 311.039, "formattedStart": "05:11", "formattedEnd": "05:11", "text": "Right, good luck to them, guys. Attempt"}, {"id": 114, "start": 318.469, "end": 318.479, "formattedStart": "05:18", "formattedEnd": "05:18", "text": "Damn it. Didn't get it."}, {"id": 115, "start": 320.469, "end": 320.479, "formattedStart": "05:20", "formattedEnd": "05:20", "text": "After my attempt on the cartel's master"}, {"id": 116, "start": 322.629, "end": 322.639, "formattedStart": "05:22", "formattedEnd": "05:22", "text": "gear ladder and finishing all my dailies"}, {"id": 117, "start": 324.71, "end": 324.72, "formattedStart": "05:24", "formattedEnd": "05:24", "text": "and weeklies, I headed back to Alen"}, {"id": 118, "start": 326.31, "end": 326.32, "formattedStart": "05:26", "formattedEnd": "05:26", "text": "Wheel to take a look at what more I"}, {"id": 119, "start": 328.15, "end": 328.16, "formattedStart": "05:28", "formattedEnd": "05:28", "text": "could complete in the zone. The first"}, {"id": 120, "start": 329.749, "end": 329.759, "formattedStart": "05:29", "formattedEnd": "05:29", "text": "thing that caught my eye was the"}, {"id": 121, "start": 331.59, "end": 331.6, "formattedStart": "05:31", "formattedEnd": "05:31", "text": "inscription recipe, which comes from"}, {"id": 122, "start": 334.23, "end": 334.24, "formattedStart": "05:34", "formattedEnd": "05:34", "text": "this NPC. But it wasn't as easy as it"}, {"id": 123, "start": 336.07, "end": 336.08, "formattedStart": "05:36", "formattedEnd": "05:36", "text": "looks. What I had to do to get the"}, {"id": 124, "start": 338.15, "end": 338.15999999999997, "formattedStart": "05:38", "formattedEnd": "05:38", "text": "recipe was collect an item called a fay"}, {"id": 125, "start": 340.87, "end": 340.88, "formattedStart": "05:40", "formattedEnd": "05:40", "text": "net and go and catch a veil flitter with"}, {"id": 126, "start": 342.87, "end": 342.88, "formattedStart": "05:42", "formattedEnd": "05:42", "text": "the net to give me a buff so I could"}, {"id": 127, "start": 345.189, "end": 345.199, "formattedStart": "05:45", "formattedEnd": "05:45", "text": "then collect the recipe. The problem was"}, {"id": 128, "start": 347.35, "end": 347.36, "formattedStart": "05:47", "formattedEnd": "05:47", "text": "to catch the veil flitter I had to go"}, {"id": 129, "start": 349.51, "end": 349.52, "formattedStart": "05:49", "formattedEnd": "05:49", "text": "outside the Shadowlands expansion to"}, {"id": 130, "start": 352.15, "end": 352.15999999999997, "formattedStart": "05:52", "formattedEnd": "05:52", "text": "Legion and head towards Val Shira zone."}, {"id": 131, "start": 354.15, "end": 354.15999999999997, "formattedStart": "05:54", "formattedEnd": "05:54", "text": "Veil flitter can be found all over the"}, {"id": 132, "start": 356.87, "end": 356.88, "formattedStart": "05:56", "formattedEnd": "05:56", "text": "zone. But I tell you what, this creature"}, {"id": 133, "start": 359.749, "end": 359.759, "formattedStart": "05:59", "formattedEnd": "05:59", "text": "was not easy to locate or catch. But"}, {"id": 134, "start": 361.51, "end": 361.52, "formattedStart": "06:01", "formattedEnd": "06:01", "text": "after a good hour of trying, I managed"}, {"id": 135, "start": 363.67, "end": 363.68, "formattedStart": "06:03", "formattedEnd": "06:03", "text": "to finally catch the veil flitter and"}, {"id": 136, "start": 365.749, "end": 365.759, "formattedStart": "06:05", "formattedEnd": "06:05", "text": "got the buff. So I rushed back to our"}, {"id": 137, "start": 368.629, "end": 368.639, "formattedStart": "06:08", "formattedEnd": "06:08", "text": "ward and got the inscription recipe."}, {"id": 138, "start": 370.79, "end": 370.8, "formattedStart": "06:10", "formattedEnd": "06:10", "text": "Talk to her."}, {"id": 139, "start": 373.189, "end": 377.27, "formattedStart": "06:13", "formattedEnd": "06:17", "text": "I have the moth for you. And over the mob."}, {"id": 140, "start": 381.51, "end": 381.52, "formattedStart": "06:21", "formattedEnd": "06:21", "text": "There we go. Got it. Sweet. Done."}, {"id": 141, "start": 382.87, "end": 382.88, "formattedStart": "06:22", "formattedEnd": "06:22", "text": "Now, do you remember the war quest"}, {"id": 142, "start": 384.71, "end": 384.72, "formattedStart": "06:24", "formattedEnd": "06:24", "text": "achievement? We failed something not"}, {"id": 143, "start": 386.71, "end": 386.72, "formattedStart": "06:26", "formattedEnd": "06:26", "text": "quite right. Well, you guys' stream told"}, {"id": 144, "start": 388.629, "end": 388.639, "formattedStart": "06:28", "formattedEnd": "06:28", "text": "me it was finally fixed. So, I went back"}, {"id": 145, "start": 390.469, "end": 390.479, "formattedStart": "06:30", "formattedEnd": "06:30", "text": "to see if the rumors were true. Give me"}, {"id": 146, "start": 394.23, "end": 394.24, "formattedStart": "06:34", "formattedEnd": "06:34", "text": "the Give me We got it, guys. Something"}, {"id": 147, "start": 397.029, "end": 397.039, "formattedStart": "06:37", "formattedEnd": "06:37", "text": "not quite right. Achievement finally is"}, {"id": 148, "start": 400.07, "end": 400.08, "formattedStart": "06:40", "formattedEnd": "06:40", "text": "ours. Let's go. So, maybe Blizzard does"}, {"id": 149, "start": 401.909, "end": 401.919, "formattedStart": "06:41", "formattedEnd": "06:41", "text": "know things that are broken in Shannons."}, {"id": 150, "start": 403.83, "end": 403.84000000000003, "formattedStart": "06:43", "formattedEnd": "06:43", "text": "So, hopefully they will continue fixing"}, {"id": 151, "start": 405.67, "end": 405.68, "formattedStart": "06:45", "formattedEnd": "06:45", "text": "all the problems we are having in this"}, {"id": 152, "start": 408.95, "end": 408.96, "formattedStart": "06:48", "formattedEnd": "06:48", "text": "expansion, so I won't be here forever."}, {"id": 153, "start": 410.71, "end": 410.72, "formattedStart": "06:50", "formattedEnd": "06:50", "text": "Now, there's one achievement I have been"}, {"id": 154, "start": 412.629, "end": 412.639, "formattedStart": "06:52", "formattedEnd": "06:52", "text": "kind of avoiding, and that achievement"}, {"id": 155, "start": 414.79, "end": 414.8, "formattedStart": "06:54", "formattedEnd": "06:54", "text": "is the tour of duty achievement. There's"}, {"id": 156, "start": 415.99, "end": 416, "formattedStart": "06:55", "formattedEnd": "06:56", "text": "actually four variants of this"}, {"id": 157, "start": 418.07, "end": 418.08, "formattedStart": "06:58", "formattedEnd": "06:58", "text": "achievement in Shalons for each zone"}, {"id": 158, "start": 419.99, "end": 420, "formattedStart": "06:59", "formattedEnd": "07:00", "text": "where I have to collect 1, 000 honor in"}, {"id": 159, "start": 423.11, "end": 423.12, "formattedStart": "07:03", "formattedEnd": "07:03", "text": "each zone while in war mode. Now,"}, {"id": 160, "start": 424.79, "end": 424.8, "formattedStart": "07:04", "formattedEnd": "07:04", "text": "originally I thought I would have to go"}, {"id": 161, "start": 426.71, "end": 426.72, "formattedStart": "07:06", "formattedEnd": "07:06", "text": "in war mode and start hunting players"}, {"id": 162, "start": 428.71, "end": 428.72, "formattedStart": "07:08", "formattedEnd": "07:08", "text": "down. Or my other option was to ask some"}, {"id": 163, "start": 431.11, "end": 431.12, "formattedStart": "07:11", "formattedEnd": "07:11", "text": "of you guys in stream to come online and"}, {"id": 164, "start": 433.27, "end": 433.28, "formattedStart": "07:13", "formattedEnd": "07:13", "text": "let me kill you in war mode, but what I"}, {"id": 165, "start": 435.67, "end": 435.68, "formattedStart": "07:15", "formattedEnd": "07:15", "text": "didn't know is that there is a daily PVP"}, {"id": 166, "start": 437.909, "end": 437.919, "formattedStart": "07:17", "formattedEnd": "07:17", "text": "war quest that rotates in each of the"}, {"id": 167, "start": 440.55, "end": 440.56, "formattedStart": "07:20", "formattedEnd": "07:20", "text": "four covenant zones. Each PvP world"}, {"id": 168, "start": 442.55, "end": 442.56, "formattedStart": "07:22", "formattedEnd": "07:22", "text": "quest has its own unique objectives, but"}, {"id": 169, "start": 443.99, "end": 444, "formattedStart": "07:23", "formattedEnd": "07:24", "text": "once you have completed one, you're"}, {"id": 170, "start": 446.309, "end": 446.319, "formattedStart": "07:26", "formattedEnd": "07:26", "text": "rewarded with 50 honor. Now, this means"}, {"id": 171, "start": 448.629, "end": 448.639, "formattedStart": "07:28", "formattedEnd": "07:28", "text": "I will have to do each of the four daily"}, {"id": 172, "start": 451.27, "end": 451.28, "formattedStart": "07:31", "formattedEnd": "07:31", "text": "quests 20 times to get 1, 000 honor"}, {"id": 173, "start": 453.189, "end": 453.199, "formattedStart": "07:33", "formattedEnd": "07:33", "text": "points in each of their zones for the"}, {"id": 174, "start": 454.79, "end": 454.8, "formattedStart": "07:34", "formattedEnd": "07:34", "text": "achievement. So, I would definitely add"}, {"id": 175, "start": 456.95, "end": 456.96, "formattedStart": "07:36", "formattedEnd": "07:36", "text": "this to my daily routine as it's much"}, {"id": 176, "start": 458.55, "end": 458.56, "formattedStart": "07:38", "formattedEnd": "07:38", "text": "more easier than trying to hunt down"}, {"id": 177, "start": 460.39, "end": 460.4, "formattedStart": "07:40", "formattedEnd": "07:40", "text": "players in war mode. Speaking of"}, {"id": 178, "start": 462.55, "end": 462.56, "formattedStart": "07:42", "formattedEnd": "07:42", "text": "dailies, there is one daily that has"}, {"id": 179, "start": 465.27, "end": 465.28, "formattedStart": "07:45", "formattedEnd": "07:45", "text": "just appeared in Coria and it only means"}, {"id": 180, "start": 468.629, "end": 468.639, "formattedStart": "07:48", "formattedEnd": "07:48", "text": "one thing. So, today"}, {"id": 181, "start": 472.15, "end": 473.749, "formattedStart": "07:52", "formattedEnd": "07:53", "text": "I officially in my bag have the last piece"}, {"id": 182, "start": 482.55, "end": 482.56, "formattedStart": "08:02", "formattedEnd": "08:02", "text": "and that is the full complete set for"}, {"id": 183, "start": 485.589, "end": 485.599, "formattedStart": "08:05", "formattedEnd": "08:05", "text": "Coria for night."}, {"id": 184, "start": 491.51, "end": 491.52, "formattedStart": "08:11", "formattedEnd": "08:11", "text": "This set. It only appears every so often"}, {"id": 185, "start": 494.15, "end": 494.16, "formattedStart": "08:14", "formattedEnd": "08:14", "text": "in Coria these quests and I've been"}, {"id": 186, "start": 495.909, "end": 495.919, "formattedStart": "08:15", "formattedEnd": "08:15", "text": "farming it for four months and I finally"}, {"id": 187, "start": 499.27, "end": 499.28, "formattedStart": "08:19", "formattedEnd": "08:19", "text": "have the full set finally. And I think"}, {"id": 188, "start": 500.71, "end": 500.72, "formattedStart": "08:20", "formattedEnd": "08:20", "text": "today is the day where I actually wear"}, {"id": 189, "start": 503.189, "end": 503.199, "formattedStart": "08:23", "formattedEnd": "08:23", "text": "this strand box set just because I've"}, {"id": 190, "start": 504.79, "end": 506.39, "formattedStart": "08:24", "formattedEnd": "08:26", "text": "been farming the [ __ ] out of it every day."}, {"id": 191, "start": 509.51, "end": 509.52, "formattedStart": "08:29", "formattedEnd": "08:29", "text": "And now we have it. Awesome. Finally, I"}, {"id": 192, "start": 511.43, "end": 511.44, "formattedStart": "08:31", "formattedEnd": "08:31", "text": "have now completed the Twilight Grow set"}, {"id": 193, "start": 513.909, "end": 513.919, "formattedStart": "08:33", "formattedEnd": "08:33", "text": "for Honor World, but I do have three"}, {"id": 194, "start": 516.31, "end": 518.08, "formattedStart": "08:36", "formattedEnd": "08:38", "text": "more variants of each covenant to do in Coria."}, {"id": 195, "start": 519.99, "end": 520, "formattedStart": "08:39", "formattedEnd": "08:40", "text": "The remaining three are the Kyrion,"}, {"id": 196, "start": 522.55, "end": 522.56, "formattedStart": "08:42", "formattedEnd": "08:42", "text": "Venia, and Necrolord. So, once I've"}, {"id": 197, "start": 524.389, "end": 524.399, "formattedStart": "08:44", "formattedEnd": "08:44", "text": "allied myself with those covenants, the"}, {"id": 198, "start": 526.389, "end": 526.399, "formattedStart": "08:46", "formattedEnd": "08:46", "text": "daily war quest will be available for me"}, {"id": 199, "start": 529.59, "end": 529.6, "formattedStart": "08:49", "formattedEnd": "08:49", "text": "to do in Corpia for these transmogs."}, {"id": 200, "start": 531.19, "end": 531.2, "formattedStart": "08:51", "formattedEnd": "08:51", "text": "Speaking of the more, you guys told me"}, {"id": 201, "start": 532.63, "end": 532.64, "formattedStart": "08:52", "formattedEnd": "08:52", "text": "there was an achievement that I needed"}, {"id": 202, "start": 534.23, "end": 534.24, "formattedStart": "08:54", "formattedEnd": "08:54", "text": "to collect, which is a part of the"}, {"id": 203, "start": 536.47, "end": 536.48, "formattedStart": "08:56", "formattedEnd": "08:56", "text": "Kyrion Covenant Assault for this week."}, {"id": 204, "start": 538.31, "end": 538.32, "formattedStart": "08:58", "formattedEnd": "08:58", "text": "That achievement is called the Zval"}, {"id": 205, "start": 539.91, "end": 539.92, "formattedStart": "08:59", "formattedEnd": "08:59", "text": "Shuffle. For this achievement, I had to"}, {"id": 206, "start": 542.31, "end": 542.32, "formattedStart": "09:02", "formattedEnd": "09:02", "text": "collect a parasol from <PERSON><PERSON><PERSON><PERSON>. Once I"}, {"id": 207, "start": 544.79, "end": 544.8, "formattedStart": "09:04", "formattedEnd": "09:04", "text": "had the parasol, I needed to go to five"}, {"id": 208, "start": 547.11, "end": 547.12, "formattedStart": "09:07", "formattedEnd": "09:07", "text": "of the forges and then dance within the"}, {"id": 209, "start": 549.19, "end": 549.2, "formattedStart": "09:09", "formattedEnd": "09:09", "text": "ring. Once I had danced in five of the"}, {"id": 210, "start": 551.59, "end": 551.6, "formattedStart": "09:11", "formattedEnd": "09:11", "text": "forges, I got the achievement. But that"}, {"id": 211, "start": 554.55, "end": 554.56, "formattedStart": "09:14", "formattedEnd": "09:14", "text": "wasn't the only achievement I got. There"}, {"id": 212, "start": 561.03, "end": 561.04, "formattedStart": "09:21", "formattedEnd": "09:21", "text": "did I just get the only offensive"}, {"id": 213, "start": 565.11, "end": 565.12, "formattedStart": "09:25", "formattedEnd": "09:25", "text": "achievement? What?"}, {"id": 214, "start": 566.949, "end": 566.959, "formattedStart": "09:26", "formattedEnd": "09:26", "text": "Oh, I haven't completed it. But how come"}, {"id": 215, "start": 569.19, "end": 569.2, "formattedStart": "09:29", "formattedEnd": "09:29", "text": "it says I've got it?"}, {"id": 216, "start": 570.47, "end": 570.48, "formattedStart": "09:30", "formattedEnd": "09:30", "text": "Complete for the covenant salt"}, {"id": 217, "start": 571.75, "end": 571.76, "formattedStart": "09:31", "formattedEnd": "09:31", "text": "achievements. Oh, right. So, I have got"}, {"id": 218, "start": 573.67, "end": 573.68, "formattedStart": "09:33", "formattedEnd": "09:33", "text": "all the offensive achievement. Oh, [ __ ]"}, {"id": 219, "start": 576.87, "end": 576.88, "formattedStart": "09:36", "formattedEnd": "09:36", "text": "Let's go. I didn't even know I got it."}, {"id": 220, "start": 579.99, "end": 580, "formattedStart": "09:39", "formattedEnd": "09:40", "text": "[ __ ] Today is a reset day. And you guys"}, {"id": 221, "start": 581.67, "end": 581.68, "formattedStart": "09:41", "formattedEnd": "09:41", "text": "know what that means. It's time to do"}, {"id": 222, "start": 583.99, "end": 584, "formattedStart": "09:43", "formattedEnd": "09:44", "text": "our weeklies and dailies in Shadowlands"}, {"id": 223, "start": 586.15, "end": 586.16, "formattedStart": "09:46", "formattedEnd": "09:46", "text": "again. So, we start off with Castle"}, {"id": 224, "start": 588.949, "end": 588.9590000000001, "formattedStart": "09:48", "formattedEnd": "09:48", "text": "Nafria. And today was a good day because"}, {"id": 225, "start": 592.15, "end": 592.16, "formattedStart": "09:52", "formattedEnd": "09:52", "text": "we finished heroic Castle Nathria. Thank"}, {"id": 226, "start": 594.91, "end": 597.19, "formattedStart": "09:54", "formattedEnd": "09:57", "text": "you so much, <PERSON><PERSON><PERSON>. Appreciate you. [Music]"}, {"id": 227, "start": 599.67, "end": 603.269, "formattedStart": "09:59", "formattedEnd": "10:03", "text": "There we go. Ladies and gentlemen, heroic sonafria"}, {"id": 228, "start": 606.63, "end": 608.24, "formattedStart": "10:06", "formattedEnd": "10:08", "text": "is completely finished on transmog skull."}, {"id": 229, "start": 609.829, "end": 609.839, "formattedStart": "10:09", "formattedEnd": "10:09", "text": "Now, I haven't given you an update on"}, {"id": 230, "start": 611.75, "end": 611.76, "formattedStart": "10:11", "formattedEnd": "10:11", "text": "how the raids are going in a while. So,"}, {"id": 231, "start": 613.67, "end": 613.68, "formattedStart": "10:13", "formattedEnd": "10:13", "text": "let me show you what we have done so"}, {"id": 232, "start": 615.43, "end": 615.44, "formattedStart": "10:15", "formattedEnd": "10:15", "text": "far. For <PERSON><PERSON><PERSON><PERSON>, I have fully"}, {"id": 233, "start": 616.63, "end": 616.64, "formattedStart": "10:16", "formattedEnd": "10:16", "text": "collected everything on mythic"}, {"id": 234, "start": 619.59, "end": 619.6, "formattedStart": "10:19", "formattedEnd": "10:19", "text": "difficulty, including zone drops. And"}, {"id": 235, "start": 621.75, "end": 621.76, "formattedStart": "10:21", "formattedEnd": "10:21", "text": "for heroic class, as you just saw, I've"}, {"id": 236, "start": 623.59, "end": 623.6, "formattedStart": "10:23", "formattedEnd": "10:23", "text": "collected all the transmog on that"}, {"id": 237, "start": 625.59, "end": 625.6, "formattedStart": "10:25", "formattedEnd": "10:25", "text": "difficulty. But I am missing one zone"}, {"id": 238, "start": 627.75, "end": 627.76, "formattedStart": "10:27", "formattedEnd": "10:27", "text": "drop, which is the cape and the heroic"}, {"id": 239, "start": 629.91, "end": 629.92, "formattedStart": "10:29", "formattedEnd": "10:29", "text": "skip, which if you remember from one of"}, {"id": 240, "start": 631.829, "end": 631.8389999999999, "formattedStart": "10:31", "formattedEnd": "10:31", "text": "our previous episodes, the quest will"}, {"id": 241, "start": 633.99, "end": 634, "formattedStart": "10:33", "formattedEnd": "10:34", "text": "not appear for me on Tuesday cuz I've"}, {"id": 242, "start": 635.91, "end": 635.92, "formattedStart": "10:35", "formattedEnd": "10:35", "text": "already collected the skip on mythic. So"}, {"id": 243, "start": 637.829, "end": 637.8389999999999, "formattedStart": "10:37", "formattedEnd": "10:37", "text": "I will have to come back and do it on a"}, {"id": 244, "start": 639.91, "end": 639.92, "formattedStart": "10:39", "formattedEnd": "10:39", "text": "different tune to collect and finish the"}, {"id": 245, "start": 641.91, "end": 641.92, "formattedStart": "10:41", "formattedEnd": "10:41", "text": "quest. But for now, I'm ready to move on"}, {"id": 246, "start": 645.43, "end": 645.44, "formattedStart": "10:45", "formattedEnd": "10:45", "text": "to Kasanafriia on normal difficulty."}, {"id": 247, "start": 647.19, "end": 647.2, "formattedStart": "10:47", "formattedEnd": "10:47", "text": "For sent domination, we have completed"}, {"id": 248, "start": 649.43, "end": 649.44, "formattedStart": "10:49", "formattedEnd": "10:49", "text": "mythic difficulty apart from one zone"}, {"id": 249, "start": 651.19, "end": 651.2, "formattedStart": "10:51", "formattedEnd": "10:51", "text": "drop which has a shared appearance"}, {"id": 250, "start": 653.43, "end": 653.44, "formattedStart": "10:53", "formattedEnd": "10:53", "text": "outside of the raid."}, {"id": 251, "start": 655.35, "end": 655.36, "formattedStart": "10:55", "formattedEnd": "10:55", "text": "And since we have started heroic, we are"}, {"id": 252, "start": 657.35, "end": 657.36, "formattedStart": "10:57", "formattedEnd": "10:57", "text": "now one item away from completing heroic"}, {"id": 253, "start": 659.03, "end": 659.04, "formattedStart": "10:59", "formattedEnd": "10:59", "text": "difficulty. And that item is the"}, {"id": 254, "start": 660.79, "end": 660.8, "formattedStart": "11:00", "formattedEnd": "11:00", "text": "devarian cold which drops from the"}, {"id": 255, "start": 663.19, "end": 663.2, "formattedStart": "11:03", "formattedEnd": "11:03", "text": "second to last boss, <PERSON><PERSON>. Since"}, {"id": 256, "start": 664.949, "end": 664.959, "formattedStart": "11:04", "formattedEnd": "11:04", "text": "it's on the second to last boss and one"}, {"id": 257, "start": 666.87, "end": 666.88, "formattedStart": "11:06", "formattedEnd": "11:06", "text": "item that we need, I'm going to do the"}, {"id": 258, "start": 669.59, "end": 669.6, "formattedStart": "11:09", "formattedEnd": "11:09", "text": "heroic skip just for the last item and"}, {"id": 259, "start": 671.11, "end": 672.8, "formattedStart": "11:11", "formattedEnd": "11:12", "text": "start doing four runes on normal difficulty."}, {"id": 260, "start": 674.87, "end": 674.88, "formattedStart": "11:14", "formattedEnd": "11:14", "text": "And for <PERSON><PERSON><PERSON>, the first ones, I now"}, {"id": 261, "start": 677.11, "end": 677.12, "formattedStart": "11:17", "formattedEnd": "11:17", "text": "started doing heroic difficulty. I"}, {"id": 262, "start": 678.55, "end": 678.56, "formattedStart": "11:18", "formattedEnd": "11:18", "text": "haven't fully completed the transmog"}, {"id": 263, "start": 680.87, "end": 680.88, "formattedStart": "11:20", "formattedEnd": "11:20", "text": "sets for mythic difficulty yet because I"}, {"id": 264, "start": 682.79, "end": 682.8, "formattedStart": "11:22", "formattedEnd": "11:22", "text": "can't collect them until I have all the"}, {"id": 265, "start": 684.71, "end": 684.72, "formattedStart": "11:24", "formattedEnd": "11:24", "text": "classes to level 60 to collect the"}, {"id": 266, "start": 687.11, "end": 687.12, "formattedStart": "11:27", "formattedEnd": "11:27", "text": "transmog items. Sepica is the only raid"}, {"id": 267, "start": 689.59, "end": 689.6, "formattedStart": "11:29", "formattedEnd": "11:29", "text": "in Shalons with tier set items, and some"}, {"id": 268, "start": 691.35, "end": 691.36, "formattedStart": "11:31", "formattedEnd": "11:31", "text": "items are hidden behind the catalyst in"}, {"id": 269, "start": 693.67, "end": 693.68, "formattedStart": "11:33", "formattedEnd": "11:33", "text": "<PERSON><PERSON> Mo<PERSON>is. So, as you can imagine on"}, {"id": 270, "start": 696.63, "end": 696.64, "formattedStart": "11:36", "formattedEnd": "11:36", "text": "ATT add-on, it gets a bit confusing at"}, {"id": 271, "start": 698.389, "end": 698.399, "formattedStart": "11:38", "formattedEnd": "11:38", "text": "what I need to collect and how I need to"}, {"id": 272, "start": 700.47, "end": 700.48, "formattedStart": "11:40", "formattedEnd": "11:40", "text": "do it. Even some of the zone drops have"}, {"id": 273, "start": 702.15, "end": 702.16, "formattedStart": "11:42", "formattedEnd": "11:42", "text": "shared appearances. So, I'm having to"}, {"id": 274, "start": 704.389, "end": 704.399, "formattedStart": "11:44", "formattedEnd": "11:44", "text": "double check a lot of these items. But,"}, {"id": 275, "start": 706.47, "end": 706.48, "formattedStart": "11:46", "formattedEnd": "11:46", "text": "as you can see in my warb band, I have"}, {"id": 276, "start": 708.47, "end": 708.48, "formattedStart": "11:48", "formattedEnd": "11:48", "text": "accumulated a lot of tokens for mythic"}, {"id": 277, "start": 710.55, "end": 710.56, "formattedStart": "11:50", "formattedEnd": "11:50", "text": "tier set for my alts once they reach"}, {"id": 278, "start": 712.63, "end": 712.64, "formattedStart": "11:52", "formattedEnd": "11:52", "text": "level 60. But, one thing I do know is"}, {"id": 279, "start": 714.55, "end": 714.56, "formattedStart": "11:54", "formattedEnd": "11:54", "text": "I've collected all the items the bosses"}, {"id": 280, "start": 716.949, "end": 716.9590000000001, "formattedStart": "11:56", "formattedEnd": "11:56", "text": "drop on mythic difficulty. So, I thought"}, {"id": 281, "start": 719.03, "end": 719.04, "formattedStart": "11:59", "formattedEnd": "11:59", "text": "that was a good indication to move down"}, {"id": 282, "start": 721.59, "end": 721.6, "formattedStart": "12:01", "formattedEnd": "12:01", "text": "to heroic difficulty."}, {"id": 283, "start": 723.19, "end": 723.2, "formattedStart": "12:03", "formattedEnd": "12:03", "text": "Well, there you go. There's your update"}, {"id": 284, "start": 725.43, "end": 725.44, "formattedStart": "12:05", "formattedEnd": "12:05", "text": "on all three raids. I'm really happy"}, {"id": 285, "start": 727.67, "end": 727.68, "formattedStart": "12:07", "formattedEnd": "12:07", "text": "with the progress so far, but <PERSON><PERSON>"}, {"id": 286, "start": 729.35, "end": 729.36, "formattedStart": "12:09", "formattedEnd": "12:09", "text": "the first ones is definitely going to be"}, {"id": 287, "start": 731.75, "end": 731.76, "formattedStart": "12:11", "formattedEnd": "12:11", "text": "the most annoying one to complete. After"}, {"id": 288, "start": 733.829, "end": 733.839, "formattedStart": "12:13", "formattedEnd": "12:13", "text": "I finished the castle neafia raid, I had"}, {"id": 289, "start": 735.91, "end": 735.92, "formattedStart": "12:15", "formattedEnd": "12:15", "text": "a court of the harvesters paragon chest"}, {"id": 290, "start": 737.75, "end": 737.76, "formattedStart": "12:17", "formattedEnd": "12:17", "text": "available to open. Is this the paragon"}, {"id": 291, "start": 739.99, "end": 740, "formattedStart": "12:19", "formattedEnd": "12:20", "text": "box? Yeah, it is. This is our fourth"}, {"id": 292, "start": 745.19, "end": 745.2, "formattedStart": "12:25", "formattedEnd": "12:25", "text": "attempt of a stone skin dreadwing pup."}, {"id": 293, "start": 748.069, "end": 748.079, "formattedStart": "12:28", "formattedEnd": "12:28", "text": "Fourth attempt for the co harvester."}, {"id": 294, "start": 749.35, "end": 749.36, "formattedStart": "12:29", "formattedEnd": "12:29", "text": "Dude, this is going really fast for"}, {"id": 295, "start": 751.91, "end": 751.92, "formattedStart": "12:31", "formattedEnd": "12:31", "text": "these guys. Let's see if we get it."}, {"id": 296, "start": 757.03, "end": 757.04, "formattedStart": "12:37", "formattedEnd": "12:37", "text": "Nope. 15 Fused rubies, 3, 533 gold, but"}, {"id": 297, "start": 759.03, "end": 759.04, "formattedStart": "12:39", "formattedEnd": "12:39", "text": "we didn't get a pop. But this wasn't the"}, {"id": 298, "start": 763.36, "end": 767.35, "formattedStart": "12:43", "formattedEnd": "12:47", "text": "Nice."}, {"id": 299, "start": 770.15, "end": 770.16, "formattedStart": "12:50", "formattedEnd": "12:50", "text": "Got a snail salt. Sweet. Thank you. We"}, {"id": 300, "start": 771.829, "end": 771.8389999999999, "formattedStart": "12:51", "formattedEnd": "12:51", "text": "all got to open the chest after the"}, {"id": 301, "start": 774.23, "end": 774.24, "formattedStart": "12:54", "formattedEnd": "12:54", "text": "first boss called the high value cash."}, {"id": 302, "start": 776.23, "end": 776.24, "formattedStart": "12:56", "formattedEnd": "12:56", "text": "This cash is located in the supplicate"}, {"id": 303, "start": 777.99, "end": 778, "formattedStart": "12:57", "formattedEnd": "12:58", "text": "of the first one's raid. To open this"}, {"id": 304, "start": 780.23, "end": 780.24, "formattedStart": "13:00", "formattedEnd": "13:00", "text": "chest, only one player needs to clip the"}, {"id": 305, "start": 782.23, "end": 782.24, "formattedStart": "13:02", "formattedEnd": "13:02", "text": "security override orb from the"}, {"id": 306, "start": 784.55, "end": 784.56, "formattedStart": "13:04", "formattedEnd": "13:04", "text": "taskmaster boss. But for him to drop the"}, {"id": 307, "start": 786.47, "end": 786.48, "formattedStart": "13:06", "formattedEnd": "13:06", "text": "orb, you need to kill him when he has"}, {"id": 308, "start": 788.79, "end": 788.8, "formattedStart": "13:08", "formattedEnd": "13:08", "text": "the debuff that stacks three times on"}, {"id": 309, "start": 791.269, "end": 791.279, "formattedStart": "13:11", "formattedEnd": "13:11", "text": "him called synergy. Once one player has"}, {"id": 310, "start": 793.19, "end": 793.2, "formattedStart": "13:13", "formattedEnd": "13:13", "text": "the orb, then everyone can open the"}, {"id": 311, "start": 795.35, "end": 795.36, "formattedStart": "13:15", "formattedEnd": "13:15", "text": "chest and collect the loot. After"}, {"id": 312, "start": 797.269, "end": 797.279, "formattedStart": "13:17", "formattedEnd": "13:17", "text": "supplic, the first ones, I started doing"}, {"id": 313, "start": 800.15, "end": 800.16, "formattedStart": "13:20", "formattedEnd": "13:20", "text": "my weekly quest in Zer Mortis, which"}, {"id": 314, "start": 801.829, "end": 801.8389999999999, "formattedStart": "13:21", "formattedEnd": "13:21", "text": "involves me doing war quests, collecting"}, {"id": 315, "start": 803.75, "end": 803.76, "formattedStart": "13:23", "formattedEnd": "13:23", "text": "treasures, and killing rare spawns."}, {"id": 316, "start": 805.43, "end": 805.44, "formattedStart": "13:25", "formattedEnd": "13:25", "text": "While doing this, we finally managed to"}, {"id": 317, "start": 807.269, "end": 807.279, "formattedStart": "13:27", "formattedEnd": "13:27", "text": "get the enlightened reputation to"}, {"id": 318, "start": 810.389, "end": 810.399, "formattedStart": "13:30", "formattedEnd": "13:30", "text": "exalted. And"}, {"id": 319, "start": 813.19, "end": 813.2, "formattedStart": "13:33", "formattedEnd": "13:33", "text": "oh, cool."}, {"id": 320, "start": 815.67, "end": 818.87, "formattedStart": "13:35", "formattedEnd": "13:38", "text": "We're exalted with the alliance. Finally,"}, {"id": 321, "start": 820.31, "end": 820.32, "formattedStart": "13:40", "formattedEnd": "13:40", "text": "we're exalted with the allian. Does that"}, {"id": 322, "start": 821.59, "end": 821.6, "formattedStart": "13:41", "formattedEnd": "13:41", "text": "mean we're exalted with everything or"}, {"id": 323, "start": 824.069, "end": 824.079, "formattedStart": "13:44", "formattedEnd": "13:44", "text": "what we got left?"}, {"id": 324, "start": 828.55, "end": 828.56, "formattedStart": "13:48", "formattedEnd": "13:48", "text": "Reputations. We only got activist codeex"}, {"id": 325, "start": 832.47, "end": 832.48, "formattedStart": "13:52", "formattedEnd": "13:52", "text": "avowed and the venari left."}, {"id": 326, "start": 835.59, "end": 835.6, "formattedStart": "13:55", "formattedEnd": "13:55", "text": "Sick. So, we're officially exalted with"}, {"id": 327, "start": 838.949, "end": 838.9590000000001, "formattedStart": "13:58", "formattedEnd": "13:58", "text": "the Alliance. Woohoo. But there's more."}, {"id": 328, "start": 841.11, "end": 841.12, "formattedStart": "14:01", "formattedEnd": "14:01", "text": "After finishing both Coria and Zer"}, {"id": 329, "start": 843.509, "end": 843.519, "formattedStart": "14:03", "formattedEnd": "14:03", "text": "Mortis weekly quest, I head back to Arn"}, {"id": 330, "start": 845.35, "end": 845.36, "formattedStart": "14:05", "formattedEnd": "14:05", "text": "World as there is something that I need"}, {"id": 331, "start": 849.189, "end": 849.199, "formattedStart": "14:09", "formattedEnd": "14:09", "text": "to complete after 4 months. This is it."}, {"id": 332, "start": 851.03, "end": 851.04, "formattedStart": "14:11", "formattedEnd": "14:11", "text": "We officially"}, {"id": 333, "start": 853.35, "end": 853.36, "formattedStart": "14:13", "formattedEnd": "14:13", "text": "upgrade to upgrade"}, {"id": 334, "start": 856.23, "end": 856.24, "formattedStart": "14:16", "formattedEnd": "14:16", "text": "the Queen's Conservatory to level five."}, {"id": 335, "start": 858.55, "end": 858.56, "formattedStart": "14:18", "formattedEnd": "14:18", "text": "So, all our covenant features now in the"}, {"id": 336, "start": 862.15, "end": 862.16, "formattedStart": "14:22", "formattedEnd": "14:22", "text": "Sanctum of the Nightfall are all maximum"}, {"id": 337, "start": 868.389, "end": 868.399, "formattedStart": "14:28", "formattedEnd": "14:28", "text": "it's researching, but guys, we"}, {"id": 338, "start": 870.55, "end": 870.56, "formattedStart": "14:30", "formattedEnd": "14:30", "text": "officially did it."}, {"id": 339, "start": 872.87, "end": 872.88, "formattedStart": "14:32", "formattedEnd": "14:32", "text": "Night Fay Covenant is officially"}, {"id": 340, "start": 875.35, "end": 875.36, "formattedStart": "14:35", "formattedEnd": "14:35", "text": "completely [ __ ] done. Spent all the"}, {"id": 341, "start": 877.91, "end": 877.92, "formattedStart": "14:37", "formattedEnd": "14:37", "text": "on it. It's done. All Covenant features"}, {"id": 342, "start": 880.47, "end": 880.48, "formattedStart": "14:40", "formattedEnd": "14:40", "text": "are now max level. But that's not all. I"}, {"id": 343, "start": 882.389, "end": 882.399, "formattedStart": "14:42", "formattedEnd": "14:42", "text": "fully completed."}, {"id": 344, "start": 890.629, "end": 890.639, "formattedStart": "14:50", "formattedEnd": "14:50", "text": "Ladies and gentlemen. We've done it."}, {"id": 345, "start": 893.269, "end": 893.279, "formattedStart": "14:53", "formattedEnd": "14:53", "text": "We've officially done it."}, {"id": 346, "start": 898.47, "end": 898.48, "formattedStart": "14:58", "formattedEnd": "14:58", "text": "The nafim smiths are complete to 100%."}, {"id": 347, "start": 900.31, "end": 900.32, "formattedStart": "15:00", "formattedEnd": "15:00", "text": "We've now completed the mythic nafim"}, {"id": 348, "start": 902.71, "end": 902.72, "formattedStart": "15:02", "formattedEnd": "15:02", "text": "smiths fully complete and we've"}, {"id": 349, "start": 906.069, "end": 906.079, "formattedStart": "15:06", "formattedEnd": "15:06", "text": "completed the heroic normal and lfr uh"}, {"id": 350, "start": 908.87, "end": 908.88, "formattedStart": "15:08", "formattedEnd": "15:08", "text": "nafam smiths fully complete."}, {"id": 351, "start": 910.31, "end": 910.32, "formattedStart": "15:10", "formattedEnd": "15:10", "text": "Everything's collected as you can see"}, {"id": 352, "start": 913.509, "end": 913.519, "formattedStart": "15:13", "formattedEnd": "15:13", "text": "there in the top right. 24 Out of 24"}, {"id": 353, "start": 916.23, "end": 916.24, "formattedStart": "15:16", "formattedEnd": "15:16", "text": "out of 24 for everything. Finally, after"}, {"id": 354, "start": 918.389, "end": 918.399, "formattedStart": "15:18", "formattedEnd": "15:18", "text": "quite a while of farming Castle Nafria,"}, {"id": 355, "start": 920.47, "end": 920.48, "formattedStart": "15:20", "formattedEnd": "15:20", "text": "the Nafin smiths are now crossed off"}, {"id": 356, "start": 922.87, "end": 922.88, "formattedStart": "15:22", "formattedEnd": "15:22", "text": "this list in Ardum World. Since I'm back"}, {"id": 357, "start": 924.79, "end": 924.8, "formattedStart": "15:24", "formattedEnd": "15:24", "text": "in Arn World Zone, I thought this would"}, {"id": 358, "start": 926.47, "end": 926.48, "formattedStart": "15:26", "formattedEnd": "15:26", "text": "be a good chance for me to do some more"}, {"id": 359, "start": 936.31, "end": 939.189, "formattedStart": "15:36", "formattedEnd": "15:39", "text": "Will we get it at 1050? Let's do this. Oh,"}, {"id": 360, "start": 942.629, "end": 942.639, "formattedStart": "15:42", "formattedEnd": "15:42", "text": "right. I'm hurting. See you later."}, {"id": 361, "start": 944.31, "end": 944.32, "formattedStart": "15:44", "formattedEnd": "15:44", "text": "Well, yet again, the crown does not drop"}, {"id": 362, "start": 946.389, "end": 946.399, "formattedStart": "15:46", "formattedEnd": "15:46", "text": "for me. And we are now sat at 50"}, {"id": 363, "start": 948.629, "end": 948.639, "formattedStart": "15:48", "formattedEnd": "15:48", "text": "attempts on this item. Let me know on"}, {"id": 364, "start": 950.55, "end": 950.56, "formattedStart": "15:50", "formattedEnd": "15:50", "text": "the comments how many attempts do you"}, {"id": 365, "start": 952.47, "end": 952.48, "formattedStart": "15:52", "formattedEnd": "15:52", "text": "think it will take me to get the crown"}, {"id": 366, "start": 954.629, "end": 954.639, "formattedStart": "15:54", "formattedEnd": "15:54", "text": "of autumn flora. Me personally, I think"}, {"id": 367, "start": 956.55, "end": 956.56, "formattedStart": "15:56", "formattedEnd": "15:56", "text": "it's going to be around 200 attempts or"}, {"id": 368, "start": 958.55, "end": 958.56, "formattedStart": "15:58", "formattedEnd": "15:58", "text": "more. After my failed attempts in the"}, {"id": 369, "start": 961.189, "end": 963.04, "formattedStart": "16:01", "formattedEnd": "16:03", "text": "Mr. <PERSON><PERSON><PERSON> dungeon, I am now sat at 32. 11%"}, {"id": 370, "start": 965.189, "end": 965.199, "formattedStart": "16:05", "formattedEnd": "16:05", "text": "collected in the Shadowlands expansion."}, {"id": 371, "start": 966.55, "end": 966.56, "formattedStart": "16:06", "formattedEnd": "16:06", "text": "And I think this is where I'm going to"}, {"id": 372, "start": 969.11, "end": 969.12, "formattedStart": "16:09", "formattedEnd": "16:09", "text": "leave this episode of expansion locked."}, {"id": 373, "start": 970.389, "end": 970.399, "formattedStart": "16:10", "formattedEnd": "16:10", "text": "If you've watched this episode all the"}, {"id": 374, "start": 971.59, "end": 971.6, "formattedStart": "16:11", "formattedEnd": "16:11", "text": "way to the end, I just want to say a big"}, {"id": 375, "start": 973.509, "end": 973.519, "formattedStart": "16:13", "formattedEnd": "16:13", "text": "massive thank you for watching. A big"}, {"id": 376, "start": 975.189, "end": 975.199, "formattedStart": "16:15", "formattedEnd": "16:15", "text": "shout out to my Twitch subs and YouTube"}, {"id": 377, "start": 976.949, "end": 976.959, "formattedStart": "16:16", "formattedEnd": "16:16", "text": "members who support me every single"}, {"id": 378, "start": 978.389, "end": 978.399, "formattedStart": "16:18", "formattedEnd": "16:18", "text": "week. If you enjoy this video, make sure"}, {"id": 379, "start": 979.91, "end": 979.92, "formattedStart": "16:19", "formattedEnd": "16:19", "text": "to leave a like, comment, subscribe for"}, {"id": 380, "start": 982.389, "end": 982.399, "formattedStart": "16:22", "formattedEnd": "16:22", "text": "more expansion locked episodes. And if"}, {"id": 381, "start": 983.91, "end": 983.92, "formattedStart": "16:23", "formattedEnd": "16:23", "text": "you want to watch this channel live, you"}, {"id": 382, "start": 985.59, "end": 985.6, "formattedStart": "16:25", "formattedEnd": "16:25", "text": "can on Twitch and his very channel on"}, {"id": 383, "start": 987.509, "end": 987.519, "formattedStart": "16:27", "formattedEnd": "16:27", "text": "YouTube. Thanks for watching and I'll"}], "isAutoGenerated": false}