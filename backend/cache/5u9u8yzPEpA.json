{"videoId": "5u9u8yzPEpA", "language": "en", "transcript": [{"id": 1, "start": 0.16, "end": 2.83, "formattedStart": "00:00", "formattedEnd": "00:02", "text": "I taught myself to code with chat GPT"}, {"id": 2, "start": 2.84, "end": 4.87, "formattedStart": "00:02", "formattedEnd": "00:04", "text": "and my apps have made over $10 million"}, {"id": 3, "start": 4.88, "end": 7.15, "formattedStart": "00:04", "formattedEnd": "00:07", "text": "to date how did this guy make over $10"}, {"id": 4, "start": 7.16, "end": 9.709, "formattedStart": "00:07", "formattedEnd": "00:09", "text": "million with three iPhone apps after"}, {"id": 5, "start": 9.719, "end": 11.87, "formattedStart": "00:09", "formattedEnd": "00:11", "text": "learning how to code less than 2 years"}, {"id": 6, "start": 11.88, "end": 16.51, "formattedStart": "00:11", "formattedEnd": "00:16", "text": "ago well two letters AI now with the"}, {"id": 7, "start": 16.52, "end": 18.63, "formattedStart": "00:16", "formattedEnd": "00:18", "text": "Advent of chat GPT and the"}, {"id": 8, "start": 18.64, "end": 21.75, "formattedStart": "00:18", "formattedEnd": "00:21", "text": "popularization of AI there's so many"}, {"id": 9, "start": 21.76, "end": 23.47, "formattedStart": "00:21", "formattedEnd": "00:23", "text": "different problems that you can solve"}, {"id": 10, "start": 23.48, "end": 26.23, "formattedStart": "00:23", "formattedEnd": "00:26", "text": "either in different ways or a decreased"}, {"id": 11, "start": 26.24, "end": 28.47, "formattedStart": "00:26", "formattedEnd": "00:28", "text": "cost in order to solve it I spent over"}, {"id": 12, "start": 28.48, "end": 30.349, "formattedStart": "00:28", "formattedEnd": "00:30", "text": "an hour talking to <PERSON> to figure out"}, {"id": 13, "start": 30.359, "end": 32.47, "formattedStart": "00:30", "formattedEnd": "00:32", "text": "the truth behind how we actually built"}, {"id": 14, "start": 32.48, "end": 34.51, "formattedStart": "00:32", "formattedEnd": "00:34", "text": "these million dooll AI apps and how"}, {"id": 15, "start": 34.52, "end": 36.19, "formattedStart": "00:34", "formattedEnd": "00:36", "text": "other builders can take advantage of the"}, {"id": 16, "start": 36.2, "end": 38.99, "formattedStart": "00:36", "formattedEnd": "00:38", "text": "same opportunity and he shares it all"}, {"id": 17, "start": 39, "end": 40.91, "formattedStart": "00:39", "formattedEnd": "00:40", "text": "including his process for finding"}, {"id": 18, "start": 40.92, "end": 43.27, "formattedStart": "00:40", "formattedEnd": "00:43", "text": "million dooll AI ideas and validating"}, {"id": 19, "start": 43.28, "end": 45.47, "formattedStart": "00:43", "formattedEnd": "00:45", "text": "them his blueprint for building an app"}, {"id": 20, "start": 45.48, "end": 48.069, "formattedStart": "00:45", "formattedEnd": "00:48", "text": "from scratch as a complete beginner and"}, {"id": 21, "start": 48.079, "end": 49.869, "formattedStart": "00:48", "formattedEnd": "00:49", "text": "the marketing strategy that is making"}, {"id": 22, "start": 49.879, "end": 53.029, "formattedStart": "00:49", "formattedEnd": "00:53", "text": "him Millions Tik <PERSON>k and Instagram are"}, {"id": 23, "start": 53.039, "end": 55.15, "formattedStart": "00:53", "formattedEnd": "00:55", "text": "very solvable right now you can figure"}, {"id": 24, "start": 55.16, "end": 57.83, "formattedStart": "00:55", "formattedEnd": "00:57", "text": "out the various influencer strategies to"}, {"id": 25, "start": 57.84, "end": 59.509, "formattedStart": "00:57", "formattedEnd": "00:59", "text": "funnel millions of people to your"}, {"id": 26, "start": 59.519, "end": 62.509, "formattedStart": "00:59", "formattedEnd": "01:02", "text": "product now let's get into the details"}, {"id": 27, "start": 62.519, "end": 64.869, "formattedStart": "01:02", "formattedEnd": "01:04", "text": "I'm <PERSON> and this is starter"}, {"id": 28, "start": 64.879, "end": 67.39, "formattedStart": "01:04", "formattedEnd": "01:07", "text": "[Music]"}, {"id": 29, "start": 67.4, "end": 69.83, "formattedStart": "01:07", "formattedEnd": "01:09", "text": "story welcome man it's nice to have you"}, {"id": 30, "start": 69.84, "end": 71.55, "formattedStart": "01:09", "formattedEnd": "01:11", "text": "tell us about who you are and what you"}, {"id": 31, "start": 71.56, "end": 74.99, "formattedStart": "01:11", "formattedEnd": "01:14", "text": "built yeah absolutely my name is <PERSON>"}, {"id": 32, "start": 75, "end": 77.71000000000001, "formattedStart": "01:15", "formattedEnd": "01:17", "text": "<PERSON> in one year I taught myself how"}, {"id": 33, "start": 77.72, "end": 80.789, "formattedStart": "01:17", "formattedEnd": "01:20", "text": "to code with AI the first app that I"}, {"id": 34, "start": 80.799, "end": 83.95, "formattedStart": "01:20", "formattedEnd": "01:23", "text": "built was called Riz GPT it's at 2 and2"}, {"id": 35, "start": 83.96000000000001, "end": 86.51, "formattedStart": "01:23", "formattedEnd": "01:26", "text": "million in annual revenue the second app"}, {"id": 36, "start": 86.52, "end": 88.91, "formattedStart": "01:26", "formattedEnd": "01:28", "text": "that I built was called umax once again"}, {"id": 37, "start": 88.92, "end": 91.39, "formattedStart": "01:28", "formattedEnd": "01:31", "text": "came up with the idea designed and built"}, {"id": 38, "start": 91.4, "end": 93.50999999999999, "formattedStart": "01:31", "formattedEnd": "01:33", "text": "the application and led the majority of"}, {"id": 39, "start": 93.52000000000001, "end": 95.50999999999999, "formattedStart": "01:33", "formattedEnd": "01:35", "text": "the marketing in the past year we've"}, {"id": 40, "start": 95.52000000000001, "end": 98.71000000000001, "formattedStart": "01:35", "formattedEnd": "01:38", "text": "done almost $5 million in Revenue then"}, {"id": 41, "start": 98.72, "end": 100.87, "formattedStart": "01:38", "formattedEnd": "01:40", "text": "the third app that I co-founded was"}, {"id": 42, "start": 100.88, "end": 103.55, "formattedStart": "01:40", "formattedEnd": "01:43", "text": "called CI this one I also came up with"}, {"id": 43, "start": 103.56, "end": 106.469, "formattedStart": "01:43", "formattedEnd": "01:46", "text": "the idea for though I did not build the"}, {"id": 44, "start": 106.479, "end": 108.63, "formattedStart": "01:46", "formattedEnd": "01:48", "text": "actual application I helped with the"}, {"id": 45, "start": 108.64, "end": 110.149, "formattedStart": "01:48", "formattedEnd": "01:50", "text": "designs but I have two amazing"}, {"id": 46, "start": 110.15899999999999, "end": 112.99000000000001, "formattedStart": "01:50", "formattedEnd": "01:52", "text": "co-founders that lead operations on that"}, {"id": 47, "start": 113, "end": 116.149, "formattedStart": "01:53", "formattedEnd": "01:56", "text": "CI uh was launched about 6 months ago"}, {"id": 48, "start": 116.15899999999999, "end": 118.149, "formattedStart": "01:56", "formattedEnd": "01:58", "text": "and is now doing over a million dollars"}, {"id": 49, "start": 118.15899999999999, "end": 120.709, "formattedStart": "01:58", "formattedEnd": "02:00", "text": "in Revenue per month okay so you built"}, {"id": 50, "start": 120.719, "end": 123.63, "formattedStart": "02:00", "formattedEnd": "02:03", "text": "not one but three successful AI apps"}, {"id": 51, "start": 123.64, "end": 126.149, "formattedStart": "02:03", "formattedEnd": "02:06", "text": "that are doing almost $20 million per"}, {"id": 52, "start": 126.159, "end": 128.83, "formattedStart": "02:06", "formattedEnd": "02:08", "text": "year tell me about this opportunity in"}, {"id": 53, "start": 128.84, "end": 131.35, "formattedStart": "02:08", "formattedEnd": "02:11", "text": "apps right now okay this is a great"}, {"id": 54, "start": 131.36, "end": 133.63, "formattedStart": "02:11", "formattedEnd": "02:13", "text": "question there are two primary reasons"}, {"id": 55, "start": 133.64, "end": 136.03, "formattedStart": "02:13", "formattedEnd": "02:16", "text": "that apps are so lucrative right now the"}, {"id": 56, "start": 136.04, "end": 139.11, "formattedStart": "02:16", "formattedEnd": "02:19", "text": "first is the Advent of chat GPT and the"}, {"id": 57, "start": 139.12, "end": 141.99, "formattedStart": "02:19", "formattedEnd": "02:21", "text": "popularization of AI there's so many"}, {"id": 58, "start": 142, "end": 143.79, "formattedStart": "02:22", "formattedEnd": "02:23", "text": "different problems that you can solve"}, {"id": 59, "start": 143.8, "end": 145.67000000000002, "formattedStart": "02:23", "formattedEnd": "02:25", "text": "either in different ways or at a"}, {"id": 60, "start": 145.68, "end": 148.55, "formattedStart": "02:25", "formattedEnd": "02:28", "text": "decreased cost in order to to solve it"}, {"id": 61, "start": 148.56, "end": 150.55, "formattedStart": "02:28", "formattedEnd": "02:30", "text": "with Riz GPT it would have been"}, {"id": 62, "start": 150.56, "end": 152.949, "formattedStart": "02:30", "formattedEnd": "02:32", "text": "virtually impossible to build a AI"}, {"id": 63, "start": 152.959, "end": 155.82999999999998, "formattedStart": "02:32", "formattedEnd": "02:35", "text": "dating assistant without the use of llms"}, {"id": 64, "start": 155.84, "end": 158.589, "formattedStart": "02:35", "formattedEnd": "02:38", "text": "and number two is novel distribution"}, {"id": 65, "start": 158.599, "end": 161.309, "formattedStart": "02:38", "formattedEnd": "02:41", "text": "techniques Tik Tok and Instagram are"}, {"id": 66, "start": 161.31900000000002, "end": 163.35, "formattedStart": "02:41", "formattedEnd": "02:43", "text": "very solvable right now you can figure"}, {"id": 67, "start": 163.36, "end": 165.869, "formattedStart": "02:43", "formattedEnd": "02:45", "text": "out the various influencer strategies"}, {"id": 68, "start": 165.879, "end": 168.55, "formattedStart": "02:45", "formattedEnd": "02:48", "text": "the user generated content strategies"}, {"id": 69, "start": 168.56, "end": 171.35, "formattedStart": "02:48", "formattedEnd": "02:51", "text": "organic account growth strategies to"}, {"id": 70, "start": 171.36, "end": 173.11, "formattedStart": "02:51", "formattedEnd": "02:53", "text": "funnel millions of people to your"}, {"id": 71, "start": 173.12, "end": 175.**************, "formattedStart": "02:53", "formattedEnd": "02:55", "text": "products and I don't think that the"}, {"id": 72, "start": 175.519, "end": 179.19, "formattedStart": "02:55", "formattedEnd": "02:59", "text": "algorithms were as optimized for new"}, {"id": 73, "start": 179.2, "end": 180.43, "formattedStart": "02:59", "formattedEnd": "03:00", "text": "accounts to be be able to generate"}, {"id": 74, "start": 180.44, "end": 182.83, "formattedStart": "03:00", "formattedEnd": "03:02", "text": "millions of views a few years ago but"}, {"id": 75, "start": 182.84, "end": 184.309, "formattedStart": "03:02", "formattedEnd": "03:04", "text": "now that they're all in bloodbath"}, {"id": 76, "start": 184.319, "end": 186.149, "formattedStart": "03:04", "formattedEnd": "03:06", "text": "competition with one another they have"}, {"id": 77, "start": 186.159, "end": 188.43, "formattedStart": "03:06", "formattedEnd": "03:08", "text": "to incentivize new creators and so"}, {"id": 78, "start": 188.44, "end": 190.789, "formattedStart": "03:08", "formattedEnd": "03:10", "text": "they'll push more views to new accounts"}, {"id": 79, "start": 190.799, "end": 192.83, "formattedStart": "03:10", "formattedEnd": "03:12", "text": "than they did previously all right so"}, {"id": 80, "start": 192.84, "end": 194.63, "formattedStart": "03:12", "formattedEnd": "03:14", "text": "essentially apps have become way easier"}, {"id": 81, "start": 194.64, "end": 197.07, "formattedStart": "03:14", "formattedEnd": "03:17", "text": "to build and Market recently which is"}, {"id": 82, "start": 197.**************, "end": 198.35, "formattedStart": "03:17", "formattedEnd": "03:18", "text": "why they're a great business to start"}, {"id": 83, "start": 198.36, "end": 199.71, "formattedStart": "03:18", "formattedEnd": "03:19", "text": "for anyone that's watching this right"}, {"id": 84, "start": 199.72, "end": 202.309, "formattedStart": "03:19", "formattedEnd": "03:22", "text": "now but let's go back I want you to"}, {"id": 85, "start": 202.319, "end": 204.149, "formattedStart": "03:22", "formattedEnd": "03:24", "text": "actually share the story of how you got"}, {"id": 86, "start": 204.159, "end": 205.91, "formattedStart": "03:24", "formattedEnd": "03:25", "text": "started with this whole thing I've"}, {"id": 87, "start": 205.92000000000002, "end": 208.11, "formattedStart": "03:25", "formattedEnd": "03:28", "text": "always been fascinated by"}, {"id": 88, "start": 208.12, "end": 210.75, "formattedStart": "03:28", "formattedEnd": "03:30", "text": "entrepreneurship always hustl throughout"}, {"id": 89, "start": 210.76, "end": 212.91, "formattedStart": "03:30", "formattedEnd": "03:32", "text": "childhood figuring out new ways to make"}, {"id": 90, "start": 212.92000000000002, "end": 215.22899999999998, "formattedStart": "03:32", "formattedEnd": "03:35", "text": "money mowing lawns raking leaves"}, {"id": 91, "start": 215.239, "end": 217.55, "formattedStart": "03:35", "formattedEnd": "03:37", "text": "shoveling Drive waves then in high"}, {"id": 92, "start": 217.56, "end": 219.35, "formattedStart": "03:37", "formattedEnd": "03:39", "text": "school I was still hustling like I built"}, {"id": 93, "start": 219.36, "end": 222.35, "formattedStart": "03:39", "formattedEnd": "03:42", "text": "an ethereum mining machine back in 2016"}, {"id": 94, "start": 222.36, "end": 226.67000000000002, "formattedStart": "03:42", "formattedEnd": "03:46", "text": "or 2017 scaled Instagram meme pages but"}, {"id": 95, "start": 226.68, "end": 228.99, "formattedStart": "03:46", "formattedEnd": "03:48", "text": "to be honest I drank way too much"}, {"id": 96, "start": 229, "end": 231.19, "formattedStart": "03:49", "formattedEnd": "03:51", "text": "alcohol did too many drugs really just"}, {"id": 97, "start": 231.2, "end": 233.429, "formattedStart": "03:51", "formattedEnd": "03:53", "text": "prioritized like partying and living a"}, {"id": 98, "start": 233.439, "end": 236.309, "formattedStart": "03:53", "formattedEnd": "03:56", "text": "relatively degenerate lifestyle after"}, {"id": 99, "start": 236.31900000000002, "end": 238.63, "formattedStart": "03:56", "formattedEnd": "03:58", "text": "college so senior year I'm graduating"}, {"id": 100, "start": 238.64, "end": 240.63, "formattedStart": "03:58", "formattedEnd": "04:00", "text": "all of my friends have great jobs in New"}, {"id": 101, "start": 240.64, "end": 243.83, "formattedStart": "04:00", "formattedEnd": "04:03", "text": "York City making six figures and I had"}, {"id": 102, "start": 243.84, "end": 246.11, "formattedStart": "04:03", "formattedEnd": "04:06", "text": "nothing you know I had moved back home"}, {"id": 103, "start": 246.12, "end": 248.67, "formattedStart": "04:06", "formattedEnd": "04:08", "text": "family isn't doing well financially"}, {"id": 104, "start": 248.68, "end": 250.71, "formattedStart": "04:08", "formattedEnd": "04:10", "text": "house is going up on the market my older"}, {"id": 105, "start": 250.72, "end": 252.99, "formattedStart": "04:10", "formattedEnd": "04:12", "text": "brother is giving me loans for groceries"}, {"id": 106, "start": 253, "end": 254.55, "formattedStart": "04:13", "formattedEnd": "04:14", "text": "I was like okay I got to figure"}, {"id": 107, "start": 254.56, "end": 256.509, "formattedStart": "04:14", "formattedEnd": "04:16", "text": "something out I do not want to get a"}, {"id": 108, "start": 256.519, "end": 258.87, "formattedStart": "04:16", "formattedEnd": "04:18", "text": "full-time job and I set a goal for"}, {"id": 109, "start": 258.88, "end": 261.069, "formattedStart": "04:18", "formattedEnd": "04:21", "text": "myself right as I graduated college I"}, {"id": 110, "start": 261.079, "end": 262.749, "formattedStart": "04:21", "formattedEnd": "04:22", "text": "was like over the course of the next 12"}, {"id": 111, "start": 262.759, "end": 264.749, "formattedStart": "04:22", "formattedEnd": "04:24", "text": "months I will figure out a way to make"}, {"id": 112, "start": 264.759, "end": 267.43, "formattedStart": "04:24", "formattedEnd": "04:27", "text": "$50, 000 on my own all right so you're"}, {"id": 113, "start": 267.44, "end": 269.27, "formattedStart": "04:27", "formattedEnd": "04:29", "text": "back at your parents house borrowing"}, {"id": 114, "start": 269.28, "end": 270.79, "formattedStart": "04:29", "formattedEnd": "04:30", "text": "money from from your brother to buy"}, {"id": 115, "start": 270.8, "end": 273.189, "formattedStart": "04:30", "formattedEnd": "04:33", "text": "groceries but you set this crazy goal"}, {"id": 116, "start": 273.199, "end": 275.469, "formattedStart": "04:33", "formattedEnd": "04:35", "text": "for yourself to make $50, 000 over the"}, {"id": 117, "start": 275.479, "end": 277.67, "formattedStart": "04:35", "formattedEnd": "04:37", "text": "next year I suppose this is where your"}, {"id": 118, "start": 277.68, "end": 281.15, "formattedStart": "04:37", "formattedEnd": "04:41", "text": "first app Riz gbt comes into play right"}, {"id": 119, "start": 281.15999999999997, "end": 283.629, "formattedStart": "04:41", "formattedEnd": "04:43", "text": "how do you get started with it yeah we"}, {"id": 120, "start": 283.639, "end": 285.67, "formattedStart": "04:43", "formattedEnd": "04:45", "text": "had friends that would send in"}, {"id": 121, "start": 285.68, "end": 287.59000000000003, "formattedStart": "04:45", "formattedEnd": "04:47", "text": "fraternity group chats or whatever group"}, {"id": 122, "start": 287.6, "end": 289.35, "formattedStart": "04:47", "formattedEnd": "04:49", "text": "chats with guys saying like what should"}, {"id": 123, "start": 289.36, "end": 291.35, "formattedStart": "04:49", "formattedEnd": "04:51", "text": "I say to this girl and we figured that"}, {"id": 124, "start": 291.36, "end": 293.39, "formattedStart": "04:51", "formattedEnd": "04:53", "text": "we could build an app to help people"}, {"id": 125, "start": 293.4, "end": 295.95, "formattedStart": "04:53", "formattedEnd": "04:55", "text": "respond to girls I taught myself to code"}, {"id": 126, "start": 295.96, "end": 299.07, "formattedStart": "04:55", "formattedEnd": "04:59", "text": "with chat gbt in the month of uh May"}, {"id": 127, "start": 299.08, "end": 301.99, "formattedStart": "04:59", "formattedEnd": "05:01", "text": "June 2023 and then we launched in July"}, {"id": 128, "start": 302, "end": 305.23, "formattedStart": "05:02", "formattedEnd": "05:05", "text": "of 2023 the first version of the app was"}, {"id": 129, "start": 305.24, "end": 307.87, "formattedStart": "05:05", "formattedEnd": "05:07", "text": "so bad I built this myself we didn't"}, {"id": 130, "start": 307.88, "end": 309.749, "formattedStart": "05:07", "formattedEnd": "05:09", "text": "have notifications we didn't have"}, {"id": 131, "start": 309.759, "end": 312.59, "formattedStart": "05:09", "formattedEnd": "05:12", "text": "reviews The Design was a mess the pay"}, {"id": 132, "start": 312.6, "end": 314.51, "formattedStart": "05:12", "formattedEnd": "05:14", "text": "wall was terrible and the first few"}, {"id": 133, "start": 314.52, "end": 316.39, "formattedStart": "05:14", "formattedEnd": "05:16", "text": "promos go out and it doesn't really do"}, {"id": 134, "start": 316.4, "end": 318.909, "formattedStart": "05:16", "formattedEnd": "05:18", "text": "anything few downloads we're making a"}, {"id": 135, "start": 318.919, "end": 321.59, "formattedStart": "05:18", "formattedEnd": "05:21", "text": "few bucks and then I find these two"}, {"id": 136, "start": 321.6, "end": 323.469, "formattedStart": "05:21", "formattedEnd": "05:23", "text": "underground kind of undiscovered"}, {"id": 137, "start": 323.479, "end": 326.55, "formattedStart": "05:23", "formattedEnd": "05:26", "text": "creators I pay them each $50 for promo"}, {"id": 138, "start": 326.56, "end": 330.95, "formattedStart": "05:26", "formattedEnd": "05:30", "text": "so $100 total and overnight 5 10 million"}, {"id": 139, "start": 330.96, "end": 333.79, "formattedStart": "05:30", "formattedEnd": "05:33", "text": "views total 45, 000 downloads in that"}, {"id": 140, "start": 333.8, "end": 336.30899999999997, "formattedStart": "05:33", "formattedEnd": "05:36", "text": "first big day 200, 000 downloads on that"}, {"id": 141, "start": 336.319, "end": 338.51, "formattedStart": "05:36", "formattedEnd": "05:38", "text": "week 500, 000 downloads on the month"}, {"id": 142, "start": 338.52, "end": 341.39, "formattedStart": "05:38", "formattedEnd": "05:41", "text": "we're at 80k Mr from the jump that's"}, {"id": 143, "start": 341.4, "end": 343.189, "formattedStart": "05:41", "formattedEnd": "05:43", "text": "pretty much all profit we ended up"}, {"id": 144, "start": 343.199, "end": 347.15, "formattedStart": "05:43", "formattedEnd": "05:47", "text": "scaling it to a little bit over 250k Mr"}, {"id": 145, "start": 347.15999999999997, "end": 348.629, "formattedStart": "05:47", "formattedEnd": "05:48", "text": "and then it kind of dropped down in"}, {"id": 146, "start": 348.639, "end": 351.07, "formattedStart": "05:48", "formattedEnd": "05:51", "text": "plateaued to like 150 to 200k Mr"}, {"id": 147, "start": 351.08, "end": 352.469, "formattedStart": "05:51", "formattedEnd": "05:52", "text": "consistently and that's where it's at"}, {"id": 148, "start": 352.479, "end": 354.629, "formattedStart": "05:52", "formattedEnd": "05:54", "text": "Now <PERSON> is proof that you don't need"}, {"id": 149, "start": 354.639, "end": 356.55, "formattedStart": "05:54", "formattedEnd": "05:56", "text": "the perfect app or website to start"}, {"id": 150, "start": 356.56, "end": 358.83, "formattedStart": "05:56", "formattedEnd": "05:58", "text": "making thousands of dollars off an idea"}, {"id": 151, "start": 358.84000000000003, "end": 360.95, "formattedStart": "05:58", "formattedEnd": "06:00", "text": "all you need is an an MVP in a smart"}, {"id": 152, "start": 360.96, "end": 363.43, "formattedStart": "06:00", "formattedEnd": "06:03", "text": "marketing strategy but you also need to"}, {"id": 153, "start": 363.44, "end": 365.35, "formattedStart": "06:03", "formattedEnd": "06:05", "text": "make sure that you're working on a solid"}, {"id": 154, "start": 365.36, "end": 367.589, "formattedStart": "06:05", "formattedEnd": "06:07", "text": "idea that's why I created the starter"}, {"id": 155, "start": 367.599, "end": 369.469, "formattedStart": "06:07", "formattedEnd": "06:09", "text": "story Academy a place that helps you"}, {"id": 156, "start": 369.479, "end": 372.55, "formattedStart": "06:09", "formattedEnd": "06:12", "text": "find an idea validate the idea with real"}, {"id": 157, "start": 372.56, "end": 375.15, "formattedStart": "06:12", "formattedEnd": "06:15", "text": "feedback and shows you how to execute on"}, {"id": 158, "start": 375.16, "end": 377.11, "formattedStart": "06:15", "formattedEnd": "06:17", "text": "that idea so you can get those first"}, {"id": 159, "start": 377.12, "end": 379.23, "formattedStart": "06:17", "formattedEnd": "06:19", "text": "users so if you're curious about"}, {"id": 160, "start": 379.24, "end": 381.189, "formattedStart": "06:19", "formattedEnd": "06:21", "text": "building a software product like <PERSON>"}, {"id": 161, "start": 381.199, "end": 383.029, "formattedStart": "06:21", "formattedEnd": "06:23", "text": "and hundreds of other Founders just like"}, {"id": 162, "start": 383.039, "end": 384.749, "formattedStart": "06:23", "formattedEnd": "06:24", "text": "him head to the first link in the"}, {"id": 163, "start": 384.759, "end": 387.469, "formattedStart": "06:24", "formattedEnd": "06:27", "text": "description to check out the academy now"}, {"id": 164, "start": 387.479, "end": 389.71, "formattedStart": "06:27", "formattedEnd": "06:29", "text": "back to the video all right Blake you're"}, {"id": 165, "start": 389.72, "end": 392.11, "formattedStart": "06:29", "formattedEnd": "06:32", "text": "story is insane mobile apps totally"}, {"id": 166, "start": 392.12, "end": 394.35, "formattedStart": "06:32", "formattedEnd": "06:34", "text": "change your life yeah now let's talk"}, {"id": 167, "start": 394.36, "end": 396.35, "formattedStart": "06:34", "formattedEnd": "06:36", "text": "about how our viewers could do something"}, {"id": 168, "start": 396.36, "end": 398.589, "formattedStart": "06:36", "formattedEnd": "06:38", "text": "similar everything starts with an idea"}, {"id": 169, "start": 398.599, "end": 399.749, "formattedStart": "06:38", "formattedEnd": "06:39", "text": "right what do you think are some of the"}, {"id": 170, "start": 399.759, "end": 401.67, "formattedStart": "06:39", "formattedEnd": "06:41", "text": "mobile app ideas that could be turned"}, {"id": 171, "start": 401.68, "end": 404.30899999999997, "formattedStart": "06:41", "formattedEnd": "06:44", "text": "into millions of dollars right now yeah"}, {"id": 172, "start": 404.319, "end": 405.99, "formattedStart": "06:44", "formattedEnd": "06:45", "text": "number one I think that there are a lot"}, {"id": 173, "start": 406, "end": 408.15, "formattedStart": "06:46", "formattedEnd": "06:48", "text": "of fragmented tools it's like AI"}, {"id": 174, "start": 408.15999999999997, "end": 410.749, "formattedStart": "06:48", "formattedEnd": "06:50", "text": "LinkedIn image generator or like AI"}, {"id": 175, "start": 410.759, "end": 413.87, "formattedStart": "06:50", "formattedEnd": "06:53", "text": "resume analysis but I think that there's"}, {"id": 176, "start": 413.88, "end": 416.39, "formattedStart": "06:53", "formattedEnd": "06:56", "text": "massive opportunity to build a career AI"}, {"id": 177, "start": 416.4, "end": 418.55, "formattedStart": "06:56", "formattedEnd": "06:58", "text": "style platform where you roll up all"}, {"id": 178, "start": 418.56, "end": 420.749, "formattedStart": "06:58", "formattedEnd": "07:00", "text": "these tools into one platform and brand"}, {"id": 179, "start": 420.759, "end": 423.35, "formattedStart": "07:00", "formattedEnd": "07:03", "text": "it as like if you're a student and you"}, {"id": 180, "start": 423.36, "end": 425.909, "formattedStart": "07:03", "formattedEnd": "07:05", "text": "want to get a job this is the platform"}, {"id": 181, "start": 425.919, "end": 427.83, "formattedStart": "07:05", "formattedEnd": "07:07", "text": "to help you do so uh and you could build"}, {"id": 182, "start": 427.84, "end": 429.39, "formattedStart": "07:07", "formattedEnd": "07:09", "text": "that in a mobile app as well as a web"}, {"id": 183, "start": 429.4, "end": 432.39, "formattedStart": "07:09", "formattedEnd": "07:12", "text": "app next up language learning the chat"}, {"id": 184, "start": 432.4, "end": 434.749, "formattedStart": "07:12", "formattedEnd": "07:14", "text": "gbt advanced voice mode it's pretty"}, {"id": 185, "start": 434.759, "end": 437.309, "formattedStart": "07:14", "formattedEnd": "07:17", "text": "incredible it's like actually a personal"}, {"id": 186, "start": 437.319, "end": 440.909, "formattedStart": "07:17", "formattedEnd": "07:20", "text": "tutor <PERSON><PERSON><PERSON> is making $33 million a"}, {"id": 187, "start": 440.919, "end": 442.95, "formattedStart": "07:20", "formattedEnd": "07:22", "text": "month multiply that by 12 and we get"}, {"id": 188, "start": 442.96, "end": 445.67, "formattedStart": "07:22", "formattedEnd": "07:25", "text": "like $400 million per year you capture"}, {"id": 189, "start": 445.68, "end": 447.749, "formattedStart": "07:25", "formattedEnd": "07:27", "text": "1% of that market and you have a $4"}, {"id": 190, "start": 447.759, "end": 450.71, "formattedStart": "07:27", "formattedEnd": "07:30", "text": "million annual application on your hands"}, {"id": 191, "start": 450.72, "end": 454.30899999999997, "formattedStart": "07:30", "formattedEnd": "07:34", "text": "that's crazy honestly to me it seems"}, {"id": 192, "start": 454.319, "end": 455.51, "formattedStart": "07:34", "formattedEnd": "07:35", "text": "like you're the type of person that"}, {"id": 193, "start": 455.52, "end": 457.39, "formattedStart": "07:35", "formattedEnd": "07:37", "text": "never runs out of ideas no matter how"}, {"id": 194, "start": 457.4, "end": 459.39, "formattedStart": "07:37", "formattedEnd": "07:39", "text": "many things you build so let's talk"}, {"id": 195, "start": 459.4, "end": 461.15, "formattedStart": "07:39", "formattedEnd": "07:41", "text": "about that what are your methods for"}, {"id": 196, "start": 461.15999999999997, "end": 464.149, "formattedStart": "07:41", "formattedEnd": "07:44", "text": "finding winning ideas I've used honestly"}, {"id": 197, "start": 464.159, "end": 465.869, "formattedStart": "07:44", "formattedEnd": "07:45", "text": "different methods of finding the idea"}, {"id": 198, "start": 465.879, "end": 467.869, "formattedStart": "07:45", "formattedEnd": "07:47", "text": "for each app that I've worked on with"}, {"id": 199, "start": 467.879, "end": 470.629, "formattedStart": "07:47", "formattedEnd": "07:50", "text": "Riz GPT it was very clear problem that I"}, {"id": 200, "start": 470.639, "end": 472.71, "formattedStart": "07:50", "formattedEnd": "07:52", "text": "had observed with people that I know"}, {"id": 201, "start": 472.72, "end": 474.189, "formattedStart": "07:52", "formattedEnd": "07:54", "text": "people don't know what to say on dating"}, {"id": 202, "start": 474.199, "end": 476.029, "formattedStart": "07:54", "formattedEnd": "07:56", "text": "apps and you build an app to solve it"}, {"id": 203, "start": 476.039, "end": 477.909, "formattedStart": "07:56", "formattedEnd": "07:57", "text": "umax on the other hand was identified a"}, {"id": 204, "start": 477.919, "end": 479.869, "formattedStart": "07:57", "formattedEnd": "07:59", "text": "little bit more opportunistically when"}, {"id": 205, "start": 479.879, "end": 481.35, "formattedStart": "07:59", "formattedEnd": "08:01", "text": "people want to become more attractive"}, {"id": 206, "start": 481.36, "end": 482.909, "formattedStart": "08:01", "formattedEnd": "08:02", "text": "they're willing to spend money on it"}, {"id": 207, "start": 482.919, "end": 484.749, "formattedStart": "08:02", "formattedEnd": "08:04", "text": "they were spending money on skin care"}, {"id": 208, "start": 484.759, "end": 487.67, "formattedStart": "08:04", "formattedEnd": "08:07", "text": "<PERSON> <PERSON> the whole nine yards"}, {"id": 209, "start": 487.68, "end": 489.589, "formattedStart": "08:07", "formattedEnd": "08:09", "text": "but there didn't exist any software to"}, {"id": 210, "start": 489.599, "end": 491.11, "formattedStart": "08:09", "formattedEnd": "08:11", "text": "solve the problem that people were"}, {"id": 211, "start": 491.12, "end": 493.27, "formattedStart": "08:11", "formattedEnd": "08:13", "text": "having when you find a niche that's"}, {"id": 212, "start": 493.28, "end": 495.749, "formattedStart": "08:13", "formattedEnd": "08:15", "text": "heavily monetized with physical products"}, {"id": 213, "start": 495.759, "end": 497.469, "formattedStart": "08:15", "formattedEnd": "08:17", "text": "that's a good indicator that the niche"}, {"id": 214, "start": 497.479, "end": 500.309, "formattedStart": "08:17", "formattedEnd": "08:20", "text": "converts it's also a good indicator that"}, {"id": 215, "start": 500.319, "end": 502.71, "formattedStart": "08:20", "formattedEnd": "08:22", "text": "you can probably build a software or"}, {"id": 216, "start": 502.72, "end": 505.35, "formattedStart": "08:22", "formattedEnd": "08:25", "text": "application to solve a problem within it"}, {"id": 217, "start": 505.36, "end": 507.07, "formattedStart": "08:25", "formattedEnd": "08:27", "text": "I think that there's still a lot of room"}, {"id": 218, "start": 507.08, "end": 510.149, "formattedStart": "08:27", "formattedEnd": "08:30", "text": "for like an all-in-one AI glowup applic"}, {"id": 219, "start": 510.159, "end": 512.149, "formattedStart": "08:30", "formattedEnd": "08:32", "text": "umax was kind of like the first"}, {"id": 220, "start": 512.159, "end": 514.389, "formattedStart": "08:32", "formattedEnd": "08:34", "text": "iteration but if someone were to put"}, {"id": 221, "start": 514.399, "end": 516.829, "formattedStart": "08:34", "formattedEnd": "08:36", "text": "real time investment into building a"}, {"id": 222, "start": 516.*************, "end": 518.55, "formattedStart": "08:36", "formattedEnd": "08:38", "text": "super valuable application here that"}, {"id": 223, "start": 518.56, "end": 520.63, "formattedStart": "08:38", "formattedEnd": "08:40", "text": "really helps people improve I think that"}, {"id": 224, "start": 520.64, "end": 522.79, "formattedStart": "08:40", "formattedEnd": "08:42", "text": "there's still uh millions and millions"}, {"id": 225, "start": 522.8, "end": 525.15, "formattedStart": "08:42", "formattedEnd": "08:45", "text": "on the table there all right man thank"}, {"id": 226, "start": 525.16, "end": 527.269, "formattedStart": "08:45", "formattedEnd": "08:47", "text": "you for sharing that now that we have"}, {"id": 227, "start": 527.279, "end": 529.03, "formattedStart": "08:47", "formattedEnd": "08:49", "text": "not only the exact ideas that could be"}, {"id": 228, "start": 529.04, "end": 531.07, "formattedStart": "08:49", "formattedEnd": "08:51", "text": "built right now but also your methods"}, {"id": 229, "start": 531.08, "end": 533.71, "formattedStart": "08:51", "formattedEnd": "08:53", "text": "for coming up with more winning ideas do"}, {"id": 230, "start": 533.72, "end": 535.75, "formattedStart": "08:53", "formattedEnd": "08:55", "text": "you have a specific process for"}, {"id": 231, "start": 535.76, "end": 538.47, "formattedStart": "08:55", "formattedEnd": "08:58", "text": "validating these ideas my validation"}, {"id": 232, "start": 538.48, "end": 541.79, "formattedStart": "08:58", "formattedEnd": "09:01", "text": "process primarily looks like a deep dive"}, {"id": 233, "start": 541.8, "end": 544.87, "formattedStart": "09:01", "formattedEnd": "09:04", "text": "into the uh Niche on social media so"}, {"id": 234, "start": 544.88, "end": 547.03, "formattedStart": "09:04", "formattedEnd": "09:07", "text": "what I'll do is I'll create an account"}, {"id": 235, "start": 547.04, "end": 550.389, "formattedStart": "09:07", "formattedEnd": "09:10", "text": "say Cal and then purely consume"}, {"id": 236, "start": 550.399, "end": 552.829, "formattedStart": "09:10", "formattedEnd": "09:12", "text": "nutrition and dieting and calorie"}, {"id": 237, "start": 552.839, "end": 554.71, "formattedStart": "09:12", "formattedEnd": "09:14", "text": "counting weightlifting content"}, {"id": 238, "start": 554.72, "end": 556.87, "formattedStart": "09:14", "formattedEnd": "09:16", "text": "essentially becoming the target market"}, {"id": 239, "start": 556.88, "end": 559.31, "formattedStart": "09:16", "formattedEnd": "09:19", "text": "or Target demographic and then thinking"}, {"id": 240, "start": 559.32, "end": 561.829, "formattedStart": "09:19", "formattedEnd": "09:21", "text": "about what it is that I want and then"}, {"id": 241, "start": 561.*************, "end": 564.99, "formattedStart": "09:21", "formattedEnd": "09:24", "text": "just build that okay gotcha putting"}, {"id": 242, "start": 565, "end": 566.91, "formattedStart": "09:25", "formattedEnd": "09:26", "text": "yourself in your customer shoes is how"}, {"id": 243, "start": 566.92, "end": 568.71, "formattedStart": "09:26", "formattedEnd": "09:28", "text": "you know they'll buy now let's talk"}, {"id": 244, "start": 568.72, "end": 570.67, "formattedStart": "09:28", "formattedEnd": "09:30", "text": "about actually build the app what's"}, {"id": 245, "start": 570.68, "end": 572.11, "formattedStart": "09:30", "formattedEnd": "09:32", "text": "amazing to me is how you're able to"}, {"id": 246, "start": 572.12, "end": 574.67, "formattedStart": "09:32", "formattedEnd": "09:34", "text": "learn design coding and distribution in"}, {"id": 247, "start": 574.68, "end": 576.47, "formattedStart": "09:34", "formattedEnd": "09:36", "text": "such a short amount of time and make"}, {"id": 248, "start": 576.48, "end": 578.31, "formattedStart": "09:36", "formattedEnd": "09:38", "text": "millions with those skills if you were"}, {"id": 249, "start": 578.32, "end": 580.509, "formattedStart": "09:38", "formattedEnd": "09:40", "text": "to restart from scratch today having"}, {"id": 250, "start": 580.519, "end": 582.31, "formattedStart": "09:40", "formattedEnd": "09:42", "text": "nothing but the idea in mind how would"}, {"id": 251, "start": 582.32, "end": 585.43, "formattedStart": "09:42", "formattedEnd": "09:45", "text": "you build it if I were to restart and"}, {"id": 252, "start": 585.44, "end": 587.389, "formattedStart": "09:45", "formattedEnd": "09:47", "text": "build apps today here are the tools that"}, {"id": 253, "start": 587.399, "end": 590.99, "formattedStart": "09:47", "formattedEnd": "09:50", "text": "I would use number one for design figma"}, {"id": 254, "start": 591, "end": 593.47, "formattedStart": "09:51", "formattedEnd": "09:53", "text": "learning how to use figma will be one of"}, {"id": 255, "start": 593.48, "end": 595.829, "formattedStart": "09:53", "formattedEnd": "09:55", "text": "the greatest unlocks in ensuring that"}, {"id": 256, "start": 595.*************, "end": 597.67, "formattedStart": "09:55", "formattedEnd": "09:57", "text": "the application that you put into the"}, {"id": 257, "start": 597.68, "end": 600.19, "formattedStart": "09:57", "formattedEnd": "10:00", "text": "market is actually us"}, {"id": 258, "start": 600.2, "end": 602.069, "formattedStart": "10:00", "formattedEnd": "10:02", "text": "now what I would recommend for anybody"}, {"id": 259, "start": 602.079, "end": 604.67, "formattedStart": "10:02", "formattedEnd": "10:04", "text": "designing to do is to use references I'm"}, {"id": 260, "start": 604.68, "end": 606.55, "formattedStart": "10:04", "formattedEnd": "10:06", "text": "grabbing different apps that have"}, {"id": 261, "start": 606.56, "end": 608.31, "formattedStart": "10:06", "formattedEnd": "10:08", "text": "similar functionality and design to what"}, {"id": 262, "start": 608.32, "end": 610.829, "formattedStart": "10:08", "formattedEnd": "10:10", "text": "I'm looking to build and then using that"}, {"id": 263, "start": 610.839, "end": 613.35, "formattedStart": "10:10", "formattedEnd": "10:13", "text": "to inform kind of my design process"}, {"id": 264, "start": 613.36, "end": 615.35, "formattedStart": "10:13", "formattedEnd": "10:15", "text": "right even if you yourself don't intend"}, {"id": 265, "start": 615.36, "end": 617.31, "formattedStart": "10:15", "formattedEnd": "10:17", "text": "to be a designer you need to be able to"}, {"id": 266, "start": 617.32, "end": 619.509, "formattedStart": "10:17", "formattedEnd": "10:19", "text": "work in figma to work with designers"}, {"id": 267, "start": 619.519, "end": 621.67, "formattedStart": "10:19", "formattedEnd": "10:21", "text": "whether you hire them off upwork or work"}, {"id": 268, "start": 621.68, "end": 623.63, "formattedStart": "10:21", "formattedEnd": "10:23", "text": "with someone internally number two when"}, {"id": 269, "start": 623.64, "end": 625.35, "formattedStart": "10:23", "formattedEnd": "10:25", "text": "it comes to building what I would"}, {"id": 270, "start": 625.36, "end": 627.829, "formattedStart": "10:25", "formattedEnd": "10:27", "text": "recommend is that you build in react"}, {"id": 271, "start": 627.*************, "end": 630.87, "formattedStart": "10:27", "formattedEnd": "10:30", "text": "native with the EXP Expo framework using"}, {"id": 272, "start": 630.88, "end": 633.67, "formattedStart": "10:30", "formattedEnd": "10:33", "text": "cursor as your IDE then finally when it"}, {"id": 273, "start": 633.68, "end": 635.87, "formattedStart": "10:33", "formattedEnd": "10:35", "text": "comes to marketing the two primary"}, {"id": 274, "start": 635.88, "end": 637.75, "formattedStart": "10:35", "formattedEnd": "10:37", "text": "methods that I would recommend are"}, {"id": 275, "start": 637.76, "end": 640.03, "formattedStart": "10:37", "formattedEnd": "10:40", "text": "influencer marketing and doing internal"}, {"id": 276, "start": 640.04, "end": 642.55, "formattedStart": "10:40", "formattedEnd": "10:42", "text": "ugc so influencer marketing is when you"}, {"id": 277, "start": 642.56, "end": 644.47, "formattedStart": "10:42", "formattedEnd": "10:44", "text": "find somebody that already has a"}, {"id": 278, "start": 644.48, "end": 647.03, "formattedStart": "10:44", "formattedEnd": "10:47", "text": "platform or an audience and you pay them"}, {"id": 279, "start": 647.04, "end": 650.069, "formattedStart": "10:47", "formattedEnd": "10:50", "text": "to promote your product internal ugc is"}, {"id": 280, "start": 650.079, "end": 652.509, "formattedStart": "10:50", "formattedEnd": "10:52", "text": "where you or someone that you hire"}, {"id": 281, "start": 652.519, "end": 654.509, "formattedStart": "10:52", "formattedEnd": "10:54", "text": "creates content based around your"}, {"id": 282, "start": 654.519, "end": 657.269, "formattedStart": "10:54", "formattedEnd": "10:57", "text": "application and then post that to"}, {"id": 283, "start": 657.279, "end": 659.829, "formattedStart": "10:57", "formattedEnd": "10:59", "text": "accounts branded around your application"}, {"id": 284, "start": 659.*************, "end": 661.67, "formattedStart": "10:59", "formattedEnd": "11:01", "text": "and if you are creating content yourself"}, {"id": 285, "start": 661.68, "end": 662.829, "formattedStart": "11:01", "formattedEnd": "11:02", "text": "in the early days you're going to want"}, {"id": 286, "start": 662.839, "end": 665.23, "formattedStart": "11:02", "formattedEnd": "11:05", "text": "to use cap cut to do so and you can do a"}, {"id": 287, "start": 665.24, "end": 667.79, "formattedStart": "11:05", "formattedEnd": "11:07", "text": "combination of slideshows and faceless"}, {"id": 288, "start": 667.8, "end": 669.87, "formattedStart": "11:07", "formattedEnd": "11:09", "text": "video content or content where you're"}, {"id": 289, "start": 669.88, "end": 672.19, "formattedStart": "11:09", "formattedEnd": "11:12", "text": "speaking directly to the camera the most"}, {"id": 290, "start": 672.2, "end": 673.71, "formattedStart": "11:12", "formattedEnd": "11:13", "text": "important note here when it comes to"}, {"id": 291, "start": 673.72, "end": 676.87, "formattedStart": "11:13", "formattedEnd": "11:16", "text": "marketing is constant iteration be"}, {"id": 292, "start": 676.88, "end": 679.15, "formattedStart": "11:16", "formattedEnd": "11:19", "text": "ruthless about how you iterate try out"}, {"id": 293, "start": 679.16, "end": 681.15, "formattedStart": "11:19", "formattedEnd": "11:21", "text": "different strategies and as soon as you"}, {"id": 294, "start": 681.16, "end": 682.75, "formattedStart": "11:21", "formattedEnd": "11:22", "text": "find something that is able to produce"}, {"id": 295, "start": 682.76, "end": 685.03, "formattedStart": "11:22", "formattedEnd": "11:25", "text": "profitable returns for the company based"}, {"id": 296, "start": 685.04, "end": 687.79, "formattedStart": "11:25", "formattedEnd": "11:27", "text": "on the time and or money investment you"}, {"id": 297, "start": 687.8, "end": 689.829, "formattedStart": "11:27", "formattedEnd": "11:29", "text": "double down and double"}, {"id": 298, "start": 689.*************, "end": 691.91, "formattedStart": "11:29", "formattedEnd": "11:31", "text": "down and double down on that I want to double click into"}, {"id": 299, "start": 691.92, "end": 693.99, "formattedStart": "11:31", "formattedEnd": "11:33", "text": "down on that I want to double click into marketing because I see a lot of"}, {"id": 300, "start": 694, "end": 695.829, "formattedStart": "11:34", "formattedEnd": "11:35", "text": "marketing because I see a lot of Founders struggling to get eyeballs on"}, {"id": 301, "start": 695.*************, "end": 698.03, "formattedStart": "11:35", "formattedEnd": "11:38", "text": "Founders struggling to get eyeballs on their products what are the mistakes"}, {"id": 302, "start": 698.04, "end": 700.55, "formattedStart": "11:38", "formattedEnd": "11:40", "text": "their products what are the mistakes that you see people making most when it"}, {"id": 303, "start": 700.56, "end": 703.19, "formattedStart": "11:40", "formattedEnd": "11:43", "text": "that you see people making most when it comes to figuring out marketing yeah I"}, {"id": 304, "start": 703.2, "end": 704.59, "formattedStart": "11:43", "formattedEnd": "11:44", "text": "comes to figuring out marketing yeah I think that where a lot of people get"}, {"id": 305, "start": 704.6, "end": 707.23, "formattedStart": "11:44", "formattedEnd": "11:47", "text": "think that where a lot of people get lost in marketing is they think okay I"}, {"id": 306, "start": 707.24, "end": 709.87, "formattedStart": "11:47", "formattedEnd": "11:49", "text": "lost in marketing is they think okay I will just DM influencers and expect a"}, {"id": 307, "start": 709.88, "end": 712.59, "formattedStart": "11:49", "formattedEnd": "11:52", "text": "will just DM influencers and expect a response within 24 hours they'll DM 10"}, {"id": 308, "start": 712.6, "end": 714.75, "formattedStart": "11:52", "formattedEnd": "11:54", "text": "response within 24 hours they'll DM 10 influencers and they get no responses"}, {"id": 309, "start": 714.76, "end": 716.87, "formattedStart": "11:54", "formattedEnd": "11:56", "text": "influencers and they get no responses they go oh this is so hard and I'm like"}, {"id": 310, "start": 716.88, "end": 718.509, "formattedStart": "11:56", "formattedEnd": "11:58", "text": "they go oh this is so hard and I'm like no you're just you're not getting"}, {"id": 311, "start": 718.519, "end": 721.389, "formattedStart": "11:58", "formattedEnd": "12:01", "text": "no you're just you're not getting creative with with Riz GPT the Creator"}, {"id": 312, "start": 721.399, "end": 723.269, "formattedStart": "12:01", "formattedEnd": "12:03", "text": "creative with with Riz GPT the Creator is making the most profitable content"}, {"id": 313, "start": 723.279, "end": 725.35, "formattedStart": "12:03", "formattedEnd": "12:05", "text": "is making the most profitable content most of them were like 18 19 years old"}, {"id": 314, "start": 725.36, "end": 726.99, "formattedStart": "12:05", "formattedEnd": "12:06", "text": "most of them were like 18 19 years old they're just doing it for fun and so"}, {"id": 315, "start": 727, "end": 728.79, "formattedStart": "12:07", "formattedEnd": "12:08", "text": "they're just doing it for fun and so getting in contact with him was really"}, {"id": 316, "start": 728.8, "end": 730.389, "formattedStart": "12:08", "formattedEnd": "12:10", "text": "getting in contact with him was really difficult I would find that one of them"}, {"id": 317, "start": 730.399, "end": 732.35, "formattedStart": "12:10", "formattedEnd": "12:12", "text": "difficult I would find that one of them had a Discord link hidden in their"}, {"id": 318, "start": 732.36, "end": 734.509, "formattedStart": "12:12", "formattedEnd": "12:14", "text": "had a Discord link hidden in their Instagram bio or something and I would"}, {"id": 319, "start": 734.519, "end": 736.11, "formattedStart": "12:14", "formattedEnd": "12:16", "text": "Instagram bio or something and I would join that Discord and I would send a"}, {"id": 320, "start": 736.12, "end": 738.269, "formattedStart": "12:16", "formattedEnd": "12:18", "text": "join that Discord and I would send a message every 10 minutes until the"}, {"id": 321, "start": 738.279, "end": 740.189, "formattedStart": "12:18", "formattedEnd": "12:20", "text": "message every 10 minutes until the person responded there were guys that I"}, {"id": 322, "start": 740.199, "end": 743.269, "formattedStart": "12:20", "formattedEnd": "12:23", "text": "person responded there were guys that I was dming their mom saying hey get me a"}, {"id": 323, "start": 743.279, "end": 745.269, "formattedStart": "12:23", "formattedEnd": "12:25", "text": "was dming their mom saying hey get me a contact with your son I want to pay him"}, {"id": 324, "start": 745.279, "end": 747.509, "formattedStart": "12:25", "formattedEnd": "12:27", "text": "contact with your son I want to pay him money all right now let's talk about how"}, {"id": 325, "start": 747.519, "end": 749.269, "formattedStart": "12:27", "formattedEnd": "12:29", "text": "money all right now let's talk about how to make money once you have those it"}, {"id": 326, "start": 749.279, "end": 751.11, "formattedStart": "12:29", "formattedEnd": "12:31", "text": "to make money once you have those it balls how should people watching this"}, {"id": 327, "start": 751.12, "end": 754.03, "formattedStart": "12:31", "formattedEnd": "12:34", "text": "balls how should people watching this right now actually monetize their app"}, {"id": 328, "start": 754.04, "end": 756.59, "formattedStart": "12:34", "formattedEnd": "12:36", "text": "right now actually monetize their app the primary monetization model for most"}, {"id": 329, "start": 756.6, "end": 758.75, "formattedStart": "12:36", "formattedEnd": "12:38", "text": "the primary monetization model for most apps is subscription everybody that I"}, {"id": 330, "start": 758.76, "end": 760.55, "formattedStart": "12:38", "formattedEnd": "12:40", "text": "apps is subscription everybody that I know that makes a lot of money on apps"}, {"id": 331, "start": 760.56, "end": 763.67, "formattedStart": "12:40", "formattedEnd": "12:43", "text": "know that makes a lot of money on apps uses subscriptions generally I try to go"}, {"id": 332, "start": 763.68, "end": 765.99, "formattedStart": "12:43", "formattedEnd": "12:45", "text": "uses subscriptions generally I try to go with lower price points for a couple of"}, {"id": 333, "start": 766, "end": 768.189, "formattedStart": "12:46", "formattedEnd": "12:48", "text": "with lower price points for a couple of reasons one I want as many people as"}, {"id": 334, "start": 768.199, "end": 769.59, "formattedStart": "12:48", "formattedEnd": "12:49", "text": "reasons one I want as many people as possible to be able to use my"}, {"id": 335, "start": 769.6, "end": 771.47, "formattedStart": "12:49", "formattedEnd": "12:51", "text": "possible to be able to use my applications people are more likely to"}, {"id": 336, "start": 771.48, "end": 773.15, "formattedStart": "12:51", "formattedEnd": "12:53", "text": "applications people are more likely to tell their friends about it get"}, {"id": 337, "start": 773.16, "end": 775.59, "formattedStart": "12:53", "formattedEnd": "12:55", "text": "tell their friends about it get generally more positive user sentiment"}, {"id": 338, "start": 775.6, "end": 778.47, "formattedStart": "12:55", "formattedEnd": "12:58", "text": "generally more positive user sentiment on social media but then it also is more"}, {"id": 339, "start": 778.48, "end": 780.99, "formattedStart": "12:58", "formattedEnd": "13:00", "text": "on social media but then it also is more sustainable for long-term growth and you"}, {"id": 340, "start": 781, "end": 782.99, "formattedStart": "13:01", "formattedEnd": "13:02", "text": "sustainable for long-term growth and you know a lot of apps charge significantly"}, {"id": 341, "start": 783, "end": 784.949, "formattedStart": "13:03", "formattedEnd": "13:04", "text": "know a lot of apps charge significantly higher price points which leads me to"}, {"id": 342, "start": 784.959, "end": 787.35, "formattedStart": "13:04", "formattedEnd": "13:07", "text": "higher price points which leads me to the most important point which is you"}, {"id": 343, "start": 787.36, "end": 789.91, "formattedStart": "13:07", "formattedEnd": "13:09", "text": "the most important point which is you should use super wall super wall enables"}, {"id": 344, "start": 789.92, "end": 792.829, "formattedStart": "13:09", "formattedEnd": "13:12", "text": "should use super wall super wall enables you to split test different offerings so"}, {"id": 345, "start": 792.839, "end": 794.23, "formattedStart": "13:12", "formattedEnd": "13:14", "text": "you to split test different offerings so different price points whether it's"}, {"id": 346, "start": 794.24, "end": 797.11, "formattedStart": "13:14", "formattedEnd": "13:17", "text": "different price points whether it's weekly yearly monthly at different"}, {"id": 347, "start": 797.12, "end": 799.11, "formattedStart": "13:17", "formattedEnd": "13:19", "text": "weekly yearly monthly at different points in the app being able to test"}, {"id": 348, "start": 799.12, "end": 800.75, "formattedStart": "13:19", "formattedEnd": "13:20", "text": "points in the app being able to test these different offerings and different"}, {"id": 349, "start": 800.76, "end": 802.87, "formattedStart": "13:20", "formattedEnd": "13:22", "text": "these different offerings and different placements within the app can take you"}, {"id": 350, "start": 802.88, "end": 806.71, "formattedStart": "13:22", "formattedEnd": "13:26", "text": "placements within the app can take you from making x per user that downloads to"}, {"id": 351, "start": 806.72, "end": 809.509, "formattedStart": "13:26", "formattedEnd": "13:29", "text": "from making x per user that downloads to 1. 5 or 2X"}, {"id": 352, "start": 809.519, "end": 811.75, "formattedStart": "13:29", "formattedEnd": "13:31", "text": "1. 5 or 2X awesome okay so far we've only talked"}, {"id": 353, "start": 811.76, "end": 813.509, "formattedStart": "13:31", "formattedEnd": "13:33", "text": "awesome okay so far we've only talked about the positives of building apps but"}, {"id": 354, "start": 813.519, "end": 815.829, "formattedStart": "13:33", "formattedEnd": "13:35", "text": "about the positives of building apps but surely there are some pitfalls right"}, {"id": 355, "start": 815.*************, "end": 817.829, "formattedStart": "13:35", "formattedEnd": "13:37", "text": "surely there are some pitfalls right what are the main expenses you have of"}, {"id": 356, "start": 817.*************, "end": 820.03, "formattedStart": "13:37", "formattedEnd": "13:40", "text": "what are the main expenses you have of running Ai mobile apps and how can"}, {"id": 357, "start": 820.04, "end": 822.59, "formattedStart": "13:40", "formattedEnd": "13:42", "text": "running Ai mobile apps and how can people minimize them to stay profitable"}, {"id": 358, "start": 822.6, "end": 826.389, "formattedStart": "13:42", "formattedEnd": "13:46", "text": "people minimize them to stay profitable yeah so Apple charges 15% but for"}, {"id": 359, "start": 826.399, "end": 828.069, "formattedStart": "13:46", "formattedEnd": "13:48", "text": "yeah so Apple charges 15% but for whatever reason it's really closer to"}, {"id": 360, "start": 828.079, "end": 830.91, "formattedStart": "13:48", "formattedEnd": "13:50", "text": "whatever reason it's really closer to 20% of Revenue up to your first million"}, {"id": 361, "start": 830.92, "end": 833.31, "formattedStart": "13:50", "formattedEnd": "13:53", "text": "20% of Revenue up to your first million dollar in earnings beyond that Apple"}, {"id": 362, "start": 833.32, "end": 837.15, "formattedStart": "13:53", "formattedEnd": "13:57", "text": "dollar in earnings beyond that Apple charges 30 but really Clos to like 33%"}, {"id": 363, "start": 837.16, "end": 838.35, "formattedStart": "13:57", "formattedEnd": "13:58", "text": "charges 30 but really <PERSON><PERSON> to like 33% one thing that people significantly"}, {"id": 364, "start": 838.36, "end": 840.509, "formattedStart": "13:58", "formattedEnd": "14:00", "text": "one thing that people significantly overestimate is like the cost of AI in"}, {"id": 365, "start": 840.519, "end": 842.829, "formattedStart": "14:00", "formattedEnd": "14:02", "text": "overestimate is like the cost of AI in the back end unless you're doing image"}, {"id": 366, "start": 842.839, "end": 845.91, "formattedStart": "14:02", "formattedEnd": "14:05", "text": "the back end unless you're doing image generation or using advanced voice mode"}, {"id": 367, "start": 845.92, "end": 848.629, "formattedStart": "14:05", "formattedEnd": "14:08", "text": "generation or using advanced voice mode AI costs are sub 3% the bulk of the"}, {"id": 368, "start": 848.639, "end": 851.15, "formattedStart": "14:08", "formattedEnd": "14:11", "text": "AI costs are sub 3% the bulk of the expenses are usually attributable to"}, {"id": 369, "start": 851.16, "end": 854.15, "formattedStart": "14:11", "formattedEnd": "14:14", "text": "expenses are usually attributable to marketing and staff if you're doing that"}, {"id": 370, "start": 854.16, "end": 855.59, "formattedStart": "14:14", "formattedEnd": "14:15", "text": "marketing and staff if you're doing that content creation yourself you can do it"}, {"id": 371, "start": 855.6, "end": 857.35, "formattedStart": "14:15", "formattedEnd": "14:17", "text": "content creation yourself you can do it on your own but one of the reasons that"}, {"id": 372, "start": 857.36, "end": 859.55, "formattedStart": "14:17", "formattedEnd": "14:19", "text": "on your own but one of the reasons that internal ugc is great is that you"}, {"id": 373, "start": 859.56, "end": 861.269, "formattedStart": "14:19", "formattedEnd": "14:21", "text": "internal ugc is great is that you usually see somewhere around like 50 to"}, {"id": 374, "start": 861.279, "end": 863.71, "formattedStart": "14:21", "formattedEnd": "14:23", "text": "usually see somewhere around like 50 to 80% profit margins people doing"}, {"id": 375, "start": 863.72, "end": 865.629, "formattedStart": "14:23", "formattedEnd": "14:25", "text": "80% profit margins people doing influencer marketing you usually see"}, {"id": 376, "start": 865.639, "end": 868.79, "formattedStart": "14:25", "formattedEnd": "14:28", "text": "influencer marketing you usually see somewhere in the range of like 25 to 70%"}, {"id": 377, "start": 868.8, "end": 870.71, "formattedStart": "14:28", "formattedEnd": "14:30", "text": "somewhere in the range of like 25 to 70% and then people doing paid ads you"}, {"id": 378, "start": 870.72, "end": 874.55, "formattedStart": "14:30", "formattedEnd": "14:34", "text": "and then people doing paid ads you generally see like 0 to 30% so in-house"}, {"id": 379, "start": 874.56, "end": 876.91, "formattedStart": "14:34", "formattedEnd": "14:36", "text": "generally see like 0 to 30% so in-house organic content might be the way to go"}, {"id": 380, "start": 876.92, "end": 878.03, "formattedStart": "14:36", "formattedEnd": "14:38", "text": "organic content might be the way to go if you don't want to spend all your"}, {"id": 381, "start": 878.04, "end": 880.23, "formattedStart": "14:38", "formattedEnd": "14:40", "text": "if you don't want to spend all your profits on marketing and on that same"}, {"id": 382, "start": 880.24, "end": 881.59, "formattedStart": "14:40", "formattedEnd": "14:41", "text": "profits on marketing and on that same topic are you comfortable sharing your"}, {"id": 383, "start": 881.6, "end": 884.389, "formattedStart": "14:41", "formattedEnd": "14:44", "text": "topic are you comfortable sharing your profit margins so after influencer"}, {"id": 384, "start": 884.399, "end": 886.91, "formattedStart": "14:44", "formattedEnd": "14:46", "text": "profit margins so after influencer marketing after Apple's cut after server"}, {"id": 385, "start": 886.92, "end": 889.31, "formattedStart": "14:46", "formattedEnd": "14:49", "text": "marketing after Apple's cut after server processing that sort of thing Riz GPT I"}, {"id": 386, "start": 889.32, "end": 890.389, "formattedStart": "14:49", "formattedEnd": "14:50", "text": "processing that sort of thing R<PERSON> GPT I don't think I can talk about"}, {"id": 387, "start": 890.399, "end": 892.15, "formattedStart": "14:50", "formattedEnd": "14:52", "text": "don't think I can talk about unfortunately cuz it's run by my other"}, {"id": 388, "start": 892.16, "end": 894.629, "formattedStart": "14:52", "formattedEnd": "14:54", "text": "unfortunately cuz it's run by my other co-founders um<PERSON> does a little bit over"}, {"id": 389, "start": 894.639, "end": 897.31, "formattedStart": "14:54", "formattedEnd": "14:57", "text": "co-founders umax does a little bit over $100, 000 profit a month consistently"}, {"id": 390, "start": 897.32, "end": 900.67, "formattedStart": "14:57", "formattedEnd": "15:00", "text": "$100, 000 profit a month consistently used to do more k I uh does a few"}, {"id": 391, "start": 900.68, "end": 903.15, "formattedStart": "15:00", "formattedEnd": "15:03", "text": "used to do more k I uh does a few hundred th000 in profit a month okay"}, {"id": 392, "start": 903.16, "end": 905.269, "formattedStart": "15:03", "formattedEnd": "15:05", "text": "hundred th000 in profit a month okay another sensitive question sorry about"}, {"id": 393, "start": 905.279, "end": 907.389, "formattedStart": "15:05", "formattedEnd": "15:07", "text": "another sensitive question sorry about it but I'm just curious what made you"}, {"id": 394, "start": 907.399, "end": 910.79, "formattedStart": "15:07", "formattedEnd": "15:10", "text": "it but I'm just curious what made you leave the Riz GPT team left because one"}, {"id": 395, "start": 910.8, "end": 912.629, "formattedStart": "15:10", "formattedEnd": "15:12", "text": "leave the Riz GPT team left because one of my co-founders and I were constantly"}, {"id": 396, "start": 912.639, "end": 914.189, "formattedStart": "15:12", "formattedEnd": "15:14", "text": "of my co-founders and I were constantly going head-to-head about like who's at"}, {"id": 397, "start": 914.199, "end": 916.31, "formattedStart": "15:14", "formattedEnd": "15:16", "text": "going head-to-head about like who's at the Helm of the company essentially what"}, {"id": 398, "start": 916.32, "end": 917.91, "formattedStart": "15:16", "formattedEnd": "15:17", "text": "the Helm of the company essentially what I said to him was like okay we can't"}, {"id": 399, "start": 917.92, "end": 920.15, "formattedStart": "15:17", "formattedEnd": "15:20", "text": "I said to him was like okay we can't work together anymore either you run the"}, {"id": 400, "start": 920.16, "end": 922.389, "formattedStart": "15:20", "formattedEnd": "15:22", "text": "work together anymore either you run the company and I keep my Equity or I run"}, {"id": 401, "start": 922.399, "end": 924.509, "formattedStart": "15:22", "formattedEnd": "15:24", "text": "company and I keep my Equity or I run the equity and"}, {"id": 402, "start": 924.509, "end": 924.519, "formattedStart": "15:24", "formattedEnd": "15:24", "text": "the company and you keep your equity and"}, {"id": 403, "start": 924.519, "end": 926.269, "formattedStart": "15:24", "formattedEnd": "15:26", "text": "he wanted to continue running it which I"}, {"id": 404, "start": 926.279, "end": 927.91, "formattedStart": "15:26", "formattedEnd": "15:27", "text": "was like great I'm going to go work on"}, {"id": 405, "start": 927.92, "end": 929.59, "formattedStart": "15:27", "formattedEnd": "15:29", "text": "new projects"}, {"id": 406, "start": 929.6, "end": 931.03, "formattedStart": "15:29", "formattedEnd": "15:31", "text": "all right thanks for being transparent"}, {"id": 407, "start": 931.04, "end": 932.509, "formattedStart": "15:31", "formattedEnd": "15:32", "text": "about that now I'm going to go a bit"}, {"id": 408, "start": 932.519, "end": 934.189, "formattedStart": "15:32", "formattedEnd": "15:34", "text": "easier on you let's talk about"}, {"id": 409, "start": 934.199, "end": 936.629, "formattedStart": "15:34", "formattedEnd": "15:36", "text": "technology what's the tech stack"}, {"id": 410, "start": 936.639, "end": 940.309, "formattedStart": "15:36", "formattedEnd": "15:40", "text": "honestly my stack looks like figma"}, {"id": 411, "start": 940.319, "end": 943.749, "formattedStart": "15:40", "formattedEnd": "15:43", "text": "cursor cap cut Mercury for banking"}, {"id": 412, "start": 943.759, "end": 945.87, "formattedStart": "15:43", "formattedEnd": "15:45", "text": "that's actually pretty big Mercury is"}, {"id": 413, "start": 945.88, "end": 947.79, "formattedStart": "15:45", "formattedEnd": "15:47", "text": "the best platform for startup banking in"}, {"id": 414, "start": 947.8, "end": 951.269, "formattedStart": "15:47", "formattedEnd": "15:51", "text": "the US upwork for occasional contracted"}, {"id": 415, "start": 951.279, "end": 953.47, "formattedStart": "15:51", "formattedEnd": "15:53", "text": "work one upwork method I would recommend"}, {"id": 416, "start": 953.48, "end": 955.069, "formattedStart": "15:53", "formattedEnd": "15:55", "text": "for anyone considering hiring off of"}, {"id": 417, "start": 955.079, "end": 957.509, "formattedStart": "15:55", "formattedEnd": "15:57", "text": "there hire like five to 10 guys to do an"}, {"id": 418, "start": 957.519, "end": 959.67, "formattedStart": "15:57", "formattedEnd": "15:59", "text": "hour or two of work and then stick with"}, {"id": 419, "start": 959.68, "end": 961.71, "formattedStart": "15:59", "formattedEnd": "16:01", "text": "the best guy that tends to work very"}, {"id": 420, "start": 961.72, "end": 964.59, "formattedStart": "16:01", "formattedEnd": "16:04", "text": "well coding languages react native and"}, {"id": 421, "start": 964.6, "end": 967.629, "formattedStart": "16:04", "formattedEnd": "16:07", "text": "no JS yeah pretty pretty straightforward"}, {"id": 422, "start": 967.639, "end": 970.15, "formattedStart": "16:07", "formattedEnd": "16:10", "text": "nothing special all right man let's talk"}, {"id": 423, "start": 970.16, "end": 971.629, "formattedStart": "16:10", "formattedEnd": "16:11", "text": "about some personal stuff what are you"}, {"id": 424, "start": 971.639, "end": 973.99, "formattedStart": "16:11", "formattedEnd": "16:13", "text": "up to these days I've always been so"}, {"id": 425, "start": 974, "end": 976.03, "formattedStart": "16:14", "formattedEnd": "16:16", "text": "passionate about self-improvement about"}, {"id": 426, "start": 976.04, "end": 978.47, "formattedStart": "16:16", "formattedEnd": "16:18", "text": "doing everything one can in order to"}, {"id": 427, "start": 978.48, "end": 980.43, "formattedStart": "16:18", "formattedEnd": "16:20", "text": "improve their health to become more"}, {"id": 428, "start": 980.44, "end": 982.47, "formattedStart": "16:20", "formattedEnd": "16:22", "text": "successful and find more purpose in"}, {"id": 429, "start": 982.48, "end": 984.67, "formattedStart": "16:22", "formattedEnd": "16:24", "text": "their life and so that's why I set off"}, {"id": 430, "start": 984.68, "end": 986.79, "formattedStart": "16:24", "formattedEnd": "16:26", "text": "to build what I'm currently working on"}, {"id": 431, "start": 986.8, "end": 989.23, "formattedStart": "16:26", "formattedEnd": "16:29", "text": "which is Apex which I describe as is an"}, {"id": 432, "start": 989.24, "end": 991.749, "formattedStart": "16:29", "formattedEnd": "16:31", "text": "all-in-one self-improvement ecosystem"}, {"id": 433, "start": 991.759, "end": 993.87, "formattedStart": "16:31", "formattedEnd": "16:33", "text": "where we're starting to create content"}, {"id": 434, "start": 993.88, "end": 996.509, "formattedStart": "16:33", "formattedEnd": "16:36", "text": "build a free community build free mobile"}, {"id": 435, "start": 996.519, "end": 999.629, "formattedStart": "16:36", "formattedEnd": "16:39", "text": "apps to help people and sell low margin"}, {"id": 436, "start": 999.639, "end": 1001.59, "formattedStart": "16:39", "formattedEnd": "16:41", "text": "highquality physical products it's"}, {"id": 437, "start": 1001.6, "end": 1003.79, "formattedStart": "16:41", "formattedEnd": "16:43", "text": "something that I'm very excited for and"}, {"id": 438, "start": 1003.8, "end": 1005.749, "formattedStart": "16:43", "formattedEnd": "16:45", "text": "passionate about and I wake up every"}, {"id": 439, "start": 1005.759, "end": 1008.87, "formattedStart": "16:45", "formattedEnd": "16:48", "text": "morning just like so amped to work on it"}, {"id": 440, "start": 1008.88, "end": 1010.35, "formattedStart": "16:48", "formattedEnd": "16:50", "text": "it's going well we started this a little"}, {"id": 441, "start": 1010.36, "end": 1012.55, "formattedStart": "16:50", "formattedEnd": "16:52", "text": "bit over a month and a half ago hell"}, {"id": 442, "start": 1012.56, "end": 1014.269, "formattedStart": "16:52", "formattedEnd": "16:54", "text": "yeah man that's exciting I think it's"}, {"id": 443, "start": 1014.279, "end": 1015.749, "formattedStart": "16:54", "formattedEnd": "16:55", "text": "awesome to be able to wake up every day"}, {"id": 444, "start": 1015.759, "end": 1016.949, "formattedStart": "16:55", "formattedEnd": "16:56", "text": "and work on something that you're"}, {"id": 445, "start": 1016.9590000000001, "end": 1018.55, "formattedStart": "16:56", "formattedEnd": "16:58", "text": "actually passionate about another"}, {"id": 446, "start": 1018.56, "end": 1021.069, "formattedStart": "16:58", "formattedEnd": "17:01", "text": "question I want to ask you is what's the"}, {"id": 447, "start": 1021.079, "end": 1022.87, "formattedStart": "17:01", "formattedEnd": "17:02", "text": "biggest lesson that you've learned in"}, {"id": 448, "start": 1022.88, "end": 1025.51, "formattedStart": "17:02", "formattedEnd": "17:05", "text": "your entire Journey arguably the biggest"}, {"id": 449, "start": 1025.52, "end": 1029.35, "formattedStart": "17:05", "formattedEnd": "17:09", "text": "one is creating a sense of urgency I"}, {"id": 450, "start": 1029.36, "end": 1031.669, "formattedStart": "17:09", "formattedEnd": "17:11", "text": "found that my most productive and"}, {"id": 451, "start": 1031.679, "end": 1033.99, "formattedStart": "17:11", "formattedEnd": "17:13", "text": "successful periods have come when"}, {"id": 452, "start": 1034, "end": 1035.99, "formattedStart": "17:14", "formattedEnd": "17:15", "text": "there's a lot of risk and a lot of"}, {"id": 453, "start": 1036, "end": 1038.87, "formattedStart": "17:16", "formattedEnd": "17:18", "text": "urgency to get done quickly so when"}, {"id": 454, "start": 1038.88, "end": 1041.51, "formattedStart": "17:18", "formattedEnd": "17:21", "text": "I was building Riz GPT it was like I'm"}, {"id": 455, "start": 1041.52, "end": 1045.069, "formattedStart": "17:21", "formattedEnd": "17:25", "text": "at home making 0 while my parents house"}, {"id": 456, "start": 1045.079, "end": 1047.15, "formattedStart": "17:25", "formattedEnd": "17:27", "text": "is on the market meanwhile all of my"}, {"id": 457, "start": 1047.16, "end": 1049.549, "formattedStart": "17:27", "formattedEnd": "17:29", "text": "friends are making these great sixf"}, {"id": 458, "start": 1049.559, "end": 1052.23, "formattedStart": "17:29", "formattedEnd": "17:32", "text": "figure salaries living up Life After"}, {"id": 459, "start": 1052.24, "end": 1054.99, "formattedStart": "17:32", "formattedEnd": "17:34", "text": "College and I felt like I had a fire"}, {"id": 460, "start": 1055, "end": 1057.549, "formattedStart": "17:35", "formattedEnd": "17:37", "text": "under my ass to get something done and"}, {"id": 461, "start": 1057.559, "end": 1059.71, "formattedStart": "17:37", "formattedEnd": "17:39", "text": "to begin succeeding when I was building"}, {"id": 462, "start": 1059.72, "end": 1062.51, "formattedStart": "17:39", "formattedEnd": "17:42", "text": "UMX I had obviously just split from my"}, {"id": 463, "start": 1062.52, "end": 1065.11, "formattedStart": "17:42", "formattedEnd": "17:45", "text": "previous team and I felt like I had"}, {"id": 464, "start": 1065.12, "end": 1068.19, "formattedStart": "17:45", "formattedEnd": "17:48", "text": "something to prove I was on my brother's"}, {"id": 465, "start": 1068.2, "end": 1071.549, "formattedStart": "17:48", "formattedEnd": "17:51", "text": "couch I spent Thanksgiving alone I had"}, {"id": 466, "start": 1071.559, "end": 1075.11, "formattedStart": "17:51", "formattedEnd": "17:55", "text": "some sliced turkey while I was coding"}, {"id": 467, "start": 1075.12, "end": 1078.07, "formattedStart": "17:55", "formattedEnd": "17:58", "text": "and now with Apex everyone looks at me"}, {"id": 468, "start": 1078.08, "end": 1079.51, "formattedStart": "17:58", "formattedEnd": "17:59", "text": "and they're like dude you you did so"}, {"id": 469, "start": 1079.52, "end": 1081.07, "formattedStart": "17:59", "formattedEnd": "18:01", "text": "well in the app space and now you're"}, {"id": 470, "start": 1081.08, "end": 1083.39, "formattedStart": "18:01", "formattedEnd": "18:03", "text": "building what like you're making content"}, {"id": 471, "start": 1083.4, "end": 1085.47, "formattedStart": "18:03", "formattedEnd": "18:05", "text": "you're trying to be a influencer"}, {"id": 472, "start": 1085.48, "end": 1087.31, "formattedStart": "18:05", "formattedEnd": "18:07", "text": "I feel that like I feel the pressure and"}, {"id": 473, "start": 1087.32, "end": 1089.95, "formattedStart": "18:07", "formattedEnd": "18:09", "text": "it fires me up and if you feel like you"}, {"id": 474, "start": 1089.96, "end": 1091.95, "formattedStart": "18:09", "formattedEnd": "18:11", "text": "have that for lack of a better word like"}, {"id": 475, "start": 1091.96, "end": 1094.11, "formattedStart": "18:11", "formattedEnd": "18:14", "text": "you have that dog in you create a sense"}, {"id": 476, "start": 1094.12, "end": 1097.669, "formattedStart": "18:14", "formattedEnd": "18:17", "text": "of urgency 100% agreed now the last"}, {"id": 477, "start": 1097.679, "end": 1098.95, "formattedStart": "18:17", "formattedEnd": "18:18", "text": "question that I want to ask you we ask"}, {"id": 478, "start": 1098.96, "end": 1100.27, "formattedStart": "18:18", "formattedEnd": "18:20", "text": "all founders that we interview on"}, {"id": 479, "start": 1100.28, "end": 1102.59, "formattedStart": "18:20", "formattedEnd": "18:22", "text": "starter story what would you say to"}, {"id": 480, "start": 1102.6, "end": 1104.909, "formattedStart": "18:22", "formattedEnd": "18:24", "text": "entrepreneurs we're just getting started"}, {"id": 481, "start": 1104.919, "end": 1106.95, "formattedStart": "18:24", "formattedEnd": "18:26", "text": "yeah I think one of the things that has"}, {"id": 482, "start": 1106.96, "end": 1109.789, "formattedStart": "18:26", "formattedEnd": "18:29", "text": "helped me most succeed is trying to"}, {"id": 483, "start": 1109.799, "end": 1112.59, "formattedStart": "18:29", "formattedEnd": "18:32", "text": "abstract myself away from what you hear"}, {"id": 484, "start": 1112.6, "end": 1114.59, "formattedStart": "18:32", "formattedEnd": "18:34", "text": "on social media and what others are"}, {"id": 485, "start": 1114.6, "end": 1116.549, "formattedStart": "18:34", "formattedEnd": "18:36", "text": "telling you to do you have to be able to"}, {"id": 486, "start": 1116.559, "end": 1118.39, "formattedStart": "18:36", "formattedEnd": "18:38", "text": "think on your own and think from first"}, {"id": 487, "start": 1118.4, "end": 1121.549, "formattedStart": "18:38", "formattedEnd": "18:41", "text": "principles as opposed to constantly just"}, {"id": 488, "start": 1121.559, "end": 1124.59, "formattedStart": "18:41", "formattedEnd": "18:44", "text": "doing what others say there's a naval"}, {"id": 489, "start": 1124.6, "end": 1126.35, "formattedStart": "18:44", "formattedEnd": "18:46", "text": "quote that a friend recently reminded me"}, {"id": 490, "start": 1126.36, "end": 1128.909, "formattedStart": "18:46", "formattedEnd": "18:48", "text": "of if you want to make the wrong"}, {"id": 491, "start": 1128.919, "end": 1131.99, "formattedStart": "18:48", "formattedEnd": "18:51", "text": "decision ask everybody love it man well"}, {"id": 492, "start": 1132, "end": 1133.669, "formattedStart": "18:52", "formattedEnd": "18:53", "text": "<PERSON> I appreciate you coming on starter"}, {"id": 493, "start": 1133.679, "end": 1136.27, "formattedStart": "18:53", "formattedEnd": "18:56", "text": "story and sharing all of this value I"}, {"id": 494, "start": 1136.28, "end": 1137.63, "formattedStart": "18:56", "formattedEnd": "18:57", "text": "can't wait to see what you build next"}, {"id": 495, "start": 1137.64, "end": 1139.75, "formattedStart": "18:57", "formattedEnd": "18:59", "text": "I'll be watching take care man cool"}, {"id": 496, "start": 1139.76, "end": 1142.75, "formattedStart": "18:59", "formattedEnd": "19:02", "text": "appreciate you brother okay recapping"}, {"id": 497, "start": 1142.76, "end": 1145.63, "formattedStart": "19:02", "formattedEnd": "19:05", "text": "this episode what I think made <PERSON> so"}, {"id": 498, "start": 1145.64, "end": 1148.11, "formattedStart": "19:05", "formattedEnd": "19:08", "text": "successful is how hard he went on"}, {"id": 499, "start": 1148.12, "end": 1149.789, "formattedStart": "19:08", "formattedEnd": "19:09", "text": "content marketing I've done a lot of"}, {"id": 500, "start": 1149.799, "end": 1152.23, "formattedStart": "19:09", "formattedEnd": "19:12", "text": "research on businesses and nearly every"}, {"id": 501, "start": 1152.24, "end": 1154.95, "formattedStart": "19:12", "formattedEnd": "19:14", "text": "really really successful bootstrapped BC"}, {"id": 502, "start": 1154.96, "end": 1158.07, "formattedStart": "19:14", "formattedEnd": "19:18", "text": "app like this purely got traction from"}, {"id": 503, "start": 1158.08, "end": 1160.59, "formattedStart": "19:18", "formattedEnd": "19:20", "text": "content I think the most successful BC"}, {"id": 504, "start": 1160.6, "end": 1162.75, "formattedStart": "19:20", "formattedEnd": "19:22", "text": "Founders or people building iPhone apps"}, {"id": 505, "start": 1162.76, "end": 1164.63, "formattedStart": "19:22", "formattedEnd": "19:24", "text": "they truly understand that product is"}, {"id": 506, "start": 1164.64, "end": 1166.95, "formattedStart": "19:24", "formattedEnd": "19:26", "text": "just not as important as distribution as"}, {"id": 507, "start": 1166.96, "end": 1169.11, "formattedStart": "19:26", "formattedEnd": "19:29", "text": "distribution and attention wins the game"}, {"id": 508, "start": 1169.12, "end": 1171.07, "formattedStart": "19:29", "formattedEnd": "19:31", "text": "if you want to win in BC especially if"}, {"id": 509, "start": 1171.08, "end": 1172.63, "formattedStart": "19:31", "formattedEnd": "19:32", "text": "your app is not Niche it's like a"}, {"id": 510, "start": 1172.64, "end": 1174.549, "formattedStart": "19:32", "formattedEnd": "19:34", "text": "calorie tracking app like this then you"}, {"id": 511, "start": 1174.559, "end": 1176.47, "formattedStart": "19:34", "formattedEnd": "19:36", "text": "must be ready and excited for the"}, {"id": 512, "start": 1176.48, "end": 1178.149, "formattedStart": "19:36", "formattedEnd": "19:38", "text": "content game you must already be"}, {"id": 513, "start": 1178.159, "end": 1179.51, "formattedStart": "19:38", "formattedEnd": "19:39", "text": "thinking about what T<PERSON> toks you're"}, {"id": 514, "start": 1179.52, "end": 1181.19, "formattedStart": "19:39", "formattedEnd": "19:41", "text": "going to try all the channels you can"}, {"id": 515, "start": 1181.2, "end": 1183.35, "formattedStart": "19:41", "formattedEnd": "19:43", "text": "try and even validating your idea in the"}, {"id": 516, "start": 1183.36, "end": 1185.31, "formattedStart": "19:43", "formattedEnd": "19:45", "text": "first place through content online"}, {"id": 517, "start": 1185.32, "end": 1187.549, "formattedStart": "19:45", "formattedEnd": "19:47", "text": "before you even build something this is"}, {"id": 518, "start": 1187.559, "end": 1189.75, "formattedStart": "19:47", "formattedEnd": "19:49", "text": "why I created the starter story Academy"}, {"id": 519, "start": 1189.76, "end": 1191.75, "formattedStart": "19:49", "formattedEnd": "19:51", "text": "to not only have you focus on building"}, {"id": 520, "start": 1191.76, "end": 1194.19, "formattedStart": "19:51", "formattedEnd": "19:54", "text": "an idea but most importantly figure out"}, {"id": 521, "start": 1194.2, "end": 1196.39, "formattedStart": "19:54", "formattedEnd": "19:56", "text": "how to get attention on that idea how to"}, {"id": 522, "start": 1196.4, "end": 1197.99, "formattedStart": "19:56", "formattedEnd": "19:57", "text": "get your first customers how to get"}, {"id": 523, "start": 1198, "end": 1199.789, "formattedStart": "19:58", "formattedEnd": "19:59", "text": "feedback from the customers so you can"}, {"id": 524, "start": 1199.799, "end": 1201.19, "formattedStart": "19:59", "formattedEnd": "20:01", "text": "build a better product that will"}, {"id": 525, "start": 1201.2, "end": 1203.19, "formattedStart": "20:01", "formattedEnd": "20:03", "text": "resonate with thousands if not millions"}, {"id": 526, "start": 1203.2, "end": 1204.99, "formattedStart": "20:03", "formattedEnd": "20:04", "text": "of users if you're serious about taking"}, {"id": 527, "start": 1205, "end": 1206.789, "formattedStart": "20:05", "formattedEnd": "20:06", "text": "the first step then definitely check out"}, {"id": 528, "start": 1206.799, "end": 1208.71, "formattedStart": "20:06", "formattedEnd": "20:08", "text": "the starter story Academy just head to"}, {"id": 529, "start": 1208.72, "end": 1210.47, "formattedStart": "20:08", "formattedEnd": "20:10", "text": "the first link in the description if you"}, {"id": 530, "start": 1210.48, "end": 1212.51, "formattedStart": "20:10", "formattedEnd": "20:12", "text": "want to learn more much love and I'll"}, {"id": 531, "start": 1212.52, "end": 1216.32, "formattedStart": "20:12", "formattedEnd": "20:16", "text": "see you guys in the next one peace"}, {"id": 532, "start": 1216.33, "end": 1224.75, "formattedStart": "20:16", "formattedEnd": "20:24", "text": "[Music]"}]}