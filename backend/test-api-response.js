/**
 * Test script to check the raw API response for transcript data
 */

const axios = require('axios');

// The video ID to test
const videoId = 'Gv2fzC96Z40';

async function testApiResponse() {
  try {
    console.log(`Testing API response for video ID: ${videoId}`);
    
    // Make a direct request to the API
    const response = await axios.get(`http://localhost:3000/api/transcript/${videoId}`);
    
    console.log('API Response Status:', response.status);
    console.log('Transcript Length:', response.data.transcript.length);
    
    // Log the first 20 items
    console.log('\nFirst 20 transcript items:');
    for (let i = 0; i < 20 && i < response.data.transcript.length; i++) {
      const item = response.data.transcript[i];
      console.log(`[${i+1}] [${item.formattedStart} - ${item.formattedEnd}] ${item.text}`);
    }
    
    // Check for any missing segments
    console.log('\nChecking for gaps in timestamps...');
    let gapsFound = 0;
    
    for (let i = 0; i < response.data.transcript.length - 1; i++) {
      const currentItem = response.data.transcript[i];
      const nextItem = response.data.transcript[i + 1];
      
      // Check if there's a gap between the end of the current item and the start of the next item
      if (Math.abs(currentItem.end - nextItem.start) > 0.1) {
        console.log(`Gap found between items ${i+1} and ${i+2}:`);
        console.log(`  Item ${i+1}: [${currentItem.formattedStart} - ${currentItem.formattedEnd}] ${currentItem.text}`);
        console.log(`  Item ${i+2}: [${nextItem.formattedStart} - ${nextItem.formattedEnd}] ${nextItem.text}`);
        console.log(`  Gap size: ${(nextItem.start - currentItem.end).toFixed(2)} seconds`);
        gapsFound++;
        
        if (gapsFound >= 5) {
          console.log('Too many gaps found, stopping check...');
          break;
        }
      }
    }
    
    if (gapsFound === 0) {
      console.log('No gaps found in timestamps.');
    }
    
    // Check for any overlapping segments
    console.log('\nChecking for overlapping timestamps...');
    let overlapsFound = 0;
    
    for (let i = 0; i < response.data.transcript.length - 1; i++) {
      const currentItem = response.data.transcript[i];
      const nextItem = response.data.transcript[i + 1];
      
      // Check if there's an overlap between the end of the current item and the start of the next item
      if (currentItem.end > nextItem.start) {
        console.log(`Overlap found between items ${i+1} and ${i+2}:`);
        console.log(`  Item ${i+1}: [${currentItem.formattedStart} - ${currentItem.formattedEnd}] ${currentItem.text}`);
        console.log(`  Item ${i+2}: [${nextItem.formattedStart} - ${nextItem.formattedEnd}] ${nextItem.text}`);
        console.log(`  Overlap size: ${(currentItem.end - nextItem.start).toFixed(2)} seconds`);
        overlapsFound++;
        
        if (overlapsFound >= 5) {
          console.log('Too many overlaps found, stopping check...');
          break;
        }
      }
    }
    
    if (overlapsFound === 0) {
      console.log('No overlaps found in timestamps.');
    }
    
  } catch (error) {
    console.error('Error testing API response:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

// Run the test
testApiResponse();
