/**
 * Test script for comparing raw and processed transcripts in different languages
 */

const { YoutubeTranscript } = require('youtube-transcript');
const transcriptService = require('./services/transcriptService');

// The video ID to test (from the user's request)
const videoId = 'Gv2fzC96Z40';

// Languages to test
const languages = ['en', 'de'];

// Function to get raw transcript data directly from the youtube-transcript package
async function getRawTranscript(videoId, lang) {
  try {
    console.log(`Fetching raw transcript for ${videoId} in ${lang} using youtube-transcript package...`);
    const rawTranscript = await YoutubeTranscript.fetchTranscript(videoId, { lang });
    return rawTranscript;
  } catch (error) {
    console.error(`Error fetching raw transcript in ${lang}: ${error.message}`);
    return null;
  }
}

async function testTranscriptProcessing() {
  console.log(`Testing transcript processing for video ID: ${videoId}\n`);
  
  for (const lang of languages) {
    console.log(`\n========== TESTING LANGUAGE: ${lang} ==========\n`);
    
    try {
      // Step 1: Get the raw transcript data directly from the package
      const rawTranscript = await getRawTranscript(videoId, lang);
      
      if (!rawTranscript) {
        console.log(`Could not fetch raw transcript in ${lang}. Trying with the service...`);
        continue;
      }
      
      console.log(`\nRAW TRANSCRIPT DATA in ${lang} (first 10 items):`);
      console.log(JSON.stringify(rawTranscript.slice(0, 10), null, 2));
      
      // Log the first 10 items in a more readable format
      console.log(`\nRAW TRANSCRIPT in ${lang} (first 10 items):`);
      rawTranscript.slice(0, 10).forEach((item, index) => {
        console.log(`[${item.offset}s] ${item.text}`);
      });
      
      // Step 2: Get the processed transcript using the service
      console.log(`\nFetching processed transcript in ${lang} using the transcript service...`);
      console.time('Fetch time');
      const processedTranscript = await transcriptService.getTranscript(videoId, lang, true);
      console.timeEnd('Fetch time');
      
      console.log(`\nPROCESSED TRANSCRIPT DATA in ${lang} (first 10 items):`);
      console.log(JSON.stringify(processedTranscript.transcript.slice(0, 10), null, 2));
      
      // Log the first 10 items in a more readable format
      console.log(`\nPROCESSED TRANSCRIPT in ${lang} (first 10 items):`);
      processedTranscript.transcript.slice(0, 10).forEach((item) => {
        console.log(`[${item.formattedStart}] ${item.text}`);
      });
      
      // Compare the raw and processed data
      if (rawTranscript) {
        console.log(`\nCOMPARISON OF RAW VS PROCESSED in ${lang}:`);
        for (let i = 0; i < 10 && i < rawTranscript.length; i++) {
          console.log(`\nItem ${i+1}:`);
          console.log(`Raw: [${rawTranscript[i].offset}s] "${rawTranscript[i].text}"`);
          
          // Find the corresponding processed item (might not be at the same index)
          const processedItem = processedTranscript.transcript.find(item => 
            Math.abs(item.start - rawTranscript[i].offset) < 0.5);
          
          if (processedItem) {
            console.log(`Processed: [${processedItem.formattedStart}] "${processedItem.text}"`);
            console.log(`Changes: ${processedItem.text !== rawTranscript[i].text ? 'YES' : 'NO'}`);
          } else {
            console.log('No matching processed item found');
          }
        }
      }
      
    } catch (error) {
      console.error(`Error in transcript processing test for ${lang}: ${error.message}`);
      console.error(error.stack);
    }
  }
}

// Run the test
testTranscriptProcessing().catch(error => {
  console.error('Unhandled error:', error);
});
