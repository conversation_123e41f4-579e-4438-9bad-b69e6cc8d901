const axios = require('axios');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

const BASE_URL = 'http://localhost:3000';

async function testFinalVerification() {
  console.log('🧪 Final Verification Test...\n');

  try {
    // Test the specific requirement: 10-minute video should have at least 5 cards
    console.log('📹 Testing 10-minute video requirement...');
    
    const response = await axios.post(`${BASE_URL}/api/anki-cards`, {
      videoId: 'TT81fe2IobI', // Dunning-Kruger Effect (10:32)
      lang: 'en'
    });

    if (response.data.success) {
      const cards = response.data.cards;
      console.log(`✅ Generated ${cards.length} cards for 10:32 video`);
      
      // Check minimum requirement
      if (cards.length >= 5) {
        console.log(`✅ REQUIREMENT MET: ${cards.length} >= 5 cards for 10+ minute video`);
      } else {
        console.log(`❌ REQUIREMENT FAILED: ${cards.length} < 5 cards for 10+ minute video`);
        return;
      }

      // Check distribution
      const qa = cards.filter(c => c.type === 'qa').length;
      const process = cards.filter(c => c.type === 'process').length;
      const truefalse = cards.filter(c => c.type === 'truefalse').length;
      
      const qaPercent = Math.round((qa / cards.length) * 100);
      const processPercent = Math.round((process / cards.length) * 100);
      const tfPercent = Math.round((truefalse / cards.length) * 100);

      console.log(`\n📊 Distribution Analysis:`);
      console.log(`   Q&A: ${qa} cards (${qaPercent}%) - Target: 70%`);
      console.log(`   Process: ${process} cards (${processPercent}%) - Target: 20%`);
      console.log(`   True/False: ${truefalse} cards (${tfPercent}%) - Target: 10%`);

      // Check if distribution is reasonable
      const distributionOk = qaPercent >= 60 && qaPercent <= 80; // Allow some flexibility
      if (distributionOk) {
        console.log(`✅ Distribution is reasonable`);
      } else {
        console.log(`⚠️ Distribution could be improved`);
      }

      // Check for cloze cards (should be 0)
      const clozeCards = cards.filter(c => c.type === 'cloze');
      if (clozeCards.length === 0) {
        console.log(`✅ No cloze cards found (requirement met)`);
      } else {
        console.log(`❌ Found ${clozeCards.length} cloze cards (should be 0)`);
      }

      // Check transcript verification
      const cardsWithQuotes = cards.filter(c => c.transcriptQuote && c.transcriptQuote.length > 0);
      const verificationRate = Math.round((cardsWithQuotes.length / cards.length) * 100);
      
      if (verificationRate >= 80) {
        console.log(`✅ Excellent transcript verification: ${verificationRate}%`);
      } else {
        console.log(`⚠️ Transcript verification needs improvement: ${verificationRate}%`);
      }

      // Sample card quality
      console.log(`\n🔍 Sample Card Quality:`);
      
      const sampleCard = cards[0];
      console.log(`Question: ${sampleCard.question}`);
      console.log(`Answer: ${sampleCard.answer.substring(0, 100)}...`);
      console.log(`Type: ${sampleCard.type}`);
      console.log(`Timestamp: ${Math.floor(sampleCard.timestamp / 60)}:${(sampleCard.timestamp % 60).toString().padStart(2, '0')}`);
      console.log(`Has transcript quote: ${sampleCard.transcriptQuote ? 'Yes' : 'No'}`);

      // Test question variety
      const questionTypes = {
        what: cards.filter(c => c.question.toLowerCase().startsWith('what')).length,
        who: cards.filter(c => c.question.toLowerCase().startsWith('who')).length,
        when: cards.filter(c => c.question.toLowerCase().startsWith('when')).length,
        where: cards.filter(c => c.question.toLowerCase().startsWith('where')).length,
        why: cards.filter(c => c.question.toLowerCase().startsWith('why')).length,
        how: cards.filter(c => c.question.toLowerCase().startsWith('how')).length,
        truefalse: cards.filter(c => c.question.toLowerCase().includes('true or false')).length
      };

      console.log(`\n📝 Question Variety:`);
      Object.entries(questionTypes).forEach(([type, count]) => {
        if (count > 0) {
          console.log(`   ${type.toUpperCase()}: ${count} questions`);
        }
      });

      const varietyScore = Object.values(questionTypes).filter(count => count > 0).length;
      if (varietyScore >= 3) {
        console.log(`✅ Good question variety (${varietyScore} different types)`);
      } else {
        console.log(`⚠️ Limited question variety (${varietyScore} different types)`);
      }

      // Final summary
      console.log(`\n🎯 FINAL VERIFICATION RESULTS:`);
      console.log(`✅ Card quantity: ${cards.length >= 5 ? 'PASS' : 'FAIL'} (${cards.length}/5 minimum)`);
      console.log(`✅ No cloze cards: ${clozeCards.length === 0 ? 'PASS' : 'FAIL'}`);
      console.log(`✅ Transcript accuracy: ${verificationRate >= 80 ? 'PASS' : 'PARTIAL'} (${verificationRate}%)`);
      console.log(`✅ Distribution: ${distributionOk ? 'PASS' : 'PARTIAL'} (${qaPercent}% Q&A)`);
      console.log(`✅ Question variety: ${varietyScore >= 3 ? 'PASS' : 'PARTIAL'} (${varietyScore} types)`);

      const allTestsPassed = cards.length >= 5 && clozeCards.length === 0 && verificationRate >= 80;
      
      if (allTestsPassed) {
        console.log(`\n🎉 ALL REQUIREMENTS MET! System is ready for production.`);
      } else {
        console.log(`\n⚠️ Some requirements need attention before production.`);
      }

    } else {
      console.log('❌ Failed to generate cards');
      console.log('Response:', response.data);
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    
    if (error.response) {
      console.log('   Status:', error.response.status);
      console.log('   Data:', error.response.data);
    }
  }
}

// Run the test
testFinalVerification().then(() => {
  console.log('\n🏁 Final verification test completed');
});
