const axios = require('axios');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

const BASE_URL = 'http://localhost:3000';

async function testGeminiAPI() {
  console.log('🧪 Testing Gemini API Integration...\n');

  try {
    // Test 1: Check if server is running
    console.log('1. Testing server health...');
    const healthResponse = await axios.get(`${BASE_URL}/api/health`);
    console.log('✅ Server is running:', healthResponse.data.message);
    console.log();

    // Test 2: Get Gemini model info
    console.log('2. Getting Gemini model info...');
    const infoResponse = await axios.get(`${BASE_URL}/api/gemini/info`);
    console.log('✅ Model info retrieved:');
    console.log('   Model:', infoResponse.data.model);
    console.log('   Provider:', infoResponse.data.provider);
    console.log('   API Key Configured:', infoResponse.data.apiKeyConfigured);
    console.log('   API Key Prefix:', infoResponse.data.apiKeyPrefix);
    console.log();

    // Test 3: Test Gemini connection with "tell me a joke"
    console.log('3. Testing Gemini connection with joke request...');
    const testResponse = await axios.get(`${BASE_URL}/api/gemini/test`);
    
    if (testResponse.data.success) {
      console.log('✅ Gemini API connection successful!');
      console.log('   Test Prompt:', testResponse.data.testPrompt);
      console.log('   Model:', testResponse.data.model);
      console.log('   Response:', testResponse.data.response);
    } else {
      console.log('❌ Gemini API connection failed:');
      console.log('   Error:', testResponse.data.error);
    }
    console.log();

    // Test 4: Test custom prompt generation
    console.log('4. Testing custom prompt generation...');
    const customPrompt = {
      prompt: "Explain what a YouTube transcript is in one sentence.",
      options: {
        temperature: 0.7,
        maxOutputTokens: 100
      }
    };

    const generateResponse = await axios.post(`${BASE_URL}/api/gemini/generate`, customPrompt);
    
    if (generateResponse.data.success) {
      console.log('✅ Custom prompt generation successful!');
      console.log('   Prompt:', generateResponse.data.prompt);
      console.log('   Model:', generateResponse.data.model);
      console.log('   Response:', generateResponse.data.response);
    } else {
      console.log('❌ Custom prompt generation failed');
    }
    console.log();

    console.log('🎉 All tests completed successfully!');
    console.log('\nGemini 2.5 Flash Lite is now integrated and working with your YouTube Transcript Generator.');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    
    if (error.response) {
      console.error('   Status:', error.response.status);
      console.error('   Error Details:', error.response.data);
    } else if (error.code === 'ECONNREFUSED') {
      console.error('   Make sure the server is running on port 3000');
      console.error('   Run: cd backend && npm start');
    }
    
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  testGeminiAPI();
}

module.exports = testGeminiAPI;
