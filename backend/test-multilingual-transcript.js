/**
 * Comprehensive test for multilingual transcript processing
 * 
 * This script tests transcript processing for a specific video in multiple languages
 * and provides detailed analysis of the issues.
 */

const fs = require('fs').promises;
const path = require('path');
const transcriptService = require('./services/transcriptService');
const { exec } = require('child_process');
const util = require('util');

// Convert exec to Promise-based
const execPromise = util.promisify(exec);

// Test video ID
const VIDEO_ID = 'KpVPST_P4W8'; // New test video
const LANGUAGES = ['en', 'de', 'fr']; // English, German, French

// Function to format time in MM:SS format
function formatTime(seconds) {
  if (typeof seconds !== 'number' || isNaN(seconds)) {
    seconds = 0;
  }
  
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  
  if (hours > 0) {
    return [
      hours.toString().padStart(2, '0'),
      minutes.toString().padStart(2, '0'),
      secs.toString().padStart(2, '0')
    ].join(':');
  } else {
    return [
      minutes.toString().padStart(2, '0'),
      secs.toString().padStart(2, '0')
    ].join(':');
  }
}

// Function to fetch raw transcript using yt-dlp
async function fetchRawTranscript(videoId, lang) {
  const tempDir = path.join(__dirname, 'temp');
  
  try {
    // Create temp directory if it doesn't exist
    await fs.mkdir(tempDir, { recursive: true });
    
    console.log(`Fetching raw ${lang} transcript for ${videoId} using yt-dlp...`);
    const command = `yt-dlp --write-auto-sub --sub-lang ${lang} --skip-download https://www.youtube.com/watch?v=${videoId} -o "${tempDir}/%(id)s.%(ext)s"`;
    
    const { stdout } = await execPromise(command);
    console.log(stdout);
    
    // Find the subtitle file
    const files = await fs.readdir(tempDir);
    const subtitleFile = files.find(file => 
      file.startsWith(videoId) && 
      file.includes(`.${lang}.`) && 
      file.endsWith('.vtt')
    );
    
    if (!subtitleFile) {
      console.error(`No ${lang} subtitle file found`);
      return null;
    }
    
    console.log(`Found subtitle file: ${subtitleFile}`);
    
    // Read the subtitle file
    const subtitlePath = path.join(tempDir, subtitleFile);
    const subtitleContent = await fs.readFile(subtitlePath, 'utf8');
    
    // Save the raw subtitle content for analysis
    const rawOutputPath = path.join(__dirname, `${videoId}-${lang}-raw.vtt`);
    await fs.writeFile(rawOutputPath, subtitleContent);
    console.log(`Raw ${lang} transcript saved to ${rawOutputPath}`);
    
    return subtitleContent;
  } catch (error) {
    console.error('Error fetching raw transcript:', error);
    return null;
  }
}

// Function to analyze transcript for issues
function analyzeTranscript(transcriptItems) {
  console.log('\nANALYZING TRANSCRIPT FOR ISSUES:');
  
  // Check for timestamp tags in text
  const timestampTagsCount = transcriptItems.filter(item => 
    item.text.includes('<') && item.text.includes('>') && 
    /\d+:\d+/.test(item.text)
  ).length;
  
  console.log(`\n1. Timestamp tags in text: ${timestampTagsCount > 0 ? 'YES' : 'NO'}`);
  if (timestampTagsCount > 0) {
    console.log('   Examples:');
    transcriptItems.filter(item => 
      item.text.includes('<') && item.text.includes('>') && 
      /\d+:\d+/.test(item.text)
    ).slice(0, 3).forEach(item => {
      console.log(`   - "${item.text}"`);
    });
  }
  
  // Check for duplicate text segments
  const textMap = new Map();
  const duplicateTexts = [];
  
  transcriptItems.forEach(item => {
    if (textMap.has(item.text)) {
      duplicateTexts.push({
        text: item.text,
        occurrences: textMap.get(item.text) + 1
      });
      textMap.set(item.text, textMap.get(item.text) + 1);
    } else {
      textMap.set(item.text, 1);
    }
  });
  
  const duplicateCount = duplicateTexts.length;
  console.log(`\n2. Duplicate text segments: ${duplicateCount > 0 ? 'YES' : 'NO'}`);
  if (duplicateCount > 0) {
    console.log('   Examples:');
    duplicateTexts.slice(0, 3).forEach(dup => {
      console.log(`   - "${dup.text}" (${dup.occurrences} occurrences)`);
    });
  }
  
  // Check for punctuation issues
  const punctuationIssuesCount = transcriptItems.filter(item => 
    item.text.match(/([.!?,;:])([^\s])/) || // No space after punctuation
    item.text.match(/^\s*[.!?,;:]/) // Starts with punctuation
  ).length;
  
  console.log(`\n3. Punctuation issues: ${punctuationIssuesCount > 0 ? 'YES' : 'NO'}`);
  if (punctuationIssuesCount > 0) {
    console.log('   Examples:');
    transcriptItems.filter(item => 
      item.text.match(/([.!?,;:])([^\s])/) || 
      item.text.match(/^\s*[.!?,;:]/)
    ).slice(0, 3).forEach(item => {
      console.log(`   - "${item.text}"`);
    });
  }
  
  // Check for very short segments (less than 0.5 seconds)
  const shortSegments = transcriptItems.filter(item => 
    (item.end - item.start) < 0.5
  ).length;
  
  console.log(`\n4. Very short segments (<0.5s): ${shortSegments > 0 ? 'YES' : 'NO'}`);
  if (shortSegments > 0) {
    console.log('   Examples:');
    transcriptItems.filter(item => 
      (item.end - item.start) < 0.5
    ).slice(0, 3).forEach(item => {
      console.log(`   - "${item.text}" (${(item.end - item.start).toFixed(2)}s)`);
    });
  }
  
  // Check for overlapping segments
  let overlappingSegments = 0;
  for (let i = 0; i < transcriptItems.length - 1; i++) {
    if (transcriptItems[i].end > transcriptItems[i + 1].start) {
      overlappingSegments++;
    }
  }
  
  console.log(`\n5. Overlapping segments: ${overlappingSegments > 0 ? 'YES' : 'NO'}`);
  if (overlappingSegments > 0) {
    console.log('   Examples:');
    for (let i = 0, count = 0; i < transcriptItems.length - 1 && count < 3; i++) {
      if (transcriptItems[i].end > transcriptItems[i + 1].start) {
        console.log(`   - "${transcriptItems[i].text}" (${formatTime(transcriptItems[i].start)}-${formatTime(transcriptItems[i].end)}) overlaps with`);
        console.log(`     "${transcriptItems[i+1].text}" (${formatTime(transcriptItems[i+1].start)}-${formatTime(transcriptItems[i+1].end)})`);
        count++;
      }
    }
  }
  
  // Check for very long segments (more than 30 seconds)
  const longSegments = transcriptItems.filter(item => 
    (item.end - item.start) > 30
  ).length;
  
  console.log(`\n6. Very long segments (>30s): ${longSegments > 0 ? 'YES' : 'NO'}`);
  if (longSegments > 0) {
    console.log('   Examples:');
    transcriptItems.filter(item => 
      (item.end - item.start) > 30
    ).slice(0, 3).forEach(item => {
      console.log(`   - "${item.text.substring(0, 100)}..." (${(item.end - item.start).toFixed(2)}s)`);
    });
  }
  
  // Check for repeated phrases within segments
  const repeatedPhrases = transcriptItems.filter(item => {
    const words = item.text.split(' ');
    for (let i = 0; i < words.length - 6; i++) {
      const phrase = words.slice(i, i + 3).join(' ');
      for (let j = i + 3; j < words.length - 3; j++) {
        const comparePhrase = words.slice(j, j + 3).join(' ');
        if (phrase === comparePhrase && phrase.length > 10) {
          return true;
        }
      }
    }
    return false;
  }).length;
  
  console.log(`\n7. Repeated phrases within segments: ${repeatedPhrases > 0 ? 'YES' : 'NO'}`);
  if (repeatedPhrases > 0) {
    console.log('   Examples:');
    transcriptItems.filter(item => {
      const words = item.text.split(' ');
      for (let i = 0; i < words.length - 6; i++) {
        const phrase = words.slice(i, i + 3).join(' ');
        for (let j = i + 3; j < words.length - 3; j++) {
          const comparePhrase = words.slice(j, j + 3).join(' ');
          if (phrase === comparePhrase && phrase.length > 10) {
            return true;
          }
        }
      }
      return false;
    }).slice(0, 3).forEach(item => {
      console.log(`   - "${item.text.substring(0, 100)}..."`);
    });
  }
}

// Main test function
async function testMultilingualTranscript() {
  try {
    console.log(`Testing multilingual transcript processing for video ID: ${VIDEO_ID}\n`);
    
    // Test each language
    for (const lang of LANGUAGES) {
      console.log(`\n=== TESTING ${lang.toUpperCase()} TRANSCRIPT ===\n`);
      
      // Step 1: Fetch the raw transcript
      await fetchRawTranscript(VIDEO_ID, lang);
      
      // Step 2: Process the transcript using our service
      console.log(`\nProcessing ${lang} transcript with transcriptService...`);
      const processedTranscript = await transcriptService.getTranscript(VIDEO_ID, lang, true);
      
      // Save the processed transcript for analysis
      await fs.writeFile(
        path.join(__dirname, `${VIDEO_ID}-${lang}-processed.json`),
        JSON.stringify(processedTranscript, null, 2)
      );
      
      console.log(`Retrieved ${processedTranscript.transcript.length} ${lang} transcript segments`);
      
      // Step 3: Analyze the processed transcript
      analyzeTranscript(processedTranscript.transcript);
      
      // Step 4: Display sample segments
      console.log(`\nSAMPLE ${lang.toUpperCase()} TRANSCRIPT SEGMENTS:`);
      for (let i = 0; i < Math.min(10, processedTranscript.transcript.length); i++) {
        console.log(`\n[${i+1}] [${processedTranscript.transcript[i].formattedStart}] "${processedTranscript.transcript[i].text}"`);
      }
    }
    
    console.log('\nTest completed successfully!');
    
  } catch (error) {
    console.error('Error in test:', error);
  }
}

// Run the test
testMultilingualTranscript().catch(console.error);
