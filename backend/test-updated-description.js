const axios = require('axios');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

const BASE_URL = 'http://localhost:3000';

async function testUpdatedDescription() {
  console.log('🧪 Testing Updated YouTube Description Generation...\n');

  try {
    // Test with an example video (Dunning-Kruger Effect)
    const testVideoId = 'TT81fe2IobI';
    const testLanguage = 'en';

    console.log(`Testing video: ${testVideoId} (Dunning-Kruger Effect)`);
    console.log(`Language: ${testLanguage}\n`);

    console.log('Testing UPDATED Description Generation...');
    console.log('   New focus: Content-based storytelling, not meta-analysis');
    console.log('   Emphasis on actual video stories and lessons');
    console.log('   This may take a moment...\n');

    const requestBody = {
      videoId: testVideoId,
      videoTitle: '', // Let AI determine from content
      keywords: '', // Let AI determine from content
      ctaGoal: 'Subscribe for more content', // Default CTA
      lang: testLanguage
    };

    const startTime = Date.now();
    const response = await axios.post(`${BASE_URL}/api/description`, requestBody);
    const endTime = Date.now();

    if (response.data.success) {
      console.log('✅ Updated Description generated successfully!');
      console.log(`   Generation time: ${(endTime - startTime) / 1000}s`);
      console.log(`   Description length: ${response.data.description.length} characters`);
      console.log('\n📝 Generated YouTube Description (Updated Prompt):');
      console.log('─'.repeat(80));
      console.log(response.data.description);
      console.log('─'.repeat(80));

      // Analyze the output for content focus
      const descriptionText = response.data.description;
      const hasTimestamps = descriptionText.includes('⏰ **TIMESTAMPS**');
      const timestampMatches = descriptionText.match(/\d{2}:\d{2}/g);
      const timestampCount = timestampMatches ? timestampMatches.length : 0;
      const hasHashtags = descriptionText.includes('#');
      
      // Check for content-focused language vs meta-analysis
      const hasMetaLanguage = descriptionText.toLowerCase().includes('analysis') || 
                             descriptionText.toLowerCase().includes('metrics') || 
                             descriptionText.toLowerCase().includes('strategy') ||
                             descriptionText.toLowerCase().includes('content analysis');
      
      const hasStoryLanguage = descriptionText.toLowerCase().includes('story') || 
                              descriptionText.toLowerCase().includes('lesson') || 
                              descriptionText.toLowerCase().includes('experience') ||
                              descriptionText.toLowerCase().includes('journey');
      
      console.log(`\n📊 Analysis:`);
      console.log(`   Contains timestamps section: ${hasTimestamps ? '✅' : '❌'}`);
      console.log(`   Number of timestamps: ${timestampCount}`);
      console.log(`   Contains hashtags: ${hasHashtags ? '✅' : '❌'}`);
      console.log(`   Total character count: ${descriptionText.length}`);
      console.log(`   Avoids meta-analysis language: ${!hasMetaLanguage ? '✅' : '❌'}`);
      console.log(`   Focuses on content/stories: ${hasStoryLanguage ? '✅' : '❌'}`);

      // Extract hook (first few lines)
      const lines = descriptionText.split('\n').filter(line => line.trim());
      const hook = lines.slice(1, 3).join(' '); // Skip first line (---), take next 2-3 lines
      console.log(`   Hook length: ${hook.length} characters (should be <300)`);
      console.log(`   Hook preview: "${hook.substring(0, 100)}..."`);

    } else {
      console.log('❌ Updated description generation failed');
      console.log('   Response:', response.data);
    }

    console.log('\n🎉 Updated description feature tested successfully!');
    console.log('\n📋 Summary of Updates:');
    console.log('   ✅ Content-focused storytelling approach');
    console.log('   ✅ Avoids meta-analysis language');
    console.log('   ✅ Emphasizes actual video stories and lessons');
    console.log('   ✅ Compelling storyteller role');
    console.log('   ✅ Direct engagement with video content');
    console.log('\n🚀 Your updated YouTube Description generator is ready!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    
    if (error.response) {
      console.error('   Status:', error.response.status);
      console.error('   Error Details:', error.response.data);
    } else if (error.code === 'ECONNREFUSED') {
      console.error('   Make sure the backend server is running:');
      console.error('   Backend: cd backend && npm start');
    }
    
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  testUpdatedDescription();
}

module.exports = testUpdatedDescription;
