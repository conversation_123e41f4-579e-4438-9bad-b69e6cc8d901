/**
 * Test script for analyzing German transcript processing
 */

const { YoutubeTranscript } = require('youtube-transcript');
const transcriptService = require('./services/transcriptService');

// The video ID to test
const videoId = 'Gv2fzC96Z40';
const language = 'de'; // German

// Function to get raw transcript data directly from the youtube-transcript package
async function getRawTranscript(videoId, lang) {
  try {
    console.log(`Fetching raw transcript for ${videoId} in ${lang} using youtube-transcript package...`);
    const rawTranscript = await YoutubeTranscript.fetchTranscript(videoId, { lang });
    return rawTranscript;
  } catch (error) {
    console.error(`Error fetching raw transcript in ${lang}: ${error.message}`);
    return null;
  }
}

async function testGermanTranscript() {
  console.log(`Testing German transcript processing for video ID: ${videoId}\n`);
  
  try {
    // Step 1: Get the raw German transcript data directly from the package
    const rawTranscript = await getRawTranscript(videoId, language);
    
    if (!rawTranscript) {
      console.log(`Could not fetch raw transcript in ${language}. Trying with the service...`);
    } else {
      console.log(`\nRAW GERMAN TRANSCRIPT DATA (first 20 items):`);
      console.log(JSON.stringify(rawTranscript.slice(0, 20), null, 2));
      
      // Log the first 20 items in a more readable format
      console.log(`\nRAW GERMAN TRANSCRIPT (first 20 items):`);
      rawTranscript.slice(0, 20).forEach((item, index) => {
        console.log(`[${index+1}] [${item.offset}s] ${item.text}`);
      });
    }
    
    // Step 2: Get the processed transcript using the service
    console.log(`\nFetching processed German transcript using the transcript service...`);
    console.time('Fetch time');
    const processedTranscript = await transcriptService.getTranscript(videoId, language, true);
    console.timeEnd('Fetch time');
    
    console.log(`\nPROCESSED GERMAN TRANSCRIPT DATA (first 20 items):`);
    console.log(JSON.stringify(processedTranscript.transcript.slice(0, 20), null, 2));
    
    // Log the first 20 items in a more readable format
    console.log(`\nPROCESSED GERMAN TRANSCRIPT (first 20 items):`);
    processedTranscript.transcript.slice(0, 20).forEach((item, index) => {
      console.log(`[${index+1}] [${item.formattedStart}] ${item.text}`);
    });
    
    // Compare the raw and processed data
    if (rawTranscript) {
      console.log('\nANALYSIS OF GERMAN TRANSCRIPT PROCESSING:');
      
      // Check for timestamp tags in text
      const timestampTagsCount = processedTranscript.transcript.filter(item => 
        item.text.includes('<') && item.text.includes('>') && 
        /\d+:\d+/.test(item.text)
      ).length;
      
      console.log(`\n1. Timestamp tags in text: ${timestampTagsCount > 0 ? 'YES' : 'NO'}`);
      if (timestampTagsCount > 0) {
        console.log('   Examples:');
        processedTranscript.transcript.filter(item => 
          item.text.includes('<') && item.text.includes('>') && 
          /\d+:\d+/.test(item.text)
        ).slice(0, 3).forEach(item => {
          console.log(`   - "${item.text}"`);
        });
      }
      
      // Check for duplicate text segments
      const textMap = new Map();
      const duplicateTexts = [];
      
      processedTranscript.transcript.forEach(item => {
        if (textMap.has(item.text)) {
          duplicateTexts.push({
            text: item.text,
            occurrences: textMap.get(item.text) + 1
          });
          textMap.set(item.text, textMap.get(item.text) + 1);
        } else {
          textMap.set(item.text, 1);
        }
      });
      
      const duplicateCount = duplicateTexts.length;
      console.log(`\n2. Duplicate text segments: ${duplicateCount > 0 ? 'YES' : 'NO'}`);
      if (duplicateCount > 0) {
        console.log('   Examples:');
        duplicateTexts.slice(0, 3).forEach(dup => {
          console.log(`   - "${dup.text}" (${dup.occurrences} occurrences)`);
        });
      }
      
      // Check for HTML entities
      const htmlEntitiesCount = processedTranscript.transcript.filter(item => 
        item.text.includes('&amp;') || 
        item.text.includes('&lt;') || 
        item.text.includes('&gt;') ||
        item.text.includes('&#39;')
      ).length;
      
      console.log(`\n3. Unprocessed HTML entities: ${htmlEntitiesCount > 0 ? 'YES' : 'NO'}`);
      if (htmlEntitiesCount > 0) {
        console.log('   Examples:');
        processedTranscript.transcript.filter(item => 
          item.text.includes('&amp;') || 
          item.text.includes('&lt;') || 
          item.text.includes('&gt;') ||
          item.text.includes('&#39;')
        ).slice(0, 3).forEach(item => {
          console.log(`   - "${item.text}"`);
        });
      }
      
      // Check for punctuation issues
      const punctuationIssuesCount = processedTranscript.transcript.filter(item => 
        item.text.match(/([.!?,;:])([^\s])/) || // No space after punctuation
        item.text.match(/^\s*[.!?,;:]/) // Starts with punctuation
      ).length;
      
      console.log(`\n4. Punctuation issues: ${punctuationIssuesCount > 0 ? 'YES' : 'NO'}`);
      if (punctuationIssuesCount > 0) {
        console.log('   Examples:');
        processedTranscript.transcript.filter(item => 
          item.text.match(/([.!?,;:])([^\s])/) || 
          item.text.match(/^\s*[.!?,;:]/)
        ).slice(0, 3).forEach(item => {
          console.log(`   - "${item.text}"`);
        });
      }
    }
    
  } catch (error) {
    console.error(`Error in German transcript processing test: ${error.message}`);
    console.error(error.stack);
  }
}

// Run the test
testGermanTranscript().catch(error => {
  console.error('Unhandled error:', error);
});
