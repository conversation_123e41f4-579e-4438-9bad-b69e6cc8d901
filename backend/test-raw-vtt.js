/**
 * Test script to download and analyze raw VTT files for a YouTube video
 * in different languages
 */

const { exec } = require('child_process');
const fs = require('fs').promises;
const path = require('path');
const util = require('util');

// Convert exec to Promise-based
const execPromise = util.promisify(exec);

// Temp directory for downloads
const TEMP_DIR = path.join(__dirname, 'temp');

/**
 * Download VTT files for a video in different languages
 * @param {string} videoId - YouTube video ID
 * @param {Array} languages - Array of language codes
 * @returns {Promise<object>} Object with paths to downloaded files
 */
async function downloadVttFiles(videoId, languages) {
  try {
    // Create temp directory if it doesn't exist
    await fs.mkdir(TEMP_DIR, { recursive: true });
    
    const results = {};
    
    for (const lang of languages) {
      console.log(`Downloading ${lang} subtitles for video ${videoId}...`);
      
      // Use yt-dlp to download subtitles
      const command = `yt-dlp --write-auto-sub --sub-lang ${lang} --skip-download https://www.youtube.com/watch?v=${videoId} -o "${TEMP_DIR}/%(id)s.%(ext)s"`;
      
      try {
        const { stdout, stderr } = await execPromise(command);
        console.log(`yt-dlp stdout for ${lang}:`, stdout);
        
        if (stderr) {
          console.error(`yt-dlp stderr for ${lang}:`, stderr);
        }
        
        // Find the subtitle file
        const files = await fs.readdir(TEMP_DIR);
        const subtitleFile = files.find(file => 
          file.startsWith(videoId) && 
          file.includes(`.${lang}.`) && 
          file.endsWith('.vtt')
        );
        
        if (subtitleFile) {
          const filePath = path.join(TEMP_DIR, subtitleFile);
          results[lang] = filePath;
          
          // Save a copy with a more descriptive name
          const rawFilePath = path.join(__dirname, `${videoId}-${lang}-raw.vtt`);
          await fs.copyFile(filePath, rawFilePath);
          console.log(`Saved raw VTT file to ${rawFilePath}`);
        } else {
          console.log(`No ${lang} subtitle file found for ${videoId}`);
        }
      } catch (error) {
        console.error(`Error downloading ${lang} subtitles:`, error.message);
      }
    }
    
    return results;
  } catch (error) {
    console.error('Error downloading VTT files:', error.message);
    return {};
  }
}

/**
 * Analyze VTT files for common issues
 * @param {object} vttFiles - Object with paths to VTT files
 * @returns {Promise<object>} Analysis results
 */
async function analyzeVttFiles(vttFiles) {
  const results = {};
  
  for (const [lang, filePath] of Object.entries(vttFiles)) {
    try {
      console.log(`\nAnalyzing ${lang} VTT file: ${filePath}`);
      
      // Read the file
      const content = await fs.readFile(filePath, 'utf8');
      
      // Split into lines
      const lines = content.split('\n');
      
      // Basic stats
      const stats = {
        totalLines: lines.length,
        timestampLines: 0,
        textLines: 0,
        emptyLines: 0,
        linesWithTimestampTags: 0,
        linesWithHtmlTags: 0,
        linesWithPunctuationIssues: 0
      };
      
      // Sample lines for different categories
      const samples = {
        timestampLines: [],
        textLines: [],
        linesWithTimestampTags: [],
        linesWithHtmlTags: [],
        linesWithPunctuationIssues: []
      };
      
      // Analyze each line
      for (const line of lines) {
        // Skip empty lines
        if (!line.trim()) {
          stats.emptyLines++;
          continue;
        }
        
        // Check if this is a timestamp line
        if (line.includes('-->')) {
          stats.timestampLines++;
          if (samples.timestampLines.length < 3) {
            samples.timestampLines.push(line);
          }
          continue;
        }
        
        // This is a text line
        stats.textLines++;
        if (samples.textLines.length < 5) {
          samples.textLines.push(line);
        }
        
        // Check for timestamp tags
        if (line.includes('<') && line.includes('>') && /\d+:\d+/.test(line)) {
          stats.linesWithTimestampTags++;
          if (samples.linesWithTimestampTags.length < 3) {
            samples.linesWithTimestampTags.push(line);
          }
        }
        
        // Check for HTML tags
        if (/<[a-z][^>]*>/i.test(line)) {
          stats.linesWithHtmlTags++;
          if (samples.linesWithHtmlTags.length < 3) {
            samples.linesWithHtmlTags.push(line);
          }
        }
        
        // Check for punctuation issues
        if (/[.!?,;:](?=[a-zA-Z])/.test(line)) {
          stats.linesWithPunctuationIssues++;
          if (samples.linesWithPunctuationIssues.length < 3) {
            samples.linesWithPunctuationIssues.push(line);
          }
        }
      }
      
      results[lang] = { stats, samples };
      
      // Print the results
      console.log(`\nAnalysis results for ${lang}:`);
      console.log('Stats:', stats);
      console.log('Sample timestamp lines:', samples.timestampLines);
      console.log('Sample text lines:', samples.textLines);
      console.log('Sample lines with timestamp tags:', samples.linesWithTimestampTags);
      console.log('Sample lines with HTML tags:', samples.linesWithHtmlTags);
      console.log('Sample lines with punctuation issues:', samples.linesWithPunctuationIssues);
    } catch (error) {
      console.error(`Error analyzing ${lang} VTT file:`, error.message);
    }
  }
  
  return results;
}

/**
 * Main function
 */
async function main() {
  try {
    // Get video ID from command line arguments
    const args = process.argv.slice(2);
    const videoId = args[0] || 'KpVPST_P4W8';
    
    // Languages to test
    const languages = ['en', 'fr', 'de'];
    
    console.log(`Testing raw VTT files for video ${videoId} in languages: ${languages.join(', ')}`);
    
    // Download VTT files
    const vttFiles = await downloadVttFiles(videoId, languages);
    
    // Analyze VTT files
    await analyzeVttFiles(vttFiles);
    
    console.log('\nTest completed.');
  } catch (error) {
    console.error('Error in main function:', error.message);
  }
}

// Run the main function
main();
