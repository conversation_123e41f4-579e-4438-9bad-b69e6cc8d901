/**
 * Simple script to parse a VTT file
 */

const fs = require('fs');
const path = require('path');

// Path to the VTT file
const vttFilePath = path.join(__dirname, 'german-transcript-raw.vtt');

// Function to parse VTT timestamp to seconds
function parseVttTimestamp(timestamp) {
  // Handle both HH:MM:SS.mmm and MM:SS.mmm formats
  const parts = timestamp.split(':');
  let hours = 0, minutes = 0, seconds = 0;

  if (parts.length === 3) {
    hours = parseInt(parts[0]);
    minutes = parseInt(parts[1]);
    seconds = parseFloat(parts[2]);
  } else if (parts.length === 2) {
    minutes = parseInt(parts[0]);
    seconds = parseFloat(parts[1]);
  }

  return hours * 3600 + minutes * 60 + seconds;
}

// Function to parse VTT file
function parseVttFile(filePath) {
  try {
    // Read the file
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Split into lines
    const lines = content.split('\n');
    
    // Initialize variables
    const transcriptItems = [];
    let currentItem = null;
    let inCue = false;
    let textLines = [];
    
    // Process each line
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      // Skip empty lines and WEBVTT header
      if (!line || line === 'WEBVTT' || line.startsWith('Kind:') || line.startsWith('Language:')) {
        continue;
      }
      
      // Check if this is a timestamp line
      if (line.includes('-->')) {
        // If we were already in a cue, save the previous one
        if (inCue && currentItem) {
          currentItem.text = textLines.join(' ').trim();
          if (currentItem.text) {
            transcriptItems.push(currentItem);
          }
        }
        
        // Start a new cue
        inCue = true;
        textLines = [];
        
        // Parse the timestamp
        const times = line.split('-->').map(t => t.trim());
        const startTime = parseVttTimestamp(times[0]);
        const endTime = parseVttTimestamp(times[1].split(' ')[0]); // Remove alignment info
        
        currentItem = {
          start: startTime,
          end: endTime,
          text: ''
        };
      } 
      // If we're in a cue, collect text lines
      else if (inCue) {
        textLines.push(line);
      }
    }
    
    // Don't forget the last item
    if (inCue && currentItem) {
      currentItem.text = textLines.join(' ').trim();
      if (currentItem.text) {
        transcriptItems.push(currentItem);
      }
    }
    
    // Clean up the transcript items
    const cleanedItems = cleanTranscriptItems(transcriptItems);
    
    return {
      raw: transcriptItems,
      cleaned: cleanedItems
    };
  } catch (error) {
    console.error('Error parsing VTT file:', error);
    return { raw: [], cleaned: [] };
  }
}

// Function to clean transcript items
function cleanTranscriptItems(items) {
  if (!items || items.length === 0) return [];
  
  const cleanedItems = [];
  let previousText = '';
  
  for (let i = 0; i < items.length; i++) {
    const item = {...items[i]};
    
    // 1. Remove timestamp tags and <c> tags from text
    item.text = item.text
      .replace(/<\d+:\d+:\d+\.\d+>|<\d+:\d+\.\d+>/g, '') // Remove timestamp tags
      .replace(/<\/?c>/g, '') // Remove <c> and </c> tags
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim();
    
    // 2. Skip empty items
    if (!item.text) continue;
    
    // 3. Skip if this is a duplicate of the previous text
    if (item.text === previousText) continue;
    
    // 4. Add to cleaned items
    cleanedItems.push(item);
    previousText = item.text;
  }
  
  // 5. Fix overlapping segments
  for (let i = 0; i < cleanedItems.length - 1; i++) {
    // If the current segment's end time is after the next segment's start time
    if (cleanedItems[i].end > cleanedItems[i + 1].start) {
      // Adjust the current segment's end time to match the next segment's start time
      cleanedItems[i].end = cleanedItems[i + 1].start;
    }
  }
  
  // 6. Renumber the IDs
  cleanedItems.forEach((item, index) => {
    item.id = index + 1;
    // Add formatted timestamps
    item.formattedStart = formatTime(item.start);
    item.formattedEnd = formatTime(item.end);
  });
  
  return cleanedItems;
}

// Function to format time in MM:SS format
function formatTime(seconds) {
  if (typeof seconds !== 'number' || isNaN(seconds)) {
    seconds = 0;
  }
  
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  
  if (hours > 0) {
    return [
      hours.toString().padStart(2, '0'),
      minutes.toString().padStart(2, '0'),
      secs.toString().padStart(2, '0')
    ].join(':');
  } else {
    return [
      minutes.toString().padStart(2, '0'),
      secs.toString().padStart(2, '0')
    ].join(':');
  }
}

// Parse the VTT file
const result = parseVttFile(vttFilePath);

// Save the results
fs.writeFileSync(
  path.join(__dirname, 'german-transcript-parsed-new.json'),
  JSON.stringify(result.raw, null, 2)
);

fs.writeFileSync(
  path.join(__dirname, 'german-transcript-cleaned-new.json'),
  JSON.stringify(result.cleaned, null, 2)
);

console.log(`Parsed ${result.raw.length} raw transcript items`);
console.log(`Cleaned to ${result.cleaned.length} items`);

// Print a sample of the cleaned transcript
console.log('\nSample of cleaned transcript:');
result.cleaned.slice(0, 10).forEach(item => {
  console.log(`[${item.id}] [${item.formattedStart}] ${item.text}`);
});
