<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Transcript Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    h1 {
      color: #333;
    }
    button {
      background-color: #4CAF50;
      color: white;
      padding: 10px 15px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      margin-bottom: 20px;
    }
    button:hover {
      background-color: #45a049;
    }
    .transcript-container {
      height: 500px;
      overflow-y: auto;
      border: 1px solid #ddd;
      padding: 15px;
      border-radius: 4px;
    }
    .transcript-item {
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 1px solid #eee;
    }
    .timestamp {
      color: #4CAF50;
      font-weight: bold;
      margin-bottom: 5px;
    }
    .loading, .error, .success {
      padding: 10px;
      margin-bottom: 15px;
      border-radius: 4px;
    }
    .loading {
      background-color: #e7f3fe;
      border-left: 6px solid #2196F3;
    }
    .error {
      background-color: #ffdddd;
      border-left: 6px solid #f44336;
    }
    .success {
      background-color: #ddffdd;
      border-left: 6px solid #4CAF50;
    }
  </style>
</head>
<body>
  <h1>Transcript Test</h1>
  
  <button id="fetchBtn">Fetch Transcript</button>
  
  <div id="status"></div>
  
  <div id="transcriptContainer" class="transcript-container" style="display: none;"></div>
  
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const fetchBtn = document.getElementById('fetchBtn');
      const statusDiv = document.getElementById('status');
      const transcriptContainer = document.getElementById('transcriptContainer');
      
      // Default video ID (one of the example videos)
      const videoId = 'TT81fe2IobI';
      
      fetchBtn.addEventListener('click', async function() {
        // Show loading message
        statusDiv.innerHTML = '<div class="loading">Loading transcript...</div>';
        transcriptContainer.style.display = 'none';
        
        try {
          console.log('Fetching transcript for video ID:', videoId);
          
          // Make a direct fetch request to the backend API
          const response = await fetch(`/api/transcript/${videoId}`);
          
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          
          const data = await response.json();
          console.log('Raw API response:', data);
          
          if (data && data.transcript && Array.isArray(data.transcript)) {
            console.log('Transcript found, length:', data.transcript.length);
            
            // Show success message
            statusDiv.innerHTML = `<div class="success">Transcript loaded successfully! ${data.transcript.length} items found.</div>`;
            
            // Display the transcript
            transcriptContainer.innerHTML = '';
            data.transcript.forEach(item => {
              const transcriptItem = document.createElement('div');
              transcriptItem.className = 'transcript-item';
              
              const timestamp = document.createElement('div');
              timestamp.className = 'timestamp';
              timestamp.textContent = formatTime(item.start);
              
              const text = document.createElement('div');
              text.textContent = item.text;
              
              transcriptItem.appendChild(timestamp);
              transcriptItem.appendChild(text);
              
              transcriptContainer.appendChild(transcriptItem);
            });
            
            transcriptContainer.style.display = 'block';
          } else {
            throw new Error('Invalid transcript data format');
          }
        } catch (err) {
          console.error('Error fetching transcript:', err);
          statusDiv.innerHTML = `<div class="error">Error: ${err.message}</div>`;
        }
      });
      
      // Format time in HH:MM:SS format
      function formatTime(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);
        
        return [
          hours.toString().padStart(2, '0'),
          minutes.toString().padStart(2, '0'),
          secs.toString().padStart(2, '0')
        ].join(':');
      }
    });
  </script>
</body>
</html>
