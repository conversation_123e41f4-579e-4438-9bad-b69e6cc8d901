/**
 * Node WebVTT Processor
 *
 * Uses the node-webvtt package to properly parse VTT files
 * and handle different languages correctly.
 */

const webvtt = require('node-webvtt');
const he = require('he');

/**
 * Process a VTT file content using node-webvtt
 * @param {string} vttContent - Raw VTT file content
 * @param {string} lang - Language code
 * @returns {Array} Processed transcript items
 */
function processVttContent(vttContent, lang) {
  console.log(`Processing VTT content for language: ${lang} using node-webvtt`);

  try {
    // Parse the VTT content using node-webvtt
    // Set strict to false to handle potential parsing errors
    const parsed = webvtt.parse(vttContent, { strict: false, meta: true });

    if (!parsed || !parsed.cues || parsed.cues.length === 0) {
      console.error('No cues found in VTT content');
      return [];
    }

    console.log(`Parsed ${parsed.cues.length} cues from VTT file`);

    // Convert the parsed cues to our transcript format
    // Special handling for YouTube VTT format with word-level timing
    const transcriptItems = processYouTubeVttCues(parsed.cues);

    // Check if the transcript contains Arabic text when we didn't request Arabic
    // But only for specific languages that might be confused with Arabic
    if (lang !== 'ar' && lang !== 'de' && lang !== 'ru' && lang !== 'ja' && lang !== 'zh') {
      const containsArabic = transcriptItems.some(item =>
        item.text && /[\u0600-\u06FF]/.test(item.text)
      );

      if (containsArabic) {
        console.log('Detected Arabic transcript but requested language is not Arabic. Skipping this method.');
        return [];
      }
    }

    // Clean up the transcript items
    const cleanedItems = cleanupTranscriptItems(transcriptItems);
    console.log(`Cleaned ${cleanedItems.length} transcript items`);

    return cleanedItems;
  } catch (error) {
    console.error(`Error processing VTT content: ${error.message}`);
    // Return an empty array in case of error
    return [];
  }
}

/**
 * Process YouTube VTT cues that may contain word-level timing information or standard VTT format
 * @param {Array} cues - Parsed VTT cues
 * @returns {Array} Processed transcript items
 */
function processYouTubeVttCues(cues) {
  console.log(`Processing ${cues.length} VTT cues`);

  // Check if this is word-level timing format or standard VTT format
  const hasWordLevelTiming = cues.some(cue =>
    cue.text && cue.text.includes('<') && cue.text.includes('>')
  );

  if (hasWordLevelTiming) {
    console.log('Detected word-level timing format, using advanced processing');
    return processWordLevelVttCues(cues);
  } else {
    console.log('Detected standard VTT format, using standard processing');
    return processStandardVttCues(cues);
  }
}

/**
 * Process YouTube VTT cues that contain word-level timing information
 * @param {Array} cues - Parsed VTT cues
 * @returns {Array} Processed transcript items
 */
function processWordLevelVttCues(cues) {
  console.log(`Processing ${cues.length} word-level VTT cues`);

  // YouTube VTT files have overlapping segments that cause duplication and sync issues
  // Strategy: Use word-level timing segments (correct timestamps) and remove overlaps

  // Step 1: Extract word-level timing segments (these have correct timestamps)
  const wordLevelCues = [];

  for (const cue of cues) {
    let text = decodeHtmlEntities(cue.text);

    // We want cues that contain word-level timing tags (these have correct timestamps)
    if (text.includes('<') && text.includes('>')) {
      // Extract clean text by removing timing tags
      let cleanText = text
        .replace(/<\d+:\d+:\d+\.\d+>/g, '') // Remove timestamp tags like <00:00:02.399>
        .replace(/<\d+:\d+\.\d+>/g, '')     // Remove timestamp tags like <00:02.399>
        .replace(/<\/?c>/g, '')             // Remove <c> and </c> tags
        .replace(/<[^>]*>/g, '')            // Remove any other HTML-like tags
        .replace(/\s+/g, ' ')               // Normalize whitespace
        .trim();

      // Skip empty or very short segments
      if (!cleanText || cleanText.length < 5) continue;

      wordLevelCues.push({
        start: cue.start,
        end: cue.end,
        text: cleanText
      });
    }
  }

  console.log(`Extracted ${wordLevelCues.length} word-level timing segments`);

  // Step 2: Create non-overlapping segments by removing text overlap
  const nonOverlappingCues = [];

  for (let i = 0; i < wordLevelCues.length; i++) {
    const currentCue = wordLevelCues[i];
    const nextCue = wordLevelCues[i + 1];

    let uniqueText = currentCue.text;

    // If there's a next segment, remove overlapping text
    if (nextCue) {
      uniqueText = removeTextOverlap(currentCue.text, nextCue.text);
    }

    // Only add segments with meaningful unique content
    if (uniqueText && uniqueText.trim().length > 3) {
      nonOverlappingCues.push({
        start: currentCue.start,
        end: currentCue.end,
        text: uniqueText.trim()
      });
    }
  }

  console.log(`Created ${nonOverlappingCues.length} non-overlapping segments`);

  // Step 3: Convert to final transcript format with corrected timestamps
  const finalItems = [];

  for (let i = 0; i < nonOverlappingCues.length; i++) {
    const cue = nonOverlappingCues[i];

    // Skip very short or empty items
    if (!cue.text || cue.text.length < 5) continue;

    // Fix timestamp synchronization: shift start times
    let correctedStart;
    let correctedEnd;

    if (i === 0) {
      // First section starts at 00:00
      correctedStart = 0;
      correctedEnd = nonOverlappingCues[i + 1] ? nonOverlappingCues[i + 1].start : cue.end;
    } else {
      // Each section's start time is the previous section's original start time
      correctedStart = nonOverlappingCues[i - 1].start;
      correctedEnd = i < nonOverlappingCues.length - 1 ? nonOverlappingCues[i].start : cue.end;
    }

    finalItems.push({
      id: finalItems.length + 1,
      start: correctedStart,
      end: correctedEnd,
      formattedStart: formatTime(correctedStart),
      formattedEnd: formatTime(correctedEnd),
      text: cue.text
    });
  }

  console.log(`Final result: ${finalItems.length} transcript items with corrected timestamps`);
  return finalItems;
}

/**
 * Process standard VTT cues (without word-level timing)
 * @param {Array} cues - Parsed VTT cues
 * @returns {Array} Processed transcript items
 */
function processStandardVttCues(cues) {
  console.log(`Processing ${cues.length} standard VTT cues`);

  const finalItems = [];

  for (let i = 0; i < cues.length; i++) {
    const cue = cues[i];
    let text = decodeHtmlEntities(cue.text);

    // Clean up the text
    text = text
      .replace(/<[^>]*>/g, '')            // Remove any HTML-like tags
      .replace(/\s+/g, ' ')               // Normalize whitespace
      .trim();

    // Skip empty, very short, or non-speech content
    if (!text || text.length < 3) continue;
    if (text === '(Laughter)' || text === '(Applause)' || text.match(/^\([^)]*\)$/)) continue;

    finalItems.push({
      id: finalItems.length + 1,
      start: cue.start,
      end: cue.end,
      formattedStart: formatTime(cue.start),
      formattedEnd: formatTime(cue.end),
      text: text
    });
  }

  console.log(`Final result: ${finalItems.length} standard transcript items`);
  return finalItems;
}

/**
 * Remove overlapping text between current and next segments
 * @param {string} currentText - Current segment text
 * @param {string} nextText - Next segment text
 * @returns {string} Current text with overlap removed
 */
function removeTextOverlap(currentText, nextText) {
  if (!currentText || !nextText) return currentText || '';

  const currentWords = currentText.split(/\s+/);
  const nextWords = nextText.split(/\s+/);

  // Find the longest suffix of currentWords that is also a prefix of nextWords
  let maxOverlap = 0;
  for (let i = 1; i <= Math.min(currentWords.length, nextWords.length); i++) {
    const currentSuffix = currentWords.slice(-i);
    const nextPrefix = nextWords.slice(0, i);

    if (currentSuffix.join(' ').toLowerCase() === nextPrefix.join(' ').toLowerCase()) {
      maxOverlap = i;
    }
  }

  // Return the unique part (everything except the overlapping suffix)
  if (maxOverlap > 0) {
    const uniquePart = currentWords.slice(0, -maxOverlap);
    const result = uniquePart.join(' ');
    console.log(`Removed overlap: "${currentText}" -> "${result}" (${maxOverlap} words)`);
    return result;
  }

  return currentText;
}

/**
 * Check if two text segments are sequential (one continues from the other)
 * @param {string} text1 - First text
 * @param {string} text2 - Second text
 * @returns {boolean} True if text2 continues from text1
 */
function isSequentialText(text1, text2) {
  if (!text1 || !text2) return false;

  // Normalize texts
  const normalize = (text) => text.toLowerCase().replace(/[^\w\s]/g, '').replace(/\s+/g, ' ').trim();
  const norm1 = normalize(text1);
  const norm2 = normalize(text2);

  const words1 = norm1.split(' ');
  const words2 = norm2.split(' ');

  // Check if text2 starts with the last few words of text1
  const lastWords1 = words1.slice(-4); // Check last 4 words
  const firstWords2 = words2.slice(0, 4); // Check first 4 words

  // Look for overlap
  for (let i = 1; i <= Math.min(lastWords1.length, firstWords2.length); i++) {
    const overlap1 = lastWords1.slice(-i);
    const overlap2 = firstWords2.slice(0, i);

    if (overlap1.join(' ') === overlap2.join(' ')) {
      return true;
    }
  }

  return false;
}

/**
 * Combine sequential text segments by removing overlap
 * @param {string} text1 - First text
 * @param {string} text2 - Second text
 * @returns {string} Combined text or null if not suitable
 */
function combineSequentialText(text1, text2) {
  if (!text1 || !text2) return null;

  // Normalize for processing but keep original case
  const words1 = text1.split(' ');
  const words2 = text2.split(' ');

  const norm1 = text1.toLowerCase().replace(/[^\w\s]/g, '').replace(/\s+/g, ' ').trim();
  const norm2 = text2.toLowerCase().replace(/[^\w\s]/g, '').replace(/\s+/g, ' ').trim();

  const normWords1 = norm1.split(' ');
  const normWords2 = norm2.split(' ');

  // Find the overlap
  let overlapLength = 0;
  for (let i = 1; i <= Math.min(normWords1.length, normWords2.length); i++) {
    const lastNormWords1 = normWords1.slice(-i);
    const firstNormWords2 = normWords2.slice(0, i);

    if (lastNormWords1.join(' ') === firstNormWords2.join(' ')) {
      overlapLength = i;
    }
  }

  if (overlapLength > 0) {
    // Combine by removing the overlapping part from text2
    const remainingWords2 = words2.slice(overlapLength);
    if (remainingWords2.length > 0) {
      return text1 + ' ' + remainingWords2.join(' ');
    } else {
      // text2 is completely contained in text1
      return text1;
    }
  }

  // No overlap found, don't combine
  return null;
}

/**
 * Calculate text similarity between two strings
 * @param {string} text1 - First text
 * @param {string} text2 - Second text
 * @returns {number} Similarity score between 0 and 1
 */
function calculateTextSimilarity(text1, text2) {
  if (!text1 || !text2) return 0;

  // Normalize texts for comparison
  const normalize = (text) => text.toLowerCase().replace(/[^\w\s]/g, '').replace(/\s+/g, ' ').trim();
  const norm1 = normalize(text1);
  const norm2 = normalize(text2);

  if (norm1 === norm2) return 1;

  // Calculate word-based similarity
  const words1 = norm1.split(' ');
  const words2 = norm2.split(' ');

  const allWords = new Set([...words1, ...words2]);
  const commonWords = words1.filter(word => words2.includes(word));

  return commonWords.length / allWords.size;
}

/**
 * Calculate word overlap ratio between two texts
 * @param {string} text1 - First text
 * @param {string} text2 - Second text
 * @returns {number} Overlap ratio between 0 and 1
 */
function calculateWordOverlap(text1, text2) {
  if (!text1 || !text2) return 0;

  const words1 = text1.toLowerCase().split(/\s+/);
  const words2 = text2.toLowerCase().split(/\s+/);

  const commonWords = words1.filter(word => words2.includes(word));
  const minLength = Math.min(words1.length, words2.length);

  return minLength > 0 ? commonWords.length / minLength : 0;
}

/**
 * Merge two texts by adding unique words from text2 to text1
 * @param {string} text1 - Base text
 * @param {string} text2 - Text to merge from
 * @returns {string} Merged text
 */
function mergeWithUniqueWords(text1, text2) {
  if (!text1 || !text2) return text1 || text2 || '';

  const words1 = text1.split(/\s+/);
  const words2 = text2.split(/\s+/);

  // Find words in text2 that aren't in text1
  const uniqueWords = words2.filter(word => !words1.includes(word));

  // If there are unique words, append them
  if (uniqueWords.length > 0) {
    return text1 + ' ' + uniqueWords.join(' ');
  }

  // If no unique words, return the longer text
  return text1.length >= text2.length ? text1 : text2;
}

/**
 * Extract unique content from current text that doesn't overlap with next text
 * @param {string} currentText - Current segment text
 * @param {string} nextText - Next segment text
 * @returns {string} Unique content from current text
 */
function extractUniqueContent(currentText, nextText) {
  if (!currentText) return '';
  if (!nextText) return currentText;

  const currentWords = currentText.split(/\s+/);
  const nextWords = nextText.split(/\s+/);

  // Find the longest suffix of currentWords that is also a prefix of nextWords
  let maxOverlap = 0;
  for (let i = 1; i <= Math.min(currentWords.length, nextWords.length); i++) {
    const currentSuffix = currentWords.slice(-i);
    const nextPrefix = nextWords.slice(0, i);

    if (currentSuffix.join(' ').toLowerCase() === nextPrefix.join(' ').toLowerCase()) {
      maxOverlap = i;
    }
  }

  // Return the unique part (everything except the overlapping suffix)
  if (maxOverlap > 0) {
    const uniquePart = currentWords.slice(0, -maxOverlap);
    const result = uniquePart.join(' ');
    console.log(`Overlap detected: "${currentText}" -> "${result}" (removed ${maxOverlap} words)`);
    return result;
  }

  return currentText;
}

/**
 * Merge complementary text segments
 * @param {string} text1 - First text
 * @param {string} text2 - Second text
 * @returns {string} Merged text or null if not suitable for merging
 */
function mergeComplementaryText(text1, text2) {
  if (!text1 || !text2) return null;

  // Don't merge if one text is much longer than the other (likely not complementary)
  const ratio = Math.max(text1.length, text2.length) / Math.min(text1.length, text2.length);
  if (ratio > 3) return null;

  // Simple approach: if text2 seems to continue text1, append it
  // This is a basic heuristic and could be improved
  const words1 = text1.split(' ');
  const words2 = text2.split(' ');

  // Check if text2 starts with some words from the end of text1
  const lastWords1 = words1.slice(-3);
  const firstWords2 = words2.slice(0, 3);

  let overlap = 0;
  for (let i = 0; i < Math.min(lastWords1.length, firstWords2.length); i++) {
    if (lastWords1[i] === firstWords2[i]) {
      overlap++;
    } else {
      break;
    }
  }

  if (overlap > 0) {
    // Remove the overlapping words from text2 and append
    const remainingWords2 = words2.slice(overlap);
    if (remainingWords2.length > 0) {
      return text1 + ' ' + remainingWords2.join(' ');
    }
  }

  // If no clear overlap pattern, don't merge
  return null;
}

/**
 * Format time in seconds to MM:SS format
 * @param {number} timeInSeconds - Time in seconds
 * @returns {string} Formatted time string
 */
function formatTime(timeInSeconds) {
  if (typeof timeInSeconds !== 'number' || isNaN(timeInSeconds)) {
    return '00:00';
  }

  const minutes = Math.floor(timeInSeconds / 60);
  const seconds = Math.floor(timeInSeconds % 60);

  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
}

/**
 * Decode HTML entities in text
 * @param {string} text - Text with HTML entities
 * @returns {string} Decoded text
 */
function decodeHtmlEntities(text) {
  if (!text) return '';

  // First use the 'he' library for standard HTML entities
  let decodedText = he.decode(text);

  // Handle common YouTube-specific entities and other escaped characters
  // that might not be properly decoded by the he library
  decodedText = decodedText
    // Common HTML entities
    .replace(/&#39;/g, "'")
    .replace(/&amp;#39;/g, "'")
    .replace(/&quot;/g, '"')
    .replace(/&amp;quot;/g, '"')
    .replace(/&amp;amp;/g, '&')
    .replace(/&nbsp;/g, ' ')
    .replace(/&amp;nbsp;/g, ' ')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&amp;lt;/g, '<')
    .replace(/&amp;gt;/g, '>')
    // Unicode characters
    .replace(/\\u([0-9a-fA-F]{4})/g, (match, hex) => String.fromCharCode(parseInt(hex, 16)))
    // Other common escaped characters
    .replace(/\\n/g, '\n')
    .replace(/\\t/g, '\t')
    .replace(/\\r/g, '\r')
    .replace(/\\\\/g, '\\')
    .replace(/\\'/g, "'")
    .replace(/\\"/g, '"');

  return decodedText;
}

/**
 * Clean up transcript items by fixing punctuation, removing duplicates, etc.
 * @param {Array} items - Transcript items to clean up
 * @returns {Array} Cleaned transcript items
 */
function cleanupTranscriptItems(items) {
  if (!items || items.length === 0) return [];

  const cleanedItems = [];

  // Process each item
  for (let i = 0; i < items.length; i++) {
    if (!items[i].text || items[i].text.trim() === '') continue;

    const cleanedItem = { ...items[i] };

    // Remove any remaining HTML-like tags
    cleanedItem.text = cleanedItem.text.replace(/<[^>]*>/g, '');

    // Ensure spaces after punctuation
    cleanedItem.text = cleanedItem.text.replace(/([.!?,;:])([^\s])/g, '$1 $2');

    // Fix spacing around periods in numbers (e.g., "200 .000" -> "200.000")
    cleanedItem.text = cleanedItem.text.replace(/(\d+)\s+\.(\d+)/g, '$1.$2');

    // Fix incorrect spacing in numbers (e.g., "200. 000" -> "200.000")
    cleanedItem.text = cleanedItem.text.replace(/(\d+)\.(\s+)(\d+)/g, '$1.$3');

    // Remove repeated words (e.g., "I was was going" -> "I was going")
    cleanedItem.text = cleanedItem.text.replace(/\b(\w+)(\s+\1\b)+/gi, '$1');

    // Normalize whitespace
    cleanedItem.text = cleanedItem.text.replace(/\s+/g, ' ').trim();

    // Skip if the text is empty after cleaning
    if (!cleanedItem.text) continue;

    // Skip very short fragments
    if (cleanedItem.text.length < 2) continue;

    // Enhanced duplicate detection
    let isDuplicate = false;

    // Check against recent items (not just the previous one)
    for (let j = Math.max(0, cleanedItems.length - 5); j < cleanedItems.length; j++) {
      const existingItem = cleanedItems[j];

      // Check for exact duplicates
      if (cleanedItem.text === existingItem.text) {
        console.log(`Skipping exact duplicate: "${cleanedItem.text}"`);
        isDuplicate = true;
        break;
      }

      // Check if this text is completely contained in an existing item
      if (existingItem.text.includes(cleanedItem.text) && cleanedItem.text.length > 10) {
        console.log(`Skipping contained text: "${cleanedItem.text}" (contained in "${existingItem.text}")`);
        isDuplicate = true;
        break;
      }

      // Check if an existing item is completely contained in this text
      if (cleanedItem.text.includes(existingItem.text) && existingItem.text.length > 10) {
        console.log(`Replacing shorter text: "${existingItem.text}" with "${cleanedItem.text}"`);
        // Replace the existing item with the longer one
        existingItem.text = cleanedItem.text;
        existingItem.end = cleanedItem.end;
        isDuplicate = true;
        break;
      }
    }

    if (!isDuplicate) {
      cleanedItems.push(cleanedItem);
    }
  }

  // Renumber the IDs
  for (let i = 0; i < cleanedItems.length; i++) {
    cleanedItems[i].id = i + 1;
  }

  // Ensure proper capitalization of sentences
  capitalizeFirstLetterOfSentences(cleanedItems);

  return cleanedItems;
}



/**
 * Capitalize the first letter of each sentence across all transcript items
 * @param {Array} items - Transcript items to process
 */
function capitalizeFirstLetterOfSentences(items) {
  if (!items || items.length === 0) return;

  let shouldCapitalize = true;

  for (let i = 0; i < items.length; i++) {
    const item = items[i];
    if (!item.text) continue;

    // Split the text into sentences
    const sentences = item.text.split(/([.!?]\s+)/).filter(Boolean);
    let result = '';

    for (let j = 0; j < sentences.length; j++) {
      const part = sentences[j];

      // If this is just a punctuation mark with space
      if (/^[.!?]\s+$/.test(part)) {
        result += part;
        shouldCapitalize = true;
        continue;
      }

      // If we should capitalize this part
      if (shouldCapitalize) {
        // Find the first letter in the sentence
        const firstLetterMatch = part.match(/[a-zA-Z]/);
        if (firstLetterMatch) {
          const firstLetterIndex = firstLetterMatch.index;
          result += part.substring(0, firstLetterIndex) +
                   part.charAt(firstLetterIndex).toUpperCase() +
                   part.substring(firstLetterIndex + 1);
        } else {
          result += part;
        }
        shouldCapitalize = false;
      } else {
        result += part;
      }
    }

    // Update the item text
    items[i].text = result;

    // Check if this item ends with a sentence-ending punctuation
    if (/[.!?]$/.test(item.text)) {
      shouldCapitalize = true;
    }
  }
}

/**
 * Format time in MM:SS format
 * @param {number} seconds - Time in seconds
 * @returns {string} Formatted time
 */
function formatTime(seconds) {
  if (typeof seconds !== 'number' || isNaN(seconds)) {
    seconds = 0;
  }

  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  if (hours > 0) {
    return [
      hours.toString().padStart(2, '0'),
      minutes.toString().padStart(2, '0'),
      secs.toString().padStart(2, '0')
    ].join(':');
  } else {
    return [
      minutes.toString().padStart(2, '0'),
      secs.toString().padStart(2, '0')
    ].join(':');
  }
}

module.exports = {
  processVttContent,
  cleanupTranscriptItems,
  formatTime
};
