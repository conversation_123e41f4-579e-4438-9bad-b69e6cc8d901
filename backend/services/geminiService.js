const { GoogleGenerativeAI } = require('@google/generative-ai');

class GeminiService {
  constructor() {
    this.initialized = false;
    this.apiKey = null;
    this.genAI = null;
    this.model = null;
  }

  /**
   * Initialize the service (lazy initialization)
   */
  _initialize() {
    if (this.initialized) {
      return;
    }

    this.apiKey = process.env.GOOGLE_API_KEY;

    if (!this.apiKey) {
      throw new Error('GOOGLE_API_KEY environment variable is not set');
    }

    // Initialize the Google AI client
    this.genAI = new GoogleGenerativeAI(this.apiKey);

    // Use Gemini 2.5 Flash Lite as requested
    this.model = this.genAI.getGenerativeModel({
      model: "models/gemini-2.5-flash-lite-preview-06-17"
    });

    this.initialized = true;
    console.log('Gemini service initialized with API key:', this.apiKey.substring(0, 5) + '...');
  }

  /**
   * Generate content using Gemini AI
   * @param {string} prompt - The prompt to send to Gemini
   * @param {Object} options - Additional options for generation
   * @returns {Promise<string>} - The generated response
   */
  async generateContent(prompt, options = {}) {
    try {
      this._initialize(); // Ensure service is initialized

      if (!prompt || typeof prompt !== 'string') {
        throw new Error('Prompt must be a non-empty string');
      }

      // Default generation config
      const generationConfig = {
        temperature: options.temperature || 0.7,
        topK: options.topK || 40,
        topP: options.topP || 0.95,
        maxOutputTokens: options.maxOutputTokens || 1024,
        ...options.generationConfig
      };

      // Generate content
      const result = await this.model.generateContent({
        contents: [{ role: 'user', parts: [{ text: prompt }] }],
        generationConfig
      });

      const response = await result.response;
      const text = response.text();

      if (!text) {
        throw new Error('No response generated from Gemini');
      }

      return text;
    } catch (error) {
      console.error('Error generating content with Gemini:', error);
      
      // Handle specific API errors
      if (error.message?.includes('API_KEY_INVALID')) {
        throw new Error('Invalid Google API key');
      } else if (error.message?.includes('QUOTA_EXCEEDED')) {
        throw new Error('API quota exceeded');
      } else if (error.message?.includes('MODEL_NOT_FOUND')) {
        throw new Error('Gemini model not found or not available');
      }
      
      throw new Error(`Gemini API error: ${error.message}`);
    }
  }

  /**
   * Test the Gemini API connection
   * @returns {Promise<Object>} - Test result with status and response
   */
  async testConnection() {
    try {
      this._initialize(); // Ensure service is initialized

      const testPrompt = "Tell me a joke";
      const response = await this.generateContent(testPrompt);
      
      return {
        success: true,
        message: 'Gemini API connection successful',
        testPrompt,
        response,
        model: 'models/gemini-2.5-flash-lite-preview-06-17'
      };
    } catch (error) {
      return {
        success: false,
        message: 'Gemini API connection failed',
        error: error.message,
        model: 'models/gemini-2.5-flash-lite-preview-06-17'
      };
    }
  }

  /**
   * Get model information
   * @returns {Object} - Model information
   */
  getModelInfo() {
    try {
      this._initialize(); // Ensure service is initialized
    } catch (error) {
      // If initialization fails, return basic info
      return {
        model: 'models/gemini-2.5-flash-lite-preview-06-17',
        provider: 'Google AI',
        apiKeyConfigured: false,
        apiKeyPrefix: 'Not configured',
        error: error.message
      };
    }

    return {
      model: 'models/gemini-2.5-flash-lite-preview-06-17',
      provider: 'Google AI',
      apiKeyConfigured: !!this.apiKey,
      apiKeyPrefix: this.apiKey ? this.apiKey.substring(0, 5) + '...' : 'Not configured'
    };
  }
}

module.exports = new GeminiService();
