/**
 * Non-English Transcript Processor
 *
 * Specialized processor for handling non-English transcripts, particularly those
 * with embedded timestamp tags and other formatting issues.
 */

/**
 * Process a non-English transcript to fix common issues
 * @param {Array} transcriptItems - Raw transcript items
 * @returns {Array} Processed transcript items
 */
function processNonEnglishTranscript(transcriptItems) {
  if (!transcriptItems || transcriptItems.length === 0) {
    return [];
  }

  console.log(`Processing ${transcriptItems.length} non-English transcript items...`);

  // Step 1: Remove timestamp tags and clean text
  const cleanedItems = transcriptItems.map(item => {
    const cleanedItem = { ...item };

    // Remove timestamp tags and <c> tags
    cleanedItem.text = cleanedItem.text
      .replace(/<\d+:\d+:\d+\.\d+>|<\d+:\d+\.\d+>/g, '') // Remove timestamp tags
      .replace(/<\/?c>/g, '') // Remove <c> and </c> tags
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim();

    return cleanedItem;
  });

  console.log(`After cleaning: ${cleanedItems.length} items`);

  // Step 2: Remove duplicate segments (those with identical text)
  const uniqueItems = [];
  const seenTexts = new Set();

  for (const item of cleanedItems) {
    // Skip empty items
    if (!item.text) continue;

    // Skip if we've seen this exact text before
    if (seenTexts.has(item.text)) continue;

    // Add to unique items and mark as seen
    uniqueItems.push(item);
    seenTexts.add(item.text);
  }

  console.log(`After removing duplicates: ${uniqueItems.length} items`);

  // Step 3: Remove segments with very short durations (less than 0.1 seconds)
  const validDurationItems = uniqueItems.filter(item => {
    return (item.end - item.start) >= 0.1;
  });

  console.log(`After removing short segments: ${validDurationItems.length} items`);

  // Step 4: Merge segments that are part of the same sentence
  const mergedItems = [];
  let currentItem = null;

  for (const item of validDurationItems) {
    // If this is the first item or the current item ends with sentence-ending punctuation
    if (!currentItem || /[.!?]$/.test(currentItem.text)) {
      // Start a new segment
      currentItem = { ...item };
      mergedItems.push(currentItem);
    } else {
      // This is a continuation of the current segment
      // Check if the current text is a prefix of the new text
      if (item.text.startsWith(currentItem.text)) {
        // Replace the current text with the new text
        currentItem.text = item.text;
        currentItem.end = item.end;
      } else if (currentItem.text.endsWith(item.text.split(' ')[0])) {
        // If the current text ends with the first word of the new text, merge them properly
        const firstWord = item.text.split(' ')[0];
        const restOfText = item.text.substring(firstWord.length).trim();
        currentItem.text = currentItem.text + restOfText;
        currentItem.end = item.end;
      } else {
        // Append the new text to the current text
        currentItem.text += ' ' + item.text;
        currentItem.end = item.end;
      }
    }
  }

  console.log(`After merging segments: ${mergedItems.length} items`);

  // Step 5: Fix overlapping segments
  for (let i = 0; i < mergedItems.length - 1; i++) {
    // If the current segment's end time is after the next segment's start time
    if (mergedItems[i].end > mergedItems[i + 1].start) {
      // Adjust the current segment's end time to match the next segment's start time
      mergedItems[i].end = mergedItems[i + 1].start;
    }
  }

  // Step 6: Fix punctuation issues
  for (let i = 0; i < mergedItems.length; i++) {
    // Ensure there's always a space after punctuation marks
    mergedItems[i].text = mergedItems[i].text.replace(/([.!?,;:])([^\s])/g, '$1 $2');

    // Remove punctuation at the beginning of segments
    mergedItems[i].text = mergedItems[i].text.replace(/^\s*[.!?,;:]+\s*/, '');
  }

  // Step 7: Ensure proper sentence capitalization
  let isFirstSegment = true;
  for (let i = 0; i < mergedItems.length; i++) {
    const item = mergedItems[i];

    // Split the text into sentences
    const sentences = item.text.split(/([.!?])\s+/).filter(Boolean);
    let processedText = '';

    for (let j = 0; j < sentences.length; j++) {
      const sentence = sentences[j];

      // If this is a single punctuation mark, just add it
      if (sentence === '.' || sentence === '!' || sentence === '?') {
        processedText += sentence;
        continue;
      }

      // If this is the start of a new sentence or the first segment, capitalize it
      if ((j === 0 && isFirstSegment) ||
          (j > 0 && (sentences[j-1] === '.' || sentences[j-1] === '!' || sentences[j-1] === '?'))) {
        processedText += sentence.charAt(0).toUpperCase() + sentence.slice(1);
      } else {
        processedText += sentence;
      }

      // Add a space after non-punctuation parts
      if (j < sentences.length - 1 && sentence !== '.' && sentence !== '!' && sentence !== '?') {
        processedText += ' ';
      }
    }

    // Update the text
    mergedItems[i].text = processedText;

    // After processing the first segment, set isFirstSegment to false
    if (isFirstSegment) {
      isFirstSegment = false;
    }
  }

  // Step 8: Renumber the IDs
  mergedItems.forEach((item, index) => {
    item.id = index + 1;
  });

  console.log(`Final processed transcript: ${mergedItems.length} items`);
  return mergedItems;
}

/**
 * Process a VTT file content directly
 * @param {string} vttContent - Raw VTT file content
 * @returns {Array} Processed transcript items
 */
function processVttContent(vttContent) {
  // Parse the VTT content
  const rawItems = parseVttContent(vttContent);

  // Process the raw items
  return processNonEnglishTranscript(rawItems);
}

/**
 * Parse VTT content into transcript items
 * @param {string} vttContent - Raw VTT file content
 * @returns {Array} Raw transcript items
 */
function parseVttContent(vttContent) {
  console.log('Parsing VTT content with specialized non-English parser...');

  // Split the content into blocks (each subtitle entry)
  const blocks = vttContent.trim().split(/\n\s*\n/);
  const transcriptItems = [];

  console.log(`Found ${blocks.length} blocks in VTT file`);

  for (let i = 0; i < blocks.length; i++) {
    const block = blocks[i].trim();

    // Skip the WEBVTT header block
    if (block === 'WEBVTT' || block.startsWith('WEBVTT ') ||
        block.startsWith('Kind:') || block.startsWith('Language:')) {
      continue;
    }

    // Split the block into lines
    const lines = block.split('\n');

    // Find the line with the timestamp
    const timestampLineIndex = lines.findIndex(line => line.includes('-->'));

    if (timestampLineIndex === -1) {
      continue; // Skip blocks without timestamps
    }

    // Extract the timestamp
    const timestampLine = lines[timestampLineIndex];
    const times = timestampLine.split('-->').map(t => t.trim());

    // Parse the start time
    const startTime = parseVttTimestamp(times[0]);

    // Parse the end time, removing any alignment info
    const endTimePart = times[1].split(' ')[0];
    const endTime = parseVttTimestamp(endTimePart);

    // Extract the text (all lines after the timestamp line)
    const textLines = lines.slice(timestampLineIndex + 1);

    // Skip empty text blocks
    if (textLines.length === 0) {
      continue;
    }

    // Join all text lines with a space
    let text = textLines.join(' ');

    // Skip empty text after joining
    if (!text.trim()) {
      continue;
    }

    // Add to transcript items
    transcriptItems.push({
      start: startTime,
      end: endTime,
      text: text
    });
  }

  // Sort by start time to ensure proper sequence
  transcriptItems.sort((a, b) => a.start - b.start);

  console.log(`Parsed ${transcriptItems.length} transcript items from VTT file`);

  return transcriptItems;
}

/**
 * Parse VTT timestamp to seconds
 * @param {string} timestamp - VTT timestamp (HH:MM:SS.mmm)
 * @returns {number} Time in seconds
 */
function parseVttTimestamp(timestamp) {
  // Handle both HH:MM:SS.mmm and MM:SS.mmm formats
  const parts = timestamp.split(':');
  let hours = 0, minutes = 0, seconds = 0;

  if (parts.length === 3) {
    hours = parseInt(parts[0]);
    minutes = parseInt(parts[1]);
    seconds = parseFloat(parts[2]);
  } else if (parts.length === 2) {
    minutes = parseInt(parts[0]);
    seconds = parseFloat(parts[1]);
  }

  return hours * 3600 + minutes * 60 + seconds;
}

module.exports = {
  processNonEnglishTranscript,
  processVttContent
};
