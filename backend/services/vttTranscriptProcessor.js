/**
 * VTT Transcript Processor
 * 
 * A specialized processor for handling VTT transcripts from YouTube,
 * including proper handling of embedded timestamp tags and formatting.
 */

/**
 * Process a VTT file content directly
 * @param {string} vttContent - Raw VTT file content
 * @param {string} lang - Language code
 * @returns {Array} Processed transcript items
 */
function processVttContent(vttContent, lang) {
  console.log(`Processing VTT content for language: ${lang}`);
  
  // Parse the VTT content into raw items
  const rawItems = parseVttContent(vttContent);
  console.log(`Parsed ${rawItems.length} raw items from VTT file`);
  
  // Clean the raw items
  const cleanedItems = cleanTranscriptItems(rawItems);
  console.log(`Cleaned ${cleanedItems.length} transcript items`);
  
  // Merge short segments into meaningful sentences
  const mergedItems = mergeShortSegments(cleanedItems);
  console.log(`Merged into ${mergedItems.length} transcript items`);
  
  // Fix punctuation and capitalization
  const fixedItems = fixPunctuationAndCapitalization(mergedItems);
  console.log(`Fixed punctuation in ${fixedItems.length} transcript items`);
  
  // Add formatted timestamps
  const formattedItems = addFormattedTimestamps(fixedItems);
  console.log(`Added formatted timestamps to ${formattedItems.length} transcript items`);
  
  return formattedItems;
}

/**
 * Parse VTT content into transcript items
 * @param {string} vttContent - Raw VTT file content
 * @returns {Array} Raw transcript items
 */
function parseVttContent(vttContent) {
  // Split the content into blocks (each subtitle entry)
  const blocks = vttContent.trim().split(/\n\s*\n/);
  const transcriptItems = [];
  
  for (let i = 0; i < blocks.length; i++) {
    const block = blocks[i].trim();
    
    // Skip the WEBVTT header block
    if (block === 'WEBVTT' || block.startsWith('WEBVTT ') || 
        block.startsWith('Kind:') || block.startsWith('Language:')) {
      continue;
    }
    
    // Split the block into lines
    const lines = block.split('\n');
    
    // Find the line with the timestamp
    const timestampLineIndex = lines.findIndex(line => line.includes('-->'));
    
    if (timestampLineIndex === -1) {
      continue; // Skip blocks without timestamps
    }
    
    // Extract the timestamp
    const timestampLine = lines[timestampLineIndex];
    const times = timestampLine.split('-->').map(t => t.trim());
    
    // Parse the start time
    const startTime = parseVttTimestamp(times[0]);
    
    // Parse the end time, removing any alignment info
    const endTimePart = times[1].split(' ')[0];
    const endTime = parseVttTimestamp(endTimePart);
    
    // Extract the text (all lines after the timestamp line)
    const textLines = lines.slice(timestampLineIndex + 1);
    
    // Skip empty text blocks
    if (textLines.length === 0) {
      continue;
    }
    
    // Join all text lines with a space
    let text = textLines.join(' ');
    
    // Skip empty text after joining
    if (!text.trim()) {
      continue;
    }
    
    // Add to transcript items
    transcriptItems.push({
      start: startTime,
      end: endTime,
      text: text
    });
  }
  
  // Sort by start time to ensure proper sequence
  transcriptItems.sort((a, b) => a.start - b.start);
  
  return transcriptItems;
}

/**
 * Parse VTT timestamp to seconds
 * @param {string} timestamp - VTT timestamp (HH:MM:SS.mmm)
 * @returns {number} Time in seconds
 */
function parseVttTimestamp(timestamp) {
  // Handle both HH:MM:SS.mmm and MM:SS.mmm formats
  const parts = timestamp.split(':');
  let hours = 0, minutes = 0, seconds = 0;

  if (parts.length === 3) {
    hours = parseInt(parts[0]);
    minutes = parseInt(parts[1]);
    seconds = parseFloat(parts[2]);
  } else if (parts.length === 2) {
    minutes = parseInt(parts[0]);
    seconds = parseFloat(parts[1]);
  }

  return hours * 3600 + minutes * 60 + seconds;
}

/**
 * Clean transcript items by removing embedded timestamp tags and formatting
 * Also filters out very short duplicate segments that cause timestamp sync issues
 * @param {Array} items - Raw transcript items
 * @returns {Array} Cleaned transcript items
 */
function cleanTranscriptItems(items) {
  // First, clean the text content
  const cleanedItems = items.map(item => {
    // Create a new item to avoid modifying the original
    const cleanedItem = { ...item };

    // Remove embedded timestamp tags like <00:00:02.399>
    cleanedItem.text = cleanedItem.text.replace(/<\d+:\d+:\d+\.\d+>|<\d+:\d+\.\d+>/g, '');

    // Remove formatting tags like <c> and </c>
    cleanedItem.text = cleanedItem.text.replace(/<\/?c>/g, '');

    // Remove any other HTML-like tags
    cleanedItem.text = cleanedItem.text.replace(/<[^>]+>/g, '');

    // Normalize whitespace
    cleanedItem.text = cleanedItem.text.replace(/\s+/g, ' ').trim();

    return cleanedItem;
  });

  // Filter out very short segments that are likely duplicates
  // These cause timestamp synchronization issues
  const filteredItems = cleanedItems.filter(item => {
    const duration = item.end - item.start;

    // Remove segments shorter than 0.1 seconds (likely duplicates)
    if (duration < 0.1) {
      console.log(`Filtering out very short segment (${duration.toFixed(3)}s): "${item.text.substring(0, 50)}..."`);
      return false;
    }

    // Remove empty or whitespace-only segments
    if (!item.text || item.text.trim().length === 0) {
      console.log(`Filtering out empty segment`);
      return false;
    }

    return true;
  });

  console.log(`Filtered ${cleanedItems.length - filteredItems.length} short/empty segments`);
  return filteredItems;
}

/**
 * Merge short segments into meaningful sentences
 * @param {Array} items - Cleaned transcript items
 * @returns {Array} Merged transcript items
 */
function mergeShortSegments(items) {
  if (!items || items.length === 0) return [];
  
  const mergedItems = [];
  let currentItem = null;
  
  // Maximum duration for a segment (in seconds)
  const MAX_SEGMENT_DURATION = 10.0;
  
  for (let i = 0; i < items.length; i++) {
    const item = items[i];
    
    // If this is the first item or the current item is too long, start a new segment
    if (!currentItem || 
        (item.end - currentItem.start) > MAX_SEGMENT_DURATION ||
        (currentItem.text.endsWith('.') || currentItem.text.endsWith('?') || currentItem.text.endsWith('!'))) {
      
      // If we have a current item, add it to the result
      if (currentItem) {
        mergedItems.push(currentItem);
      }
      
      // Start a new segment
      currentItem = {
        start: item.start,
        end: item.end,
        text: item.text
      };
    } else {
      // Extend the current segment
      currentItem.end = item.end;
      
      // Only add a space if the current text doesn't end with a space
      // and the new text doesn't start with a space
      let separator = ' ';
      if (currentItem.text.endsWith(' ') || item.text.startsWith(' ')) {
        separator = '';
      }
      
      currentItem.text += separator + item.text;
    }
  }
  
  // Don't forget the last item
  if (currentItem) {
    mergedItems.push(currentItem);
  }
  
  return mergedItems;
}

/**
 * Fix punctuation and capitalization in transcript items
 * @param {Array} items - Merged transcript items
 * @returns {Array} Fixed transcript items
 */
function fixPunctuationAndCapitalization(items) {
  if (!items || items.length === 0) return [];
  
  const fixedItems = [];
  
  for (const item of items) {
    const fixedItem = { ...item };
    let text = fixedItem.text;
    
    // Fix spacing after punctuation
    text = text.replace(/([.!?,;:])([^\s])/g, '$1 $2');
    
    // Remove punctuation at the beginning of text
    text = text.replace(/^\s*[.!?,;:]+\s*/, '');
    
    // Fix spacing around periods in numbers (e.g., "200 .000" -> "200.000")
    text = text.replace(/(\d+)\s+\.(\d+)/g, '$1.$2');
    
    // Fix incorrect spacing in numbers (e.g., "200. 000" -> "200.000")
    text = text.replace(/(\d+)\.(\s+)(\d+)/g, '$1.$3');
    
    // Fix repeated words (e.g., "I was was going" -> "I was going")
    text = text.replace(/\b(\w+)(\s+\1\b)+/gi, '$1');
    
    // Ensure proper sentence capitalization
    text = capitalizeFirstLetterOfSentences(text);
    
    fixedItem.text = text;
    fixedItems.push(fixedItem);
  }
  
  return fixedItems;
}

/**
 * Capitalize the first letter of each sentence in a text
 * @param {string} text - Text to process
 * @returns {string} Text with capitalized sentences
 */
function capitalizeFirstLetterOfSentences(text) {
  if (!text) return '';
  
  // First, ensure there's a space after each punctuation mark
  let processedText = text.replace(/([.!?])([^\s])/g, '$1 $2');
  
  // Split the text into sentences
  const sentences = processedText.split(/([.!?]\s+)/).filter(Boolean);
  let result = '';
  let shouldCapitalize = true;
  
  for (let i = 0; i < sentences.length; i++) {
    const part = sentences[i];
    
    // If this is just a punctuation mark with space
    if (/^[.!?]\s+$/.test(part)) {
      result += part;
      shouldCapitalize = true;
      continue;
    }
    
    // If we should capitalize this part
    if (shouldCapitalize) {
      // Find the first letter in the sentence
      const firstLetterMatch = part.match(/[a-zA-Z]/);
      if (firstLetterMatch) {
        const firstLetterIndex = firstLetterMatch.index;
        result += part.substring(0, firstLetterIndex) + 
                 part.charAt(firstLetterIndex).toUpperCase() + 
                 part.substring(firstLetterIndex + 1);
      } else {
        result += part;
      }
      shouldCapitalize = false;
    } else {
      result += part;
    }
  }
  
  return result;
}

/**
 * Add formatted timestamps to transcript items
 * @param {Array} items - Transcript items
 * @returns {Array} Transcript items with formatted timestamps
 */
function addFormattedTimestamps(items) {
  return items.map((item, index) => {
    return {
      ...item,
      id: index + 1,
      formattedStart: formatTime(item.start),
      formattedEnd: formatTime(item.end)
    };
  });
}

/**
 * Format time in MM:SS format
 * @param {number} seconds - Time in seconds
 * @returns {string} Formatted time
 */
function formatTime(seconds) {
  if (typeof seconds !== 'number' || isNaN(seconds)) {
    seconds = 0;
  }
  
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  
  if (hours > 0) {
    return [
      hours.toString().padStart(2, '0'),
      minutes.toString().padStart(2, '0'),
      secs.toString().padStart(2, '0')
    ].join(':');
  } else {
    return [
      minutes.toString().padStart(2, '0'),
      secs.toString().padStart(2, '0')
    ].join(':');
  }
}

module.exports = {
  processVttContent,
  parseVttContent,
  cleanTranscriptItems,
  mergeShortSegments,
  fixPunctuationAndCapitalization,
  addFormattedTimestamps
};
