/**
 * Improved Non-English Transcript Processor
 *
 * A completely redesigned processor for handling non-English transcripts,
 * particularly those with embedded timestamp tags and other formatting issues.
 */

/**
 * Process a non-English transcript to fix common issues
 * @param {Array} transcriptItems - Raw transcript items
 * @returns {Array} Processed transcript items
 */
function processNonEnglishTranscript(transcriptItems) {
  if (!transcriptItems || transcriptItems.length === 0) {
    return [];
  }

  console.log(`Processing ${transcriptItems.length} non-English transcript items...`);

  // Step 1: Clean each item (remove timestamp tags, HTML tags, etc.)
  const cleanedItems = transcriptItems.map(item => {
    const cleanedItem = { ...item };

    // Remove timestamp tags and <c> tags
    cleanedItem.text = cleanedItem.text
      .replace(/<\d+:\d+:\d+\.\d+>|<\d+:\d+\.\d+>/g, '') // Remove timestamp tags
      .replace(/<\/?c>/g, '') // Remove <c> and </c> tags
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim();

    return cleanedItem;
  });

  console.log(`After basic cleaning: ${cleanedItems.length} items`);

  // Step 2: Remove empty items
  const nonEmptyItems = cleanedItems.filter(item => item.text.trim().length > 0);
  console.log(`After removing empty items: ${nonEmptyItems.length} items`);

  // Step 3: Remove duplicate consecutive items with identical text
  const uniqueItems = [];
  for (let i = 0; i < nonEmptyItems.length; i++) {
    if (i === 0 || nonEmptyItems[i].text !== nonEmptyItems[i-1].text) {
      uniqueItems.push(nonEmptyItems[i]);
    }
  }
  console.log(`After removing duplicates: ${uniqueItems.length} items`);

  // Step 4: Remove very short segments (less than 0.5 seconds)
  const validDurationItems = uniqueItems.filter(item => {
    return (item.end - item.start) >= 0.5;
  });
  console.log(`After removing short segments: ${validDurationItems.length} items`);

  // Step 5: Group segments by time proximity to form complete sentences
  const groupedSegments = groupSegmentsByTimeProximity(validDurationItems);
  console.log(`After grouping by time proximity: ${groupedSegments.length} groups`);

  // Step 6: Merge segments within each group to form coherent sentences
  const mergedSegments = mergeSegmentsIntoSentences(groupedSegments);
  console.log(`After merging into sentences: ${mergedSegments.length} segments`);

  // Step 7: Fix punctuation and capitalization
  const fixedSegments = fixPunctuationAndCapitalization(mergedSegments);
  console.log(`After fixing punctuation: ${fixedSegments.length} segments`);

  // Step 8: Renumber the IDs
  fixedSegments.forEach((item, index) => {
    item.id = index + 1;
  });

  return fixedSegments;
}

/**
 * Group segments by time proximity and natural sentence boundaries
 * @param {Array} items - Transcript items
 * @returns {Array} Array of segment groups
 */
function groupSegmentsByTimeProximity(items) {
  if (!items || items.length === 0) return [];

  const groups = [];
  let currentGroup = [items[0]];

  // Time threshold for considering segments as part of the same group (in seconds)
  const TIME_THRESHOLD = 0.3;

  // Maximum duration for a group (in seconds)
  const MAX_GROUP_DURATION = 15.0;

  // Maximum number of segments in a group
  const MAX_SEGMENTS_PER_GROUP = 10;

  for (let i = 1; i < items.length; i++) {
    const prevItem = items[i-1];
    const currentItem = items[i];

    // Calculate the current group's duration
    const groupStartTime = currentGroup[0].start;
    const groupEndTime = prevItem.end;
    const groupDuration = groupEndTime - groupStartTime;

    // Check if we should start a new group based on:
    // 1. Time gap between segments
    // 2. Maximum group duration
    // 3. Maximum segments per group
    // 4. Natural sentence boundaries (ending with period, question mark, exclamation mark)
    const hasTimeBoundary = currentItem.start - prevItem.end > TIME_THRESHOLD;
    const exceedsMaxDuration = groupDuration >= MAX_GROUP_DURATION;
    const exceedsMaxSegments = currentGroup.length >= MAX_SEGMENTS_PER_GROUP;
    const hasSentenceBoundary = prevItem.text.trim().match(/[.!?]$/);

    if (hasTimeBoundary || exceedsMaxDuration || exceedsMaxSegments || hasSentenceBoundary) {
      // Start a new group
      groups.push(currentGroup);
      currentGroup = [currentItem];
    } else {
      // Add to the current group
      currentGroup.push(currentItem);
    }
  }

  // Don't forget the last group
  if (currentGroup.length > 0) {
    groups.push(currentGroup);
  }

  return groups;
}

/**
 * Merge segments within each group to form coherent sentences
 * @param {Array} groups - Groups of transcript items
 * @returns {Array} Merged transcript items
 */
function mergeSegmentsIntoSentences(groups) {
  if (!groups || groups.length === 0) return [];

  const mergedItems = [];

  // Keep track of incomplete sentences
  let pendingText = '';

  for (let i = 0; i < groups.length; i++) {
    const group = groups[i];
    if (group.length === 0) continue;

    // Start with the first item in the group
    const mergedItem = {
      start: group[0].start,
      end: group[group.length - 1].end,
      text: ''
    };

    // Collect all text from the group
    const textParts = [];
    for (const item of group) {
      textParts.push(item.text);
    }

    // Join the text parts, removing duplicates
    let mergedText = removeDuplicatePhrases(textParts.join(' '));

    // If we have pending text from a previous segment, prepend it
    if (pendingText) {
      // Check if this segment starts with a lowercase letter (indicating continuation)
      const firstLetterMatch = mergedText.match(/[a-zA-Z]/);
      if (firstLetterMatch && mergedText.charAt(firstLetterMatch.index).toLowerCase() === mergedText.charAt(firstLetterMatch.index)) {
        // This is likely a continuation of the previous sentence
        mergedText = pendingText + ' ' + mergedText;
        pendingText = '';
      } else {
        // This is likely a new sentence, so add the pending text as its own segment
        mergedItems.push({
          start: groups[i-1][0].start,
          end: groups[i-1][groups[i-1].length - 1].end,
          text: pendingText
        });
        pendingText = '';
      }
    }

    // Check if this segment ends with a complete sentence
    if (mergedText.trim().match(/[.!?]$/)) {
      // Complete sentence, add it as is
      mergedItem.text = mergedText;
      mergedItems.push(mergedItem);
    } else {
      // Incomplete sentence
      // If this is the last group, add it as is
      if (i === groups.length - 1) {
        mergedItem.text = mergedText;
        mergedItems.push(mergedItem);
      } else {
        // Otherwise, save it for the next iteration
        pendingText = mergedText;
      }
    }
  }

  // If we still have pending text at the end, add it
  if (pendingText) {
    const lastGroup = groups[groups.length - 1];
    mergedItems.push({
      start: lastGroup[0].start,
      end: lastGroup[lastGroup.length - 1].end,
      text: pendingText
    });
  }

  return mergedItems;
}

/**
 * Remove duplicate phrases from text
 * @param {string} text - Text to process
 * @returns {string} Text with duplicates removed
 */
function removeDuplicatePhrases(text) {
  if (!text) return '';

  // Split text into words
  const words = text.split(' ');

  // Minimum phrase length to check for duplicates (in words)
  const MIN_PHRASE_LENGTH = 3;

  // Maximum phrase length to check for duplicates (in words)
  const MAX_PHRASE_LENGTH = 10;

  // Find and remove duplicate phrases
  for (let phraseLength = MAX_PHRASE_LENGTH; phraseLength >= MIN_PHRASE_LENGTH; phraseLength--) {
    for (let i = 0; i <= words.length - phraseLength * 2; i++) {
      const phrase1 = words.slice(i, i + phraseLength).join(' ');

      for (let j = i + phraseLength; j <= words.length - phraseLength; j++) {
        const phrase2 = words.slice(j, j + phraseLength).join(' ');

        // If we found a duplicate phrase
        if (phrase1 === phrase2) {
          // Remove the duplicate phrase
          words.splice(j, phraseLength);
          // Adjust the index to account for the removed words
          j -= phraseLength;
        }
      }
    }
  }

  return words.join(' ');
}

/**
 * Fix punctuation and capitalization in transcript items
 * @param {Array} items - Transcript items to fix
 * @returns {Array} Fixed transcript items
 */
function fixPunctuationAndCapitalization(items) {
  if (!items || items.length === 0) return [];

  const fixedItems = [];

  for (const item of items) {
    const fixedItem = { ...item };
    let text = fixedItem.text;

    // Fix spacing after punctuation
    text = text.replace(/([.!?,;:])([^\s])/g, '$1 $2');

    // Remove punctuation at the beginning of text
    text = text.replace(/^\s*[.!?,;:]+\s*/, '');

    // Fix spacing around periods in numbers (e.g., "200 .000" -> "200.000")
    text = text.replace(/(\d+)\s+\.(\d+)/g, '$1.$2');

    // Fix incorrect spacing in numbers (e.g., "200. 000" -> "200.000")
    text = text.replace(/(\d+)\.(\s+)(\d+)/g, '$1.$3');

    // Fix abbreviations like "z. B." (German for "e.g.")
    text = text.replace(/([a-z])\.\s+([A-Z])\./g, '$1. $2.');

    // Fix spacing after abbreviations
    text = text.replace(/([a-z])\.\s*([a-z])/g, '$1. $2');

    // Fix repeated words (e.g., "I was was going" -> "I was going")
    text = text.replace(/\b(\w+)(\s+\1\b)+/gi, '$1');

    // Fix repeated phrases (e.g., "I was going I was going" -> "I was going")
    const words = text.split(' ');
    for (let i = 0; i < words.length - 3; i++) {
      const phrase = words.slice(i, i + 3).join(' ');
      for (let j = i + 3; j < words.length - 2; j++) {
        const comparePhrase = words.slice(j, j + 3).join(' ');
        if (phrase === comparePhrase && phrase.length > 10) {
          // Remove the duplicate phrase
          words.splice(j, 3);
          j -= 3;
        }
      }
    }
    text = words.join(' ');

    // Fix missing periods at the end of sentences
    if (!text.trim().match(/[.!?]$/)) {
      text = text.trim() + '.';
    }

    // Fix [Musik] or [Music] tags
    text = text.replace(/\[Musik\]|\[Music\]/g, '');

    // Ensure proper sentence capitalization
    text = capitalizeFirstLetterOfSentences(text);

    fixedItem.text = text;
    fixedItems.push(fixedItem);
  }

  return fixedItems;
}

/**
 * Capitalize the first letter of each sentence in a text
 * @param {string} text - Text to process
 * @returns {string} Text with capitalized sentences
 */
function capitalizeFirstLetterOfSentences(text) {
  if (!text) return '';

  // First, ensure there's a space after each punctuation mark
  let processedText = text.replace(/([.!?])([^\s])/g, '$1 $2');

  // Split the text into sentences more accurately
  // This regex matches sentence boundaries: period, exclamation, question mark followed by space
  const sentenceRegex = /([^.!?]+[.!?]+\s*)/g;
  const sentences = processedText.match(sentenceRegex) || [processedText];

  // Process each sentence
  const capitalizedSentences = sentences.map(sentence => {
    if (!sentence.trim()) return sentence;

    // Find the first letter in the sentence
    const firstLetterMatch = sentence.match(/[a-zA-Z]/);
    if (!firstLetterMatch) return sentence;

    const firstLetterIndex = firstLetterMatch.index;

    // Capitalize the first letter
    return sentence.substring(0, firstLetterIndex) +
           sentence.charAt(firstLetterIndex).toUpperCase() +
           sentence.substring(firstLetterIndex + 1);
  });

  // Join the sentences back together
  return capitalizedSentences.join('');
}

/**
 * Process a VTT file content directly
 * @param {string} vttContent - Raw VTT file content
 * @returns {Array} Processed transcript items
 */
function processVttContent(vttContent) {
  console.log('Parsing VTT content with improved non-English parser...');

  // Parse the VTT content
  const rawItems = parseVttContent(vttContent);

  // Process the raw items
  return processNonEnglishTranscript(rawItems);
}

/**
 * Parse VTT content into transcript items
 * @param {string} vttContent - Raw VTT file content
 * @returns {Array} Raw transcript items
 */
function parseVttContent(vttContent) {
  // Split the content into blocks (each subtitle entry)
  const blocks = vttContent.trim().split(/\n\s*\n/);
  const transcriptItems = [];

  console.log(`Found ${blocks.length} blocks in VTT file`);

  for (let i = 0; i < blocks.length; i++) {
    const block = blocks[i].trim();

    // Skip the WEBVTT header block
    if (block === 'WEBVTT' || block.startsWith('WEBVTT ') ||
        block.startsWith('Kind:') || block.startsWith('Language:')) {
      continue;
    }

    // Split the block into lines
    const lines = block.split('\n');

    // Find the line with the timestamp
    const timestampLineIndex = lines.findIndex(line => line.includes('-->'));

    if (timestampLineIndex === -1) {
      continue; // Skip blocks without timestamps
    }

    // Extract the timestamp
    const timestampLine = lines[timestampLineIndex];
    const times = timestampLine.split('-->').map(t => t.trim());

    // Parse the start time
    const startTime = parseVttTimestamp(times[0]);

    // Parse the end time, removing any alignment info
    const endTimePart = times[1].split(' ')[0];
    const endTime = parseVttTimestamp(endTimePart);

    // Extract the text (all lines after the timestamp line)
    const textLines = lines.slice(timestampLineIndex + 1);

    // Skip empty text blocks
    if (textLines.length === 0) {
      continue;
    }

    // Join all text lines with a space
    let text = textLines.join(' ');

    // Skip empty text after joining
    if (!text.trim()) {
      continue;
    }

    // Add to transcript items
    transcriptItems.push({
      start: startTime,
      end: endTime,
      text: text
    });
  }

  // Sort by start time to ensure proper sequence
  transcriptItems.sort((a, b) => a.start - b.start);

  console.log(`Parsed ${transcriptItems.length} transcript items from VTT file`);

  return transcriptItems;
}

/**
 * Parse VTT timestamp to seconds
 * @param {string} timestamp - VTT timestamp (HH:MM:SS.mmm)
 * @returns {number} Time in seconds
 */
function parseVttTimestamp(timestamp) {
  // Handle both HH:MM:SS.mmm and MM:SS.mmm formats
  const parts = timestamp.split(':');
  let hours = 0, minutes = 0, seconds = 0;

  if (parts.length === 3) {
    hours = parseInt(parts[0]);
    minutes = parseInt(parts[1]);
    seconds = parseFloat(parts[2]);
  } else if (parts.length === 2) {
    minutes = parseInt(parts[0]);
    seconds = parseFloat(parts[1]);
  }

  return hours * 3600 + minutes * 60 + seconds;
}

module.exports = {
  processNonEnglishTranscript,
  processVttContent
};
