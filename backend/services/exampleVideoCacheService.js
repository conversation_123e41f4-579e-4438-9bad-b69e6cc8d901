/**
 * Example Video Cache Service
 * 
 * Handles comprehensive caching for example videos including:
 * - Transcripts in all available languages
 * - Generated content (summaries, quotes, descriptions, social media, anki cards)
 * - Fast retrieval for optimal user experience
 */

const fs = require('fs').promises;
const path = require('path');
const transcriptService = require('./transcriptService');
const geminiService = require('./geminiService');

// Cache directories
const CACHE_DIR = path.join(__dirname, '../cache');
const EXAMPLE_CACHE_DIR = path.join(CACHE_DIR, 'examples');
const CONTENT_CACHE_DIR = path.join(EXAMPLE_CACHE_DIR, 'content');

// Example video IDs (matching backend/data/example-videos.json)
const EXAMPLE_VIDEO_IDS = ['TT81fe2IobI', 'arj7oStGLkU', 'Gv2fzC96Z40', 'UF8uR6Z6KLc', 'ZrN4bKKMlLU', 'KpVPST_P4W8', 'pqWUuYTcG-o'];

// Common languages to cache for each video
const LANGUAGES_TO_CACHE = [
  'en', 'en-US', 'en-orig',
  'es', 'fr', 'de', 'it', 'pt', 'ru', 'ja', 'ko', 'zh', 'zh-CN', 'zh-TW',
  'ar', 'hi', 'tr', 'pl', 'nl', 'sv', 'da', 'no', 'fi'
];

// Content types to cache
const CONTENT_TYPES = ['summary', 'quotes', 'description', 'social-media', 'anki-cards'];

/**
 * Initialize cache directories
 */
async function initializeCacheDirectories() {
  try {
    await fs.mkdir(EXAMPLE_CACHE_DIR, { recursive: true });
    await fs.mkdir(CONTENT_CACHE_DIR, { recursive: true });
    console.log('Cache directories initialized');
  } catch (error) {
    console.error('Error initializing cache directories:', error.message);
  }
}

/**
 * Get cached transcript for example video
 */
async function getCachedTranscript(videoId, language) {
  if (!EXAMPLE_VIDEO_IDS.includes(videoId)) {
    return null; // Only cache example videos
  }

  try {
    const cachePath = path.join(EXAMPLE_CACHE_DIR, `${videoId}_${language}.json`);
    const cacheData = await fs.readFile(cachePath, 'utf8');
    const transcript = JSON.parse(cacheData);
    console.log(`Using cached transcript for example video ${videoId} in ${language}`);
    return transcript;
  } catch (error) {
    return null; // Cache miss
  }
}

/**
 * Save transcript to example video cache
 */
async function saveTranscriptToCache(videoId, language, transcriptData) {
  if (!EXAMPLE_VIDEO_IDS.includes(videoId)) {
    return false; // Only cache example videos
  }

  try {
    await initializeCacheDirectories();
    const cachePath = path.join(EXAMPLE_CACHE_DIR, `${videoId}_${language}.json`);
    await fs.writeFile(cachePath, JSON.stringify(transcriptData, null, 2));
    console.log(`Cached transcript for example video ${videoId} in ${language}`);
    return true;
  } catch (error) {
    console.error(`Error caching transcript for ${videoId}:`, error.message);
    return false;
  }
}

/**
 * Get cached content for example video
 */
async function getCachedContent(videoId, language, contentType) {
  if (!EXAMPLE_VIDEO_IDS.includes(videoId)) {
    return null; // Only cache example videos
  }

  try {
    const cachePath = path.join(CONTENT_CACHE_DIR, `${videoId}_${language}_${contentType}.json`);
    const cacheData = await fs.readFile(cachePath, 'utf8');
    const content = JSON.parse(cacheData);
    console.log(`Using cached ${contentType} for example video ${videoId} in ${language}`);
    return content;
  } catch (error) {
    return null; // Cache miss
  }
}

/**
 * Save content to example video cache
 */
async function saveContentToCache(videoId, language, contentType, contentData) {
  if (!EXAMPLE_VIDEO_IDS.includes(videoId)) {
    return false; // Only cache example videos
  }

  try {
    await initializeCacheDirectories();
    const cachePath = path.join(CONTENT_CACHE_DIR, `${videoId}_${language}_${contentType}.json`);
    
    // Add cache metadata
    const cacheData = {
      ...contentData,
      cached: true,
      cachedAt: new Date().toISOString(),
      videoId,
      language,
      contentType
    };
    
    await fs.writeFile(cachePath, JSON.stringify(cacheData, null, 2));
    console.log(`Cached ${contentType} for example video ${videoId} in ${language}`);
    return true;
  } catch (error) {
    console.error(`Error caching ${contentType} for ${videoId}:`, error.message);
    return false;
  }
}

/**
 * Check if video is an example video
 */
function isExampleVideo(videoId) {
  return EXAMPLE_VIDEO_IDS.includes(videoId);
}

/**
 * Get available languages for a video (from YouTube API)
 */
async function getAvailableLanguages(videoId) {
  try {
    // This would typically call the YouTube API to get available caption languages
    // For now, we'll return the common languages and let the transcript service handle availability
    return LANGUAGES_TO_CACHE;
  } catch (error) {
    console.error(`Error getting available languages for ${videoId}:`, error.message);
    return ['en']; // Fallback to English
  }
}

/**
 * Pre-cache all transcripts for example videos
 */
async function preCacheAllTranscripts() {
  console.log('Starting pre-cache process for example video transcripts...');

  for (const videoId of EXAMPLE_VIDEO_IDS) {
    console.log(`Pre-caching transcripts for video ${videoId}...`);

    for (const language of LANGUAGES_TO_CACHE) {
      try {
        // Check if already cached
        const cached = await getCachedTranscript(videoId, language);
        if (cached) {
          console.log(`Transcript for ${videoId} in ${language} already cached`);
          continue;
        }

        // Fetch and cache transcript
        console.log(`Fetching transcript for ${videoId} in ${language}...`);
        const transcript = await transcriptService.getTranscript(videoId, language, false);

        if (transcript && transcript.transcript && transcript.transcript.length > 0) {
          await saveTranscriptToCache(videoId, language, transcript);
        } else {
          console.log(`No transcript available for ${videoId} in ${language}`);
        }

        // Add delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 1000));

      } catch (error) {
        console.error(`Error pre-caching transcript for ${videoId} in ${language}:`, error.message);
      }
    }
  }

  console.log('Pre-cache process for transcripts completed');
}

/**
 * Generate and cache content for a specific video and language
 */
async function generateAndCacheContent(videoId, language, contentType) {
  try {
    // Get transcript first
    const transcript = await getCachedTranscript(videoId, language);
    if (!transcript || !transcript.transcript || transcript.transcript.length === 0) {
      console.log(`No transcript available for ${videoId} in ${language}, skipping ${contentType}`);
      return null;
    }

    const transcriptText = transcript.transcript.map(item => item.text).join(' ').trim();
    let contentData = null;

    switch (contentType) {
      case 'summary':
        contentData = await generateSummary(transcriptText, videoId, language);
        break;
      case 'quotes':
        contentData = await generateQuotes(transcriptText, videoId, language);
        break;
      case 'description':
        contentData = await generateDescription(transcriptText, videoId, language);
        break;
      case 'social-media':
        contentData = await generateSocialMedia(transcriptText, videoId, language);
        break;
      case 'anki-cards':
        contentData = await generateAnkiCards(transcriptText, videoId, language);
        break;
      default:
        console.error(`Unknown content type: ${contentType}`);
        return null;
    }

    if (contentData) {
      await saveContentToCache(videoId, language, contentType, contentData);
      return contentData;
    }

    return null;
  } catch (error) {
    console.error(`Error generating ${contentType} for ${videoId} in ${language}:`, error.message);
    return null;
  }
}

/**
 * Pre-cache all content for example videos
 */
async function preCacheAllContent() {
  console.log('Starting pre-cache process for example video content...');

  for (const videoId of EXAMPLE_VIDEO_IDS) {
    console.log(`Pre-caching content for video ${videoId}...`);

    // Only cache content for English to start with (most common)
    const primaryLanguages = ['en'];

    for (const language of primaryLanguages) {
      // Check if transcript exists for this language
      const transcript = await getCachedTranscript(videoId, language);
      if (!transcript) {
        console.log(`No transcript cached for ${videoId} in ${language}, skipping content generation`);
        continue;
      }

      for (const contentType of CONTENT_TYPES) {
        try {
          // Check if already cached
          const cached = await getCachedContent(videoId, language, contentType);
          if (cached) {
            console.log(`${contentType} for ${videoId} in ${language} already cached`);
            continue;
          }

          console.log(`Generating ${contentType} for ${videoId} in ${language}...`);
          await generateAndCacheContent(videoId, language, contentType);

          // Add delay to avoid rate limiting
          await new Promise(resolve => setTimeout(resolve, 2000));

        } catch (error) {
          console.error(`Error pre-caching ${contentType} for ${videoId} in ${language}:`, error.message);
        }
      }
    }
  }

  console.log('Pre-cache process for content completed');
}

/**
 * Content generation helper functions
 */
async function generateSummary(transcriptText, videoId, language) {
  const prompt = `Please create a comprehensive summary of this video transcript. Focus on the main points, key insights, and important takeaways. Structure your response with clear headings and bullet points for easy reading.

**Instructions:**
- Create a detailed summary that captures the essence of the content
- Use clear, engaging language
- Organize information logically with headings
- Include key quotes or important statements
- Aim for 300-500 words

**Video Transcript:**
${transcriptText}`;

  const summary = await geminiService.generateContent(prompt, {
    temperature: 0.7,
    maxOutputTokens: 2048
  });

  return {
    success: true,
    videoId,
    language,
    summary,
    transcriptLength: transcriptText.split(' ').length,
    model: 'models/gemini-2.5-flash-lite-preview-06-17'
  };
}

async function generateQuotes(transcriptText, videoId, language) {
  const prompt = `Extract the most impactful and shareable quotes from this video transcript. Focus on quotes that are:
- Inspirational or motivational
- Thought-provoking insights
- Memorable one-liners
- Key takeaways or lessons

**Instructions:**
- Extract 6-10 of the best quotes
- Organize them by category (Inspirational, Insights, Lessons, etc.)
- Ensure quotes are accurate and taken directly from the transcript
- Include at least 2 quotes per category
- Format as clear, shareable content

**Video Transcript:**
${transcriptText}`;

  const quotes = await geminiService.generateContent(prompt, {
    temperature: 0.3,
    maxOutputTokens: 2048
  });

  return {
    success: true,
    videoId,
    language,
    quotes,
    transcriptLength: transcriptText.split(' ').length,
    model: 'models/gemini-2.5-flash-lite-preview-06-17'
  };
}

async function generateDescription(transcriptText, videoId, language) {
  const prompt = `Create an SEO-optimized YouTube description based on this video transcript. Include:

**Requirements:**
- Compelling opening hook (2-3 sentences)
- Key topics covered with timestamps
- Relevant hashtags
- Call-to-action for engagement
- 200-300 words total

**Format:**
- Start with an engaging description
- Add "🎯 Key Topics:" section with timestamps
- Include "📝 Timestamps:" section
- End with relevant hashtags

**Video Transcript:**
${transcriptText}`;

  const description = await geminiService.generateContent(prompt, {
    temperature: 0.6,
    maxOutputTokens: 2048
  });

  return {
    success: true,
    videoId,
    language,
    description,
    transcriptLength: transcriptText.split(' ').length,
    model: 'models/gemini-2.5-flash-lite-preview-06-17'
  };
}

async function generateSocialMedia(transcriptText, videoId, language) {
  const prompt = `Create engaging social media posts for different platforms based on this video content:

**Create posts for:**
1. **Twitter/X** (280 characters max)
2. **LinkedIn** (professional tone, 150-200 words)
3. **Instagram** (engaging caption with hashtags)
4. **Facebook** (conversational tone, 100-150 words)

**Requirements:**
- Extract key insights from the transcript
- Use platform-appropriate tone and format
- Include relevant hashtags
- Add call-to-action where appropriate
- Make content shareable and engaging

**Video Transcript:**
${transcriptText}`;

  const socialMediaPosts = await geminiService.generateContent(prompt, {
    temperature: 0.7,
    maxOutputTokens: 2048
  });

  return {
    success: true,
    videoId,
    language,
    socialMediaPosts,
    transcriptLength: transcriptText.split(' ').length,
    model: 'models/gemini-2.5-flash-lite-preview-06-17'
  };
}

async function generateAnkiCards(transcriptText, videoId, language) {
  // Calculate target card count based on transcript length
  const wordCount = transcriptText.split(' ').length;
  const targetCardCount = Math.min(20, Math.max(5, Math.floor(wordCount / 100)));

  const prompt = `Create ${targetCardCount} Anki flashcards based on this video transcript. Generate cards that test understanding of key concepts, facts, and insights.

**Card Distribution:**
- 70% Q&A format (question/answer)
- 20% Process/sequence cards
- 10% True/false cards

**Requirements:**
- Extract factual information only from the transcript
- Create clear, specific questions
- Provide accurate, concise answers
- Focus on key concepts and takeaways
- Ensure cards are educational and memorable

**Format as JSON array:**
[
  {
    "type": "qa",
    "question": "Question text",
    "answer": "Answer text"
  }
]

**Video Transcript:**
${transcriptText}`;

  const ankiCardsResponse = await geminiService.generateContent(prompt, {
    temperature: 0.3,
    maxOutputTokens: 4096
  });

  // Parse JSON response
  let ankiCards = [];
  try {
    const jsonMatch = ankiCardsResponse.match(/\[[\s\S]*\]/);
    if (jsonMatch) {
      ankiCards = JSON.parse(jsonMatch[0]);
    }
  } catch (error) {
    console.error('Error parsing Anki cards JSON:', error.message);
  }

  return {
    success: true,
    videoId,
    language,
    cards: ankiCards,
    metadata: {
      transcriptLength: wordCount,
      targetCardCount,
      actualCardCount: ankiCards.length,
      model: 'models/gemini-2.5-flash-lite-preview-06-17'
    }
  };
}

/**
 * Run complete pre-caching process
 */
async function runCompletePrecache() {
  console.log('🚀 Starting complete pre-cache process for example videos...');

  await initializeCacheDirectories();
  await preCacheAllTranscripts();
  await preCacheAllContent();

  console.log('✅ Complete pre-cache process finished!');
}

module.exports = {
  initializeCacheDirectories,
  getCachedTranscript,
  saveTranscriptToCache,
  getCachedContent,
  saveContentToCache,
  isExampleVideo,
  getAvailableLanguages,
  preCacheAllTranscripts,
  preCacheAllContent,
  generateAndCacheContent,
  runCompletePrecache,
  EXAMPLE_VIDEO_IDS,
  LANGUAGES_TO_CACHE,
  CONTENT_TYPES
};
