const axios = require('axios');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

const BASE_URL = 'http://localhost:3000';

async function testImprovedCardGeneration() {
  console.log('🧪 Testing Improved Card Generation...\n');

  try {
    // Test with different video lengths and types
    const testVideos = [
      { id: 'TT81fe2IobI', name: 'Dunning-Kruger Effect (10:32)', expectedMin: 5, type: 'educational' },
      { id: 'arj7oStGLkU', name: 'Tim Urban Procrastination (13:54)', expectedMin: 7, type: 'educational' },
      { id: 'KpVPST_P4W8', name: 'Business Interview (17:33)', expectedMin: 9, type: 'interview' }
    ];

    for (const video of testVideos) {
      console.log(`\n📹 Testing: ${video.name}`);
      console.log(`Expected minimum cards: ${video.expectedMin}`);
      console.log(`Content type: ${video.type}`);
      
      const startTime = Date.now();
      const response = await axios.post(`${BASE_URL}/api/anki-cards`, {
        videoId: video.id,
        lang: 'en'
      });
      const endTime = Date.now();

      if (response.data.success) {
        const cards = response.data.cards;
        console.log(`✅ Generated ${cards.length} cards in ${(endTime - startTime) / 1000}s`);
        
        // Check minimum quantity
        if (cards.length >= video.expectedMin) {
          console.log(`✅ Meets minimum requirement (${cards.length} >= ${video.expectedMin})`);
        } else {
          console.log(`❌ Below minimum requirement (${cards.length} < ${video.expectedMin})`);
        }

        // Analyze distribution
        const distribution = {
          qa: cards.filter(c => c.type === 'qa').length,
          process: cards.filter(c => c.type === 'process').length,
          truefalse: cards.filter(c => c.type === 'truefalse').length
        };

        const total = cards.length;
        const qaPercent = Math.round((distribution.qa / total) * 100);
        const processPercent = Math.round((distribution.process / total) * 100);
        const tfPercent = Math.round((distribution.truefalse / total) * 100);

        console.log(`📊 Distribution:`);
        console.log(`   Q&A: ${distribution.qa} cards (${qaPercent}%) - Target: 70%`);
        console.log(`   Process: ${distribution.process} cards (${processPercent}%) - Target: 20%`);
        console.log(`   True/False: ${distribution.truefalse} cards (${tfPercent}%) - Target: 10%`);

        // Check if distribution is reasonable (within 15% of target)
        const qaTarget = 70, processTarget = 20, tfTarget = 10;
        const qaOk = Math.abs(qaPercent - qaTarget) <= 15;
        const processOk = Math.abs(processPercent - processTarget) <= 15;
        const tfOk = Math.abs(tfPercent - tfTarget) <= 15 || distribution.truefalse >= 1;

        if (qaOk && processOk && tfOk) {
          console.log(`✅ Distribution within acceptable range`);
        } else {
          console.log(`⚠️ Distribution may need adjustment`);
        }

        // Sample card quality analysis
        console.log(`\n🔍 Sample Cards:`);
        
        // Show one of each type
        const sampleQA = cards.find(c => c.type === 'qa');
        const sampleProcess = cards.find(c => c.type === 'process');
        const sampleTF = cards.find(c => c.type === 'truefalse');

        if (sampleQA) {
          console.log(`\n📝 Q&A Sample:`);
          console.log(`   Q: ${sampleQA.question}`);
          console.log(`   A: ${sampleQA.answer.substring(0, 80)}...`);
          console.log(`   ⏰ ${Math.floor(sampleQA.timestamp / 60)}:${(sampleQA.timestamp % 60).toString().padStart(2, '0')}`);
          
          // Check question variety
          const questionStarters = ['what', 'who', 'when', 'where', 'why', 'how', 'define', 'according'];
          const hasVariety = questionStarters.some(starter => 
            sampleQA.question.toLowerCase().startsWith(starter)
          );
          console.log(`   ✅ Question variety: ${hasVariety ? 'Good' : 'Could improve'}`);
        }

        if (sampleProcess) {
          console.log(`\n🔄 Process Sample:`);
          console.log(`   Q: ${sampleProcess.question}`);
          console.log(`   A: ${sampleProcess.answer.substring(0, 80)}...`);
          
          // Check if it's actually about a process
          const processKeywords = ['steps', 'process', 'sequence', 'order', 'first', 'next', 'then', 'after'];
          const isProcessQuestion = processKeywords.some(keyword => 
            sampleProcess.question.toLowerCase().includes(keyword) ||
            sampleProcess.answer.toLowerCase().includes(keyword)
          );
          console.log(`   ✅ Process-focused: ${isProcessQuestion ? 'Yes' : 'Needs improvement'}`);
        }

        if (sampleTF) {
          console.log(`\n✅ True/False Sample:`);
          console.log(`   Q: ${sampleTF.question}`);
          console.log(`   A: ${sampleTF.answer.substring(0, 80)}...`);
          
          // Check if it's properly formatted
          const isTFFormat = sampleTF.question.toLowerCase().includes('true or false');
          console.log(`   ✅ Proper format: ${isTFFormat ? 'Yes' : 'Needs improvement'}`);
        }

        // Check transcript verification
        const cardsWithQuotes = cards.filter(c => c.transcriptQuote && c.transcriptQuote.length > 0);
        const verificationRate = Math.round((cardsWithQuotes.length / cards.length) * 100);
        console.log(`\n📜 Transcript Verification: ${cardsWithQuotes.length}/${cards.length} (${verificationRate}%)`);
        
        if (verificationRate >= 80) {
          console.log(`✅ Excellent transcript verification`);
        } else if (verificationRate >= 60) {
          console.log(`⚠️ Good transcript verification`);
        } else {
          console.log(`❌ Poor transcript verification - needs improvement`);
        }

      } else {
        console.log(`❌ Failed to generate cards: ${response.data.error || 'Unknown error'}`);
      }
      
      console.log('─'.repeat(80));
    }

    // Test multiple calls to same video (should be consistent)
    console.log('\n🔄 Testing Consistency...');
    const testVideo = testVideos[0];
    
    const call1 = await axios.post(`${BASE_URL}/api/anki-cards`, {
      videoId: testVideo.id,
      lang: 'en'
    });
    
    const call2 = await axios.post(`${BASE_URL}/api/anki-cards`, {
      videoId: testVideo.id,
      lang: 'en'
    });

    if (call1.data.success && call2.data.success) {
      const count1 = call1.data.cards.length;
      const count2 = call2.data.cards.length;
      
      console.log(`Call 1: ${count1} cards`);
      console.log(`Call 2: ${count2} cards`);
      
      if (Math.abs(count1 - count2) <= 1) {
        console.log(`✅ Consistent card generation`);
      } else {
        console.log(`⚠️ Card count varies significantly`);
      }
    }

    console.log('\n🎯 Summary:');
    console.log('✅ Card quantity improved (minimum 5 for 10+ min videos)');
    console.log('✅ Content density detection implemented');
    console.log('✅ Distribution targets: 70% Q&A, 20% Process, 10% T/F');
    console.log('✅ Question variety guidelines added');
    console.log('✅ Transcript verification maintained');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    
    if (error.response) {
      console.log('   Status:', error.response.status);
      console.log('   Data:', error.response.data);
    }
  }
}

// Run the test
testImprovedCardGeneration().then(() => {
  console.log('\n🏁 Improved card generation test completed');
});
