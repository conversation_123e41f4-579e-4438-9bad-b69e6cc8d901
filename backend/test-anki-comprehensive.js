const axios = require('axios');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

const BASE_URL = 'http://localhost:3000';

async function testAnkiCardsComprehensive() {
  console.log('🧪 Comprehensive Anki Flash Cards Testing...\n');

  const testCases = [
    {
      name: 'Educational Video (Dunning-Kruger Effect)',
      videoId: 'TT81fe2IobI',
      lang: 'en',
      expectedMinCards: 3,
      expectedMaxCards: 15
    },
    {
      name: 'Longer Educational Video (Tim Urban Procrastination)',
      videoId: 'arj7oStGLkU',
      lang: 'en',
      expectedMinCards: 5,
      expectedMaxCards: 20
    },
    {
      name: 'Business/Startup Video',
      videoId: 'EUxGPdbxz6g',
      lang: 'en',
      expectedMinCards: 5,
      expectedMaxCards: 25
    }
  ];

  let passedTests = 0;
  let totalTests = testCases.length;

  for (const testCase of testCases) {
    console.log(`\n📋 Testing: ${testCase.name}`);
    console.log(`   Video ID: ${testCase.videoId}`);
    console.log(`   Language: ${testCase.lang}`);
    console.log(`   Expected cards: ${testCase.expectedMinCards}-${testCase.expectedMaxCards}`);

    try {
      const startTime = Date.now();
      const response = await axios.post(`${BASE_URL}/api/anki-cards`, {
        videoId: testCase.videoId,
        lang: testCase.lang
      });
      const endTime = Date.now();

      if (response.data.success) {
        const cards = response.data.cards;
        const metadata = response.data.metadata;

        console.log(`   ✅ Generated ${cards.length} cards in ${(endTime - startTime) / 1000}s`);
        console.log(`   📊 Validation rate: ${metadata.validationRate}%`);
        console.log(`   🎯 Target vs Actual: ${metadata.targetCardCount} → ${metadata.actualCardCount}`);

        // Test card count is within expected range
        if (cards.length >= testCase.expectedMinCards && cards.length <= testCase.expectedMaxCards) {
          console.log(`   ✅ Card count within expected range`);
        } else {
          console.log(`   ⚠️ Card count outside expected range: ${cards.length}`);
        }

        // Test card structure validation
        let validStructure = true;
        const requiredFields = ['id', 'type', 'question', 'answer', 'timestamp', 'difficulty', 'tags'];
        
        for (const card of cards) {
          for (const field of requiredFields) {
            if (!(field in card)) {
              console.log(`   ❌ Missing field '${field}' in card ${card.id}`);
              validStructure = false;
            }
          }
          
          // Validate field types
          if (typeof card.question !== 'string' || card.question.trim().length === 0) {
            console.log(`   ❌ Invalid question in card ${card.id}`);
            validStructure = false;
          }
          
          if (typeof card.answer !== 'string' || card.answer.trim().length === 0) {
            console.log(`   ❌ Invalid answer in card ${card.id}`);
            validStructure = false;
          }
          
          if (typeof card.timestamp !== 'number' || card.timestamp < 0) {
            console.log(`   ❌ Invalid timestamp in card ${card.id}: ${card.timestamp}`);
            validStructure = false;
          }
          
          if (!['qa', 'cloze', 'process', 'truefalse'].includes(card.type)) {
            console.log(`   ❌ Invalid card type in card ${card.id}: ${card.type}`);
            validStructure = false;
          }
          
          if (!['beginner', 'intermediate', 'advanced'].includes(card.difficulty)) {
            console.log(`   ❌ Invalid difficulty in card ${card.id}: ${card.difficulty}`);
            validStructure = false;
          }
          
          if (!Array.isArray(card.tags) || card.tags.length === 0) {
            console.log(`   ❌ Invalid tags in card ${card.id}`);
            validStructure = false;
          }
        }

        if (validStructure) {
          console.log(`   ✅ All cards have valid structure`);
        }

        // Test card type distribution
        const typeDistribution = {};
        cards.forEach(card => {
          typeDistribution[card.type] = (typeDistribution[card.type] || 0) + 1;
        });
        
        console.log(`   📈 Type distribution:`, typeDistribution);

        // Test difficulty distribution
        const difficultyDistribution = {};
        cards.forEach(card => {
          difficultyDistribution[card.difficulty] = (difficultyDistribution[card.difficulty] || 0) + 1;
        });
        
        console.log(`   📊 Difficulty distribution:`, difficultyDistribution);

        // Test timestamp validation (should be within video duration)
        const maxTimestamp = Math.max(...cards.map(card => card.timestamp));
        const videoDurationSeconds = metadata.videoDurationSeconds;
        
        if (maxTimestamp <= videoDurationSeconds) {
          console.log(`   ✅ All timestamps within video duration (max: ${Math.floor(maxTimestamp / 60)}:${(maxTimestamp % 60).toString().padStart(2, '0')})`);
        } else {
          console.log(`   ❌ Some timestamps exceed video duration`);
          validStructure = false;
        }

        if (validStructure && cards.length >= testCase.expectedMinCards) {
          passedTests++;
          console.log(`   🎉 Test PASSED`);
        } else {
          console.log(`   ❌ Test FAILED`);
        }

      } else {
        console.log(`   ❌ API call failed:`, response.data);
      }

    } catch (error) {
      console.log(`   ❌ Test failed with error:`, error.message);
      if (error.response) {
        console.log(`   Status: ${error.response.status}`);
        console.log(`   Data:`, error.response.data);
      }
    }
  }

  console.log(`\n📊 Test Results: ${passedTests}/${totalTests} tests passed`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All tests passed! Anki Flash Cards feature is working correctly.');
  } else {
    console.log('⚠️ Some tests failed. Please review the issues above.');
  }

  // Test error handling
  console.log('\n🔍 Testing Error Handling...');
  
  try {
    const errorResponse = await axios.post(`${BASE_URL}/api/anki-cards`, {
      videoId: 'invalid-video-id',
      lang: 'en'
    });
    console.log('❌ Should have failed for invalid video ID');
  } catch (error) {
    if (error.response && error.response.status >= 400) {
      console.log('✅ Correctly handles invalid video ID');
    } else {
      console.log('❌ Unexpected error handling behavior');
    }
  }

  console.log('\n🏁 Comprehensive testing completed');
}

// Run the comprehensive test
testAnkiCardsComprehensive().catch(console.error);
