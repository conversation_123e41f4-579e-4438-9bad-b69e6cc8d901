<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YouTube IFrame API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
        }
        #player {
            width: 640px;
            height: 360px;
            margin: 20px 0;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .controls {
            margin: 20px 0;
        }
        button {
            margin: 5px;
            padding: 10px 15px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>Step 2: Minimal YouTube IFrame API Test</h1>
    <p>Testing video: <strong>arj7oStGLkU</strong> with proper YouTube IFrame Player API</p>
    
    <div id="player"></div>
    
    <div class="controls">
        <button onclick="seekTo(30)">Seek to 30s</button>
        <button onclick="seekTo(60)">Seek to 60s</button>
        <button onclick="seekTo(120)">Seek to 120s</button>
        <button onclick="playVideo()">Play</button>
        <button onclick="pauseVideo()">Pause</button>
        <button onclick="clearLog()">Clear Log</button>
    </div>
    
    <div class="log" id="log"></div>

    <script>
        let player;
        let playerReady = false;
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('log');
            logElement.innerHTML += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        // Load YouTube IFrame Player API
        function loadYouTubeAPI() {
            log('Loading YouTube IFrame Player API...');
            const tag = document.createElement('script');
            tag.src = 'https://www.youtube.com/iframe_api';
            const firstScriptTag = document.getElementsByTagName('script')[0];
            firstScriptTag.parentNode.insertBefore(tag, firstScriptTag);
        }
        
        // This function creates an <iframe> (and YouTube player) after the API code downloads
        function onYouTubeIframeAPIReady() {
            log('YouTube IFrame API Ready - Creating player...');
            
            player = new YT.Player('player', {
                height: '360',
                width: '640',
                videoId: 'arj7oStGLkU',
                playerVars: {
                    'playsinline': 1,
                    'rel': 0,
                    'modestbranding': 1
                },
                events: {
                    'onReady': onPlayerReady,
                    'onStateChange': onPlayerStateChange,
                    'onError': onPlayerError
                }
            });
        }
        
        // The API will call this function when the video player is ready
        function onPlayerReady(event) {
            log('✅ onReady: Player is ready!');
            playerReady = true;
            
            // Test basic functionality
            log(`Video duration: ${event.target.getDuration()} seconds`);
            log(`Video title: ${event.target.getVideoData().title}`);
        }
        
        // The API calls this function when the player's state changes
        function onPlayerStateChange(event) {
            const states = {
                '-1': 'UNSTARTED',
                '0': 'ENDED',
                '1': 'PLAYING',
                '2': 'PAUSED',
                '3': 'BUFFERING',
                '5': 'CUED'
            };
            
            const stateName = states[event.data] || `UNKNOWN(${event.data})`;
            log(`🔄 onStateChange: ${stateName} (${event.data})`);
            
            // Log current time when state changes
            if (player && typeof player.getCurrentTime === 'function') {
                const currentTime = player.getCurrentTime();
                log(`   Current time: ${currentTime.toFixed(2)}s`);
            }
        }
        
        // The API calls this function when an error occurs
        function onPlayerError(event) {
            const errors = {
                '2': 'Invalid video ID',
                '5': 'HTML5 player error',
                '100': 'Video not found or private',
                '101': 'Embedding not allowed by video owner',
                '150': 'Embedding not allowed by video owner'
            };
            
            const errorMsg = errors[event.data] || `Unknown error (${event.data})`;
            log(`❌ onError: ${errorMsg}`);
        }
        
        // Control functions
        function seekTo(seconds) {
            if (!playerReady) {
                log(`❌ Cannot seek: Player not ready yet`);
                return;
            }
            
            log(`🎯 Seeking to ${seconds} seconds...`);
            player.seekTo(seconds, true);
        }
        
        function playVideo() {
            if (!playerReady) {
                log(`❌ Cannot play: Player not ready yet`);
                return;
            }
            
            log(`▶️ Playing video...`);
            player.playVideo();
        }
        
        function pauseVideo() {
            if (!playerReady) {
                log(`❌ Cannot pause: Player not ready yet`);
                return;
            }
            
            log(`⏸️ Pausing video...`);
            player.pauseVideo();
        }
        
        // Start the test
        log('🧪 Starting YouTube IFrame API test...');
        loadYouTubeAPI();
        
        // Make functions available globally for debugging
        window.player = player;
        window.log = log;
    </script>
</body>
</html>
