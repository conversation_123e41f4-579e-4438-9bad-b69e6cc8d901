// Test script to verify Anki <PERSON>V format
// Run this in the browser console when on the Anki Flash Cards tab

function testAnkiCSVFormat() {
  console.log('🧪 Testing Anki CSV Format...');
  
  // Sample card data for testing
  const sampleCards = [
    {
      id: 'card_1',
      type: 'qa',
      question: 'What is photosynthesis?',
      answer: 'The process by which plants convert sunlight, carbon dioxide, and water into glucose and oxygen',
      timestamp: 225, // 3:45
      difficulty: 'beginner',
      tags: ['biology', 'photosynthesis', 'plants', 'science'],
      context: 'Discussion of plant biology'
    },
    {
      id: 'card_2',
      type: 'cloze',
      question: 'Photosynthesis occurs in the {{c1::chloroplasts}} of plant cells',
      answer: 'chloroplasts',
      timestamp: 312, // 5:12
      difficulty: 'intermediate',
      tags: ['biology', 'photosynthesis', 'plants', 'cloze'],
      context: 'Cell structure explanation'
    },
    {
      id: 'card_3',
      type: 'truefalse',
      question: 'True or False: Photosynthesis only happens during the day',
      answer: 'True - photosynthesis requires sunlight to occur',
      timestamp: 513, // 8:33
      difficulty: 'beginner',
      tags: ['biology', 'photosynthesis', 'true-false'],
      context: 'Light requirements for photosynthesis'
    }
  ];

  // Test the CSV generation
  const csvContent = generateTestAnkiCSV(sampleCards, 'TT81fe2IobI');
  
  console.log('📄 Generated CSV Content:');
  console.log('─'.repeat(80));
  console.log(csvContent);
  console.log('─'.repeat(80));
  
  // Validate the format
  const lines = csvContent.split('\n');
  const header = lines[0];
  const expectedHeader = '"Front","Back","Tags","Source_Video","Timestamp","Video_URL","Card_Type","Difficulty","Created_Date","User_Notes"';
  
  console.log('\n🔍 Format Validation:');
  console.log(`✅ Header correct: ${header === expectedHeader}`);
  console.log(`✅ Number of data rows: ${lines.length - 2} (excluding header and empty line)`);
  
  // Check each row has correct number of columns
  for (let i = 1; i < lines.length - 1; i++) {
    if (lines[i].trim()) {
      const columns = parseCSVRow(lines[i]);
      console.log(`✅ Row ${i} has ${columns.length} columns (expected: 10)`);
      
      if (columns.length === 10) {
        console.log(`   Front: ${columns[0].substring(0, 50)}...`);
        console.log(`   Back: ${columns[1].substring(0, 50)}...`);
        console.log(`   Tags: ${columns[2]}`);
        console.log(`   Source: ${columns[3]}`);
        console.log(`   Timestamp: ${columns[4]}`);
        console.log(`   Video URL: ${columns[5]}`);
        console.log(`   Card Type: ${columns[6]}`);
        console.log(`   Difficulty: ${columns[7]}`);
        console.log(`   Created Date: ${columns[8]}`);
        console.log(`   User Notes: ${columns[9]}`);
      }
    }
  }
  
  // Test download
  console.log('\n📥 Testing download...');
  downloadTestFile(csvContent, 'test-anki-cards.csv', 'text/csv');
  console.log('✅ Download initiated');
  
  console.log('\n🎉 CSV format test completed!');
  console.log('\nTo test in Anki:');
  console.log('1. Open Anki');
  console.log('2. Go to File > Import');
  console.log('3. Select the downloaded CSV file');
  console.log('4. Make sure field mapping is correct');
  console.log('5. Import the cards');
}

function generateTestAnkiCSV(cards, videoId) {
  // Anki CSV format with proper headers for import
  const header = '"Front","Back","Tags","Source_Video","Timestamp","Video_URL","Card_Type","Difficulty","Created_Date","User_Notes"\n';
  
  const videoTitle = `Test Video ${videoId}`;
  const currentDate = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
  
  const rows = cards.map(card => {
    // Escape quotes in content
    const front = `"${card.question.replace(/"/g, '""')}"`;
    const back = `"${card.answer.replace(/"/g, '""')}"`;
    
    // Create tags string with spaces (Anki format)
    const tags = `"${card.tags.join(' ')} ${card.type} ${card.difficulty}"`;
    
    // Source video title
    const sourceVideo = `"${videoTitle.replace(/"/g, '""')}"`;
    
    // Timestamp in MM:SS format
    const timestamp = `"${formatTestTime(card.timestamp)}"`;
    
    // Video URL with timestamp
    const videoUrl = `"https://youtube.com/watch?v=${videoId}&t=${Math.floor(card.timestamp)}s"`;
    
    // Card type mapping
    const cardTypeMap = {
      'qa': 'basic_qa',
      'cloze': 'cloze',
      'process': 'process',
      'truefalse': 'true_false'
    };
    const cardType = `"${cardTypeMap[card.type] || 'basic_qa'}"`;
    
    // Difficulty
    const difficulty = `"${card.difficulty}"`;
    
    // Created date
    const createdDate = `"${currentDate}"`;
    
    // User notes (empty for now)
    const userNotes = '""';
    
    return `${front},${back},${tags},${sourceVideo},${timestamp},${videoUrl},${cardType},${difficulty},${createdDate},${userNotes}`;
  }).join('\n');
  
  return header + rows;
}

function formatTestTime(seconds) {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
}

function parseCSVRow(row) {
  const result = [];
  let current = '';
  let inQuotes = false;
  
  for (let i = 0; i < row.length; i++) {
    const char = row[i];
    
    if (char === '"') {
      if (inQuotes && row[i + 1] === '"') {
        // Escaped quote
        current += '"';
        i++; // Skip next quote
      } else {
        // Toggle quote state
        inQuotes = !inQuotes;
      }
    } else if (char === ',' && !inQuotes) {
      // End of field
      result.push(current);
      current = '';
    } else {
      current += char;
    }
  }
  
  // Add the last field
  result.push(current);
  
  return result;
}

function downloadTestFile(content, filename, mimeType) {
  const blob = new Blob([content], { type: mimeType });
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
}

// Instructions
console.log(`
🧪 Anki CSV Format Test Script Loaded!

To run the test:
testAnkiCSVFormat()

This will:
1. Generate a sample CSV with the correct Anki format
2. Validate the format structure
3. Download a test file for Anki import testing
`);

// Auto-run the test
console.log('🚀 Auto-running CSV format test...');
testAnkiCSVFormat();
