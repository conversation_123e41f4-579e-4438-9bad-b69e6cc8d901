#!/bin/bash

# Exit on error
set -e

# Print commands
set -x

# Explicitly tell <PERSON><PERSON> this is NOT a Rust project
echo "This is a Node.js project, not a Rust project"

# Check if we're in the right directory
pwd
ls -la

# Determine which service to build based on environment variable
if [ "$RENDER_SERVICE_TYPE" = "web" ]; then
  echo "Building backend service..."
  cd backend
  npm install
  echo "Backend build completed successfully!"
elif [ "$RENDER_SERVICE_TYPE" = "static" ]; then
  echo "Building frontend service..."
  cd frontend/youtube-transcribe
  npm install
  npm run build
  echo "Frontend build completed successfully!"
else
  # Default behavior if RENDER_SERVICE_TYPE is not set
  echo "RENDER_SERVICE_TYPE not set, defaulting to backend build..."
  cd backend
  npm install
  echo "Backend build completed successfully!"
fi
