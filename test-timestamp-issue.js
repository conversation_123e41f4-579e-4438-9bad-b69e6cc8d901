const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function testTimestampIssue() {
  console.log('🧪 Testing YouTube Description Timestamp Issue');
  console.log('=' .repeat(60));

  try {
    // Test with the 17-minute video that has timestamp issues
    const testVideoId = 'arj7oStGLkU'; // Tim Urban procrastination video
    const testLanguage = 'en';

    console.log(`Testing video: ${testVideoId} (Tim Urban - Procrastination)`);
    console.log(`Language: ${testLanguage}\n`);

    // First, let's check the actual video duration from the transcript
    console.log('📊 Checking actual video duration from transcript...');
    
    const transcriptResponse = await axios.get(`${BASE_URL}/api/transcript/${testVideoId}?lang=${testLanguage}`);
    
    if (transcriptResponse.data && transcriptResponse.data.transcript) {
      const transcript = transcriptResponse.data.transcript;
      const lastItem = transcript[transcript.length - 1];
      const videoDurationSeconds = lastItem.end;
      const videoDurationFormatted = formatTime(videoDurationSeconds);
      
      console.log(`   Total transcript items: ${transcript.length}`);
      console.log(`   Video duration: ${videoDurationSeconds} seconds (${videoDurationFormatted})`);
      console.log(`   Last timestamp: ${lastItem.formattedEnd}`);
      console.log(`   Expected max timestamp: ${videoDurationFormatted}\n`);
    }

    console.log('🎬 Generating YouTube Description with current implementation...');
    console.log('   This should demonstrate the timestamp issue...\n');

    const requestBody = {
      videoId: testVideoId,
      videoTitle: 'Inside the mind of a master procrastinator | Tim Urban',
      keywords: 'procrastination, productivity, psychology',
      ctaGoal: 'Subscribe for more psychology content',
      lang: testLanguage
    };

    const startTime = Date.now();
    const response = await axios.post(`${BASE_URL}/api/description`, requestBody);
    const endTime = Date.now();

    if (response.data.success) {
      console.log('✅ Description generated successfully!');
      console.log(`   Generation time: ${(endTime - startTime) / 1000}s`);
      console.log(`   Description length: ${response.data.description.length} characters`);
      console.log('\n📝 Generated YouTube Description:');
      console.log('─'.repeat(80));
      console.log(response.data.description);
      console.log('─'.repeat(80));

      // Analyze timestamps for issues
      console.log('\n🔍 Analyzing timestamps for issues...');
      const descriptionText = response.data.description;
      const timestampMatches = descriptionText.match(/(\d{1,2}:\d{2})/g);
      
      if (timestampMatches) {
        console.log(`   Found ${timestampMatches.length} timestamps:`);
        
        timestampMatches.forEach((timestamp, index) => {
          const [minutes, seconds] = timestamp.split(':').map(Number);
          const totalSeconds = minutes * 60 + seconds;
          const isValid = totalSeconds <= videoDurationSeconds;
          const status = isValid ? '✅' : '❌ INVALID';
          
          console.log(`   ${index + 1}. ${timestamp} (${totalSeconds}s) ${status}`);
        });
        
        const invalidTimestamps = timestampMatches.filter(timestamp => {
          const [minutes, seconds] = timestamp.split(':').map(Number);
          const totalSeconds = minutes * 60 + seconds;
          return totalSeconds > videoDurationSeconds;
        });
        
        if (invalidTimestamps.length > 0) {
          console.log(`\n❌ ISSUE CONFIRMED: ${invalidTimestamps.length} invalid timestamps found!`);
          console.log(`   Invalid timestamps: ${invalidTimestamps.join(', ')}`);
          console.log(`   These exceed the video duration of ${videoDurationFormatted}`);
        } else {
          console.log('\n✅ All timestamps are valid!');
        }
      } else {
        console.log('   No timestamps found in description');
      }

    } else {
      console.log('❌ Description generation failed');
      console.log('   Response:', response.data);
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('   Response status:', error.response.status);
      console.error('   Response data:', error.response.data);
    }
  }
}

function formatTime(seconds) {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  if (hours > 0) {
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  } else {
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }
}

// Run the test
testTimestampIssue();
