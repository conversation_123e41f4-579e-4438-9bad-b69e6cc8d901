// Test script to verify export functionality
// Run this in the browser console when on the Anki Flash Cards tab

function testExportFunctionality() {
  console.log('🧪 Testing Export Functionality...');
  
  // Check if we're on the right page
  if (!window.location.href.includes('localhost:5175')) {
    console.error('❌ Please navigate to http://localhost:5175 first');
    return;
  }
  
  // Wait for Vue app to be available
  setTimeout(() => {
    // Try to access the Vue component
    const app = document.querySelector('#app').__vue__;
    if (!app) {
      console.error('❌ Vue app not found');
      return;
    }
    
    console.log('✅ Vue app found');
    
    // Check if we have Anki cards
    const ankiCards = app.$data?.ankiCards || [];
    console.log(`📊 Found ${ankiCards.length} Anki cards`);
    
    if (ankiCards.length === 0) {
      console.warn('⚠️ No Anki cards found. Please:');
      console.warn('1. Select a video');
      console.warn('2. Go to Anki Flash Cards tab');
      console.warn('3. Wait for cards to load');
      console.warn('4. Run this test again');
      return;
    }
    
    // Check selected cards
    const selectedCards = ankiCards.filter(card => card.selected);
    console.log(`✅ Found ${selectedCards.length} selected cards out of ${ankiCards.length} total`);
    
    if (selectedCards.length === 0) {
      console.warn('⚠️ No cards are selected. Selecting all cards...');
      ankiCards.forEach(card => card.selected = true);
      const newSelectedCount = ankiCards.filter(card => card.selected).length;
      console.log(`✅ Selected ${newSelectedCount} cards`);
    }
    
    // Test CSV export
    console.log('🧪 Testing CSV export...');
    try {
      const csvContent = generateTestCSV(selectedCards);
      console.log('✅ CSV generation successful');
      console.log('📄 CSV preview (first 200 chars):', csvContent.substring(0, 200) + '...');
      
      // Test download
      downloadTestFile(csvContent, 'test-anki-cards.csv', 'text/csv');
      console.log('✅ CSV download initiated');
      
    } catch (error) {
      console.error('❌ CSV export failed:', error);
    }
    
    // Test Text export
    console.log('🧪 Testing Text export...');
    try {
      const textContent = generateTestText(selectedCards);
      console.log('✅ Text generation successful');
      console.log('📝 Text preview (first 200 chars):', textContent.substring(0, 200) + '...');
      
    } catch (error) {
      console.error('❌ Text export failed:', error);
    }
    
    // Test JSON export
    console.log('🧪 Testing JSON export...');
    try {
      const jsonContent = JSON.stringify({
        metadata: {
          testExport: true,
          exportDate: new Date().toISOString(),
          totalCards: selectedCards.length
        },
        cards: selectedCards
      }, null, 2);
      console.log('✅ JSON generation successful');
      console.log('🔧 JSON preview (first 200 chars):', jsonContent.substring(0, 200) + '...');
      
    } catch (error) {
      console.error('❌ JSON export failed:', error);
    }
    
    console.log('🎉 Export functionality test completed!');
    
  }, 1000);
}

function generateTestCSV(cards) {
  const header = 'Front,Back,Tags,Source,Timestamp\n';
  const rows = cards.map(card => {
    const front = `"${card.question.replace(/"/g, '""')}"`;
    const back = `"${card.answer.replace(/"/g, '""')}"`;
    const tags = `"${card.tags.join(' ')} ${card.type} ${card.difficulty}"`;
    const source = `"Test Export"`;
    const timestamp = `"${formatTestTime(card.timestamp)}"`;
    return `${front},${back},${tags},${source},${timestamp}`;
  }).join('\n');
  
  return header + rows;
}

function generateTestText(cards) {
  const header = `Test Anki Flash Cards Export\n`;
  const separator = '='.repeat(60) + '\n';
  const timestamp = `Generated: ${new Date().toLocaleString()}\n`;
  const cardCount = `Total Cards: ${cards.length}\n\n`;
  
  const cardContent = cards.map((card, index) => {
    const cardHeader = `Card ${index + 1} [${card.type.toUpperCase()}] - ${card.difficulty}\n`;
    const cardSeparator = '-'.repeat(40) + '\n';
    const question = `Q: ${card.question}\n`;
    const answer = `A: ${card.answer}\n`;
    const timestamp = `⏰ ${formatTestTime(card.timestamp)}\n`;
    const tags = `🏷️ ${card.tags.join(', ')}\n`;
    
    return cardHeader + cardSeparator + question + answer + timestamp + tags + '\n';
  }).join('');
  
  return header + separator + timestamp + cardCount + cardContent;
}

function formatTestTime(seconds) {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
}

function downloadTestFile(content, filename, mimeType) {
  const blob = new Blob([content], { type: mimeType });
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
}

// Instructions
console.log(`
🧪 Export Functionality Test Script Loaded!

To run the test:
1. Navigate to http://localhost:5175
2. Select a video (e.g., Dunning-Kruger Effect)
3. Click on "Anki Flash Cards" tab
4. Wait for cards to load
5. Run: testExportFunctionality()

The test will check if cards are selected and test all export formats.
`);

// Auto-run if we're already on the right page
if (typeof window !== 'undefined' && window.location.href.includes('localhost:5175')) {
  console.log('🚀 Auto-running test in 2 seconds...');
  setTimeout(testExportFunctionality, 2000);
}
