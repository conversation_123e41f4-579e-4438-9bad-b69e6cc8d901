# YouTube Subtitle Viewer & Downloader

A web application that allows users to view and download subtitles from YouTube videos in various formats (TXT, SRT, VTT).

[![GitLab](https://img.shields.io/badge/GitLab-Repository-orange)](https://gitlab.com/LaurinWirth/youtubetranscriptgeneratior)
[![Render](https://img.shields.io/badge/Render-Deployed-green)](https://youtube-transcribe-frontend.onrender.com)

## Features

- Enter a YouTube video URL to fetch video information
- View available subtitle tracks for the video
- View subtitles directly in the browser with clickable timestamps
- Download subtitles in different formats (TXT, SRT, VTT)
- Robust fallback system for fetching transcripts using multiple methods
- Prioritizes user-added subtitles when available
- Responsive design for desktop and mobile

## Tech Stack

- **Frontend**: Vue.js with Vite
- **Backend**: Node.js with Express
- **APIs**: YouTube Data API v3
- **Deployment**: Render.com
- **CI/CD**: GitLab CI/CD

## Prerequisites

- Node.js (v14 or higher)
- npm (v6 or higher)
- YouTube Data API key
- yt-dlp (optional, will be installed automatically during setup)

## Setup Instructions

### 1. Clone the repository

```bash
git clone https://gitlab.com/LaurinWirth/youtubetranscriptgeneratior.git
cd youtubetranscriptgeneratior
```

### 2. Backend Setup

```bash
cd backend
npm install
```

Create a `.env` file in the backend directory:

```
YOUTUBE_API_KEY=your_youtube_api_key_here
PORT=3000
NODE_ENV=development
```

Replace `your_youtube_api_key_here` with your actual YouTube API key.

### 3. Frontend Setup

```bash
cd ../frontend/youtube-transcribe
npm install
```

### 4. Running the Application

Start the backend server:

```bash
cd backend
npm run dev
```

Start the frontend development server:

```bash
cd ../frontend/youtube-transcribe
npm run dev
```

The application should now be running at `http://localhost:5173`

## Transcript Fallback System

This application uses a robust fallback system to fetch YouTube video transcripts using multiple free methods:

1. **Primary Method**: Uses the `youtube-transcript` package to fetch transcripts directly from YouTube
2. **Fallback 1**: Uses `yt-dlp` to download subtitles, which often includes user-added subtitles not available through the API
3. **Fallback 2**: Uses web scraping with Cheerio to extract transcript data from the YouTube page

The system automatically tries each method in sequence until it finds a transcript, ensuring maximum availability without relying on paid services.

### Manual yt-dlp Installation

The application will attempt to install yt-dlp automatically during setup. If automatic installation fails, you can install it manually:

- **macOS**: `brew install yt-dlp`
- **Linux**: `sudo apt-get install yt-dlp` or `pip install yt-dlp`
- **Windows**: Download from [yt-dlp GitHub releases](https://github.com/yt-dlp/yt-dlp/releases) and add to PATH

## Getting a YouTube API Key

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project
3. Enable the YouTube Data API v3
4. Create credentials (API key)
5. Restrict the API key to the YouTube Data API v3

## Deployment

This project is set up for automatic deployment to Render.com when changes are pushed to the main branch of the GitLab repository.

### Setting up Render.com Deployment

1. Create a Render.com account at [render.com](https://render.com)
2. Connect your GitLab account to Render.com
3. Create a new "Blueprint" on Render.com and select the GitLab repository
4. Render will automatically detect the `render.yaml` file and set up the services
5. Add your YouTube API key as an environment variable in the Render.com dashboard
6. Deploy the application

### Manual Deployment

You can also deploy the application manually:

1. Build the frontend:
   ```bash
   cd frontend/youtube-transcribe
   npm run build
   ```

2. Deploy the backend to a Node.js hosting service
3. Deploy the frontend static files to any static hosting service

## License

MIT
