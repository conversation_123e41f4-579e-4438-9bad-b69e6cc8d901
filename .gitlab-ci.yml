stages:
  - test
  - deploy

# Cache dependencies for faster builds
cache:
  paths:
    - backend/node_modules/
    - frontend/youtube-transcribe/node_modules/

# Test stage
test:
  stage: test
  image: node:18
  script:
    # Install backend dependencies and run tests
    - cd backend
    - npm install
    - npm test || true  # Continue even if tests fail for now
    
    # Install frontend dependencies and run tests
    - cd ../frontend/youtube-transcribe
    - npm install
    - npm run build  # Make sure build works

# Deploy to Render.com
deploy:
  stage: deploy
  image: alpine:latest
  script:
    - echo "Deploying to Render.com via webhook..."
    # This will be triggered automatically by Render.com when changes are pushed to the main branch
  only:
    - main  # Only deploy when changes are pushed to the main branch
