<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Transcript Service Integration Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .warning { background-color: #fff3cd; color: #856404; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        .progress {
            background-color: #e9ecef;
            border-radius: 4px;
            height: 20px;
            margin: 10px 0;
        }
        .progress-bar {
            background-color: #007bff;
            height: 100%;
            border-radius: 4px;
            transition: width 0.3s ease;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🧪 YouTube Transcript Service Integration Test</h1>
    
    <div class="test-container">
        <h2>Test Configuration</h2>
        <label>
            Test Video ID: 
            <input type="text" id="videoId" value="pqWUuYTcG-o" style="width: 200px; padding: 5px;">
        </label>
        <br><br>
        <label>
            Language: 
            <select id="language" style="padding: 5px;">
                <option value="en">English</option>
                <option value="es">Spanish</option>
                <option value="fr">French</option>
                <option value="de">German</option>
            </select>
        </label>
        <br><br>
        <button onclick="runFullTest()">🚀 Run Full Test</button>
        <button onclick="testCORSOnly()">🌐 Test CORS Only</button>
        <button onclick="testBackendOnly()">🖥️ Test Backend Only</button>
        <button onclick="clearResults()">🗑️ Clear Results</button>
    </div>

    <div class="test-container">
        <h2>Test Progress</h2>
        <div id="progress-container" style="display: none;">
            <div class="progress">
                <div class="progress-bar" id="progress-bar" style="width: 0%;"></div>
            </div>
            <div id="progress-text">Initializing...</div>
        </div>
    </div>

    <div class="test-container">
        <h2>Test Results</h2>
        <div id="results"></div>
    </div>

    <div class="test-container">
        <h2>Transcript Preview</h2>
        <div id="transcript-preview"></div>
    </div>

    <script type="module">
        // Import the transcript service
        import { getTranscript, getAvailableLanguages } from './frontend/youtube-transcribe/src/services/transcriptService.js';

        let currentTest = null;

        window.runFullTest = async function() {
            const videoId = document.getElementById('videoId').value;
            const language = document.getElementById('language').value;
            
            if (!videoId) {
                addResult('error', 'Please enter a video ID');
                return;
            }

            showProgress(true);
            updateProgress(0, 'Starting full test...');
            
            try {
                addResult('info', `Testing video: ${videoId} (${language})`);
                
                // Test 1: Language Detection
                updateProgress(20, 'Testing language detection...');
                const languages = await getAvailableLanguages(videoId);
                if (languages && languages.length > 0) {
                    addResult('success', `✅ Found ${languages.length} available languages`);
                    addResult('info', `Languages: ${languages.map(l => `${l.code} (${l.name})`).join(', ')}`);
                } else {
                    addResult('warning', '⚠️ No languages detected via client-side methods');
                }

                // Test 2: Full Transcript Fetch
                updateProgress(40, 'Testing transcript fetch with fallback chain...');
                const transcript = await getTranscript(videoId, language, {
                    onProgress: (progress) => {
                        const stepProgress = {
                            'cors-proxy': 60,
                            'youtube-api': 80,
                            'backend-api': 90
                        };
                        updateProgress(stepProgress[progress.step] || 50, `${progress.step}: ${progress.status}`);
                        addResult('info', `📊 ${progress.step}: ${progress.status} ${progress.error ? `(${progress.error})` : ''}`);
                    },
                    retryCount: 1
                });

                updateProgress(100, 'Test completed!');

                if (transcript && transcript.transcript && transcript.transcript.length > 0) {
                    addResult('success', `✅ SUCCESS: Got ${transcript.transcript.length} transcript segments`);
                    addResult('success', `📡 Source: ${transcript.source}`);
                    addResult('success', `🌐 Language: ${transcript.language}`);
                    
                    // Show transcript preview
                    showTranscriptPreview(transcript.transcript.slice(0, 5));
                } else {
                    addResult('error', '❌ FAILED: No transcript data received');
                }

            } catch (error) {
                updateProgress(100, 'Test failed');
                addResult('error', `❌ ERROR: ${error.message}`);
            } finally {
                setTimeout(() => showProgress(false), 2000);
            }
        };

        window.testCORSOnly = async function() {
            const videoId = document.getElementById('videoId').value;
            const language = document.getElementById('language').value;
            
            showProgress(true);
            updateProgress(0, 'Testing CORS proxies only...');
            
            try {
                const transcript = await getTranscript(videoId, language, {
                    skipBackend: true,
                    onProgress: (progress) => {
                        updateProgress(50, `${progress.step}: ${progress.status}`);
                        addResult('info', `📊 ${progress.step}: ${progress.status}`);
                    }
                });

                updateProgress(100, 'CORS test completed');

                if (transcript && transcript.transcript && transcript.transcript.length > 0) {
                    addResult('success', `✅ CORS SUCCESS: ${transcript.transcript.length} segments from ${transcript.source}`);
                } else {
                    addResult('warning', '⚠️ CORS methods failed, would fallback to backend');
                }

            } catch (error) {
                addResult('error', `❌ CORS ERROR: ${error.message}`);
            } finally {
                setTimeout(() => showProgress(false), 1000);
            }
        };

        window.testBackendOnly = async function() {
            const videoId = document.getElementById('videoId').value;
            const language = document.getElementById('language').value;
            
            showProgress(true);
            updateProgress(0, 'Testing backend API only...');
            
            try {
                const transcript = await getTranscript(videoId, language, {
                    skipClientSide: true,
                    onProgress: (progress) => {
                        updateProgress(50, `${progress.step}: ${progress.status}`);
                        addResult('info', `📊 ${progress.step}: ${progress.status}`);
                    }
                });

                updateProgress(100, 'Backend test completed');

                if (transcript && transcript.transcript && transcript.transcript.length > 0) {
                    addResult('success', `✅ BACKEND SUCCESS: ${transcript.transcript.length} segments`);
                } else {
                    addResult('error', '❌ Backend API failed');
                }

            } catch (error) {
                addResult('error', `❌ BACKEND ERROR: ${error.message}`);
            } finally {
                setTimeout(() => showProgress(false), 1000);
            }
        };

        window.clearResults = function() {
            document.getElementById('results').innerHTML = '';
            document.getElementById('transcript-preview').innerHTML = '';
            showProgress(false);
        };

        function addResult(type, message) {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }

        function showProgress(show) {
            document.getElementById('progress-container').style.display = show ? 'block' : 'none';
        }

        function updateProgress(percent, text) {
            document.getElementById('progress-bar').style.width = `${percent}%`;
            document.getElementById('progress-text').textContent = text;
        }

        function showTranscriptPreview(segments) {
            const preview = document.getElementById('transcript-preview');
            const html = segments.map(segment => 
                `<div style="margin-bottom: 10px;">
                    <strong>${segment.timestamp}</strong>: ${segment.text}
                </div>`
            ).join('');
            preview.innerHTML = `<h3>First 5 segments:</h3>${html}`;
        }

        // Initialize
        addResult('info', '🚀 Transcript Service Integration Test Ready');
        addResult('info', 'Click "Run Full Test" to test the complete fallback chain');
    </script>
</body>
</html>
