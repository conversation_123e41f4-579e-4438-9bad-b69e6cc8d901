/**
 * Test script for the new transcript service
 * Run this in the browser console to test the client-side transcript fetching
 */

// Import the transcript service (this would work in a module context)
// For browser console testing, you'd need to copy the service code or use it through the app

const testVideos = [
  {
    id: 'pqWUuYTcG-o',
    name: '<PERSON> Stanford Commencement',
    expectedLanguages: ['en']
  },
  {
    id: 'dQw4w9WgXcQ',
    name: '<PERSON> - Never Gonna Give You Up',
    expectedLanguages: ['en']
  },
  {
    id: 'jNQXAC9IVRw',
    name: 'Me at the zoo (first YouTube video)',
    expectedLanguages: ['en']
  }
];

async function testTranscriptService() {
  console.log('🧪 Starting transcript service tests...');
  
  for (const video of testVideos) {
    console.log(`\n📹 Testing video: ${video.name} (${video.id})`);
    
    try {
      // Test 1: Client-side only (skip backend)
      console.log('  🔄 Testing client-side methods only...');
      const clientResult = await getTranscript(video.id, 'en', {
        skipBackend: true,
        onProgress: (progress) => {
          console.log(`    📊 Progress: ${progress.step} - ${progress.status}`);
        }
      });
      
      if (clientResult && clientResult.transcript && clientResult.transcript.length > 0) {
        console.log(`  ✅ Client-side success: ${clientResult.transcript.length} segments from ${clientResult.source}`);
      } else {
        console.log('  ❌ Client-side methods failed');
      }
      
    } catch (error) {
      console.log(`  ❌ Client-side error: ${error.message}`);
    }
    
    try {
      // Test 2: Full fallback chain
      console.log('  🔄 Testing full fallback chain...');
      const fullResult = await getTranscript(video.id, 'en', {
        onProgress: (progress) => {
          console.log(`    📊 Progress: ${progress.step} - ${progress.status}`);
        },
        retryCount: 1
      });
      
      if (fullResult && fullResult.transcript && fullResult.transcript.length > 0) {
        console.log(`  ✅ Full chain success: ${fullResult.transcript.length} segments from ${fullResult.source}`);
        console.log(`    📝 Sample text: "${fullResult.transcript[0].text.substring(0, 50)}..."`);
      } else {
        console.log('  ❌ Full chain failed');
      }
      
    } catch (error) {
      console.log(`  ❌ Full chain error: ${error.message}`);
    }
    
    // Small delay between tests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log('\n🏁 Transcript service tests completed!');
}

async function testLanguageDetection() {
  console.log('\n🌐 Testing language detection...');
  
  for (const video of testVideos) {
    try {
      console.log(`\n📹 Getting languages for: ${video.name}`);
      const languages = await getAvailableLanguages(video.id);
      
      if (languages && languages.length > 0) {
        console.log(`  ✅ Found ${languages.length} languages:`);
        languages.forEach(lang => {
          console.log(`    - ${lang.code}: ${lang.name} ${lang.isAutoGenerated ? '(auto)' : '(manual)'}`);
        });
      } else {
        console.log('  ❌ No languages found');
      }
      
    } catch (error) {
      console.log(`  ❌ Language detection error: ${error.message}`);
    }
  }
}

async function testCORSProxies() {
  console.log('\n🌐 Testing CORS proxies...');
  
  const testUrl = 'https://www.youtube.com/api/timedtext?v=pqWUuYTcG-o&lang=en&fmt=json3';
  const proxies = [
    'https://api.allorigins.win/raw?url=',
    'https://corsproxy.io/?',
    'https://api.codetabs.com/v1/proxy?quest='
  ];
  
  for (const proxy of proxies) {
    try {
      console.log(`  🔄 Testing proxy: ${proxy}`);
      const response = await fetch(`${proxy}${encodeURIComponent(testUrl)}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json, text/plain, */*',
        }
      });
      
      if (response.ok) {
        const data = await response.text();
        if (data && data.length > 100) {
          console.log(`  ✅ Proxy working: ${data.length} bytes received`);
        } else {
          console.log(`  ⚠️ Proxy returned empty/small response: ${data.length} bytes`);
        }
      } else {
        console.log(`  ❌ Proxy failed: HTTP ${response.status}`);
      }
      
    } catch (error) {
      console.log(`  ❌ Proxy error: ${error.message}`);
    }
  }
}

// Export functions for manual testing
if (typeof window !== 'undefined') {
  window.testTranscriptService = testTranscriptService;
  window.testLanguageDetection = testLanguageDetection;
  window.testCORSProxies = testCORSProxies;
  
  console.log(`
🧪 Transcript Service Test Functions Available:
- testTranscriptService() - Test full transcript fetching
- testLanguageDetection() - Test language detection
- testCORSProxies() - Test CORS proxy functionality

Run any of these functions in the console to test the service.
  `);
}

// Auto-run tests if this script is executed directly
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testTranscriptService,
    testLanguageDetection,
    testCORSProxies
  };
} else if (typeof window !== 'undefined' && window.location.hostname === 'localhost') {
  // Auto-run basic tests in development
  console.log('🚀 Auto-running basic CORS proxy test...');
  testCORSProxies();
}
