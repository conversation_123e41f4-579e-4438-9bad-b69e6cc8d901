// Debug script to check API connectivity
import axios from 'axios';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000/api';

async function checkApiConnectivity() {
  console.log('Checking API connectivity...');
  console.log('API URL:', API_URL);
  
  try {
    console.log('Fetching examples...');
    const response = await axios.get(`${API_URL}/examples`);
    console.log('Examples response:', response.data);
    return true;
  } catch (error) {
    console.error('Error fetching examples:', error);
    console.error('Error details:', error.response || error.message);
    return false;
  }
}

// Export the function to be called from other components
export { checkApiConnectivity };
