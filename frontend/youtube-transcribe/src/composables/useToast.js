/**
 * Toast notification composable
 * Provides easy-to-use methods for showing toast notifications
 */

export function useToast() {
  
  /**
   * Show a toast notification
   * @param {Object} options - Toast options
   * @param {string} options.type - Toast type: 'success', 'error', 'warning', 'info', 'copy'
   * @param {string} options.message - Toast message
   * @param {string} [options.title] - Optional toast title
   * @param {number} [options.duration=3000] - Auto-dismiss duration in ms (0 = no auto-dismiss)
   * @param {boolean} [options.dismissible=true] - Whether toast can be manually dismissed
   */
  const showToast = (options) => {
    const event = new CustomEvent('show-toast', {
      detail: options
    });
    window.dispatchEvent(event);
  };

  /**
   * Show a success toast
   * @param {string} message - Success message
   * @param {Object} options - Additional options
   */
  const showSuccess = (message, options = {}) => {
    showToast({
      type: 'success',
      message,
      duration: 3000,
      ...options
    });
  };

  /**
   * Show an error toast
   * @param {string} message - Error message
   * @param {Object} options - Additional options
   */
  const showError = (message, options = {}) => {
    showToast({
      type: 'error',
      message,
      duration: 5000, // Errors stay longer
      ...options
    });
  };

  /**
   * Show a warning toast
   * @param {string} message - Warning message
   * @param {Object} options - Additional options
   */
  const showWarning = (message, options = {}) => {
    showToast({
      type: 'warning',
      message,
      duration: 4000,
      ...options
    });
  };

  /**
   * Show an info toast
   * @param {string} message - Info message
   * @param {Object} options - Additional options
   */
  const showInfo = (message, options = {}) => {
    showToast({
      type: 'info',
      message,
      duration: 3000,
      ...options
    });
  };

  /**
   * Show a copy success toast
   * @param {string} contentType - Type of content copied (e.g., 'Transcript', 'Summary', 'Quote')
   * @param {Object} options - Additional options
   */
  const showCopySuccess = (contentType = 'Content', options = {}) => {
    showToast({
      type: 'copy',
      title: 'Copied!',
      message: `${contentType} copied to clipboard`,
      duration: 2500,
      ...options
    });
  };

  /**
   * Show a copy error toast
   * @param {string} contentType - Type of content that failed to copy
   * @param {Object} options - Additional options
   */
  const showCopyError = (contentType = 'Content', options = {}) => {
    showToast({
      type: 'error',
      title: 'Copy Failed',
      message: `Failed to copy ${contentType.toLowerCase()} to clipboard`,
      duration: 4000,
      ...options
    });
  };

  /**
   * Show a download success toast
   * @param {string} filename - Name of downloaded file
   * @param {Object} options - Additional options
   */
  const showDownloadSuccess = (filename, options = {}) => {
    showToast({
      type: 'success',
      title: 'Download Started',
      message: `Downloading ${filename}`,
      duration: 3000,
      ...options
    });
  };

  /**
   * Show a loading toast (doesn't auto-dismiss)
   * @param {string} message - Loading message
   * @param {Object} options - Additional options
   */
  const showLoading = (message, options = {}) => {
    showToast({
      type: 'info',
      message,
      duration: 0, // Don't auto-dismiss
      dismissible: false,
      ...options
    });
  };

  /**
   * Utility function to copy text to clipboard and show appropriate toast
   * @param {string} text - Text to copy
   * @param {string} contentType - Type of content being copied
   */
  const copyToClipboard = async (text, contentType = 'Content') => {
    try {
      if (navigator.clipboard && window.isSecureContext) {
        // Use modern clipboard API
        await navigator.clipboard.writeText(text);
        showCopySuccess(contentType);
        return true;
      } else {
        // Fallback for older browsers or non-secure contexts
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        
        const successful = document.execCommand('copy');
        document.body.removeChild(textArea);
        
        if (successful) {
          showCopySuccess(contentType);
          return true;
        } else {
          throw new Error('Copy command failed');
        }
      }
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
      showCopyError(contentType);
      return false;
    }
  };

  /**
   * Utility function to download text as file and show toast
   * @param {string} content - Content to download
   * @param {string} filename - Filename for download
   * @param {string} mimeType - MIME type of file
   */
  const downloadAsFile = (content, filename, mimeType = 'text/plain') => {
    try {
      const blob = new Blob([content], { type: mimeType });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      
      showDownloadSuccess(filename);
      return true;
    } catch (error) {
      console.error('Failed to download file:', error);
      showError(`Failed to download ${filename}`);
      return false;
    }
  };

  return {
    showToast,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    showCopySuccess,
    showCopyError,
    showDownloadSuccess,
    showLoading,
    copyToClipboard,
    downloadAsFile
  };
}
