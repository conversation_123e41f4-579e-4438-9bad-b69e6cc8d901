/**
 * Simple test utility to validate transcript service functionality
 * This can be imported and used in Vue components for testing
 */

import { getTranscript, getAvailableLanguages } from '../services/transcriptService.js';

export async function quickTest() {
  console.log('🧪 Running quick transcript service test...');
  
  const testVideoId = 'pqWUuYTcG-o'; // Steve Jobs Stanford Commencement
  
  try {
    console.log('📹 Testing with video:', testVideoId);
    
    // Test transcript fetching with progress
    const result = await getTranscript(testVideoId, 'en', {
      onProgress: (progress) => {
        console.log(`📊 ${progress.step}: ${progress.status}`, progress.error || '');
      },
      retryCount: 1
    });
    
    if (result && result.transcript && result.transcript.length > 0) {
      console.log('✅ Test PASSED!');
      console.log(`📝 Got ${result.transcript.length} transcript segments`);
      console.log(`🔧 Source: ${result.source}`);
      console.log(`🌐 Language: ${result.language}`);
      console.log(`📄 Sample: "${result.transcript[0].text.substring(0, 100)}..."`);
      return true;
    } else {
      console.log('❌ Test FAILED: No transcript data received');
      return false;
    }
    
  } catch (error) {
    console.log('❌ Test FAILED with error:', error.message);
    return false;
  }
}

export async function testCORSProxies() {
  console.log('🌐 Testing CORS proxy availability...');
  
  const testUrl = 'https://httpbin.org/json'; // Simple test endpoint
  const proxies = [
    { name: 'AllOrigins', url: 'https://api.allorigins.win/raw?url=' },
    { name: 'CorsProxy.io', url: 'https://corsproxy.io/?' },
    { name: 'CodeTabs', url: 'https://api.codetabs.com/v1/proxy?quest=' }
  ];
  
  const results = [];
  
  for (const proxy of proxies) {
    try {
      console.log(`🔄 Testing ${proxy.name}...`);
      
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);
      
      const response = await fetch(`${proxy.url}${encodeURIComponent(testUrl)}`, {
        signal: controller.signal,
        headers: {
          'Accept': 'application/json'
        }
      });
      
      clearTimeout(timeoutId);
      
      if (response.ok) {
        const data = await response.text();
        if (data && data.length > 10) {
          console.log(`✅ ${proxy.name}: Working`);
          results.push({ name: proxy.name, status: 'working' });
        } else {
          console.log(`⚠️ ${proxy.name}: Empty response`);
          results.push({ name: proxy.name, status: 'empty' });
        }
      } else {
        console.log(`❌ ${proxy.name}: HTTP ${response.status}`);
        results.push({ name: proxy.name, status: 'error', code: response.status });
      }
      
    } catch (error) {
      if (error.name === 'AbortError') {
        console.log(`⏰ ${proxy.name}: Timeout`);
        results.push({ name: proxy.name, status: 'timeout' });
      } else {
        console.log(`❌ ${proxy.name}: ${error.message}`);
        results.push({ name: proxy.name, status: 'error', error: error.message });
      }
    }
  }
  
  const workingProxies = results.filter(r => r.status === 'working').length;
  console.log(`📊 CORS Proxy Test Results: ${workingProxies}/${proxies.length} working`);
  
  return results;
}

export async function testLanguageDetection() {
  console.log('🌐 Testing language detection...');
  
  const testVideoId = 'pqWUuYTcG-o';
  
  try {
    const languages = await getAvailableLanguages(testVideoId);
    
    if (languages && languages.length > 0) {
      console.log(`✅ Found ${languages.length} languages:`);
      languages.forEach(lang => {
        console.log(`  - ${lang.code}: ${lang.name} ${lang.isAutoGenerated ? '(auto)' : '(manual)'}`);
      });
      return languages;
    } else {
      console.log('❌ No languages detected');
      return [];
    }
    
  } catch (error) {
    console.log('❌ Language detection failed:', error.message);
    return [];
  }
}

// Auto-test function that runs all tests
export async function runAllTests() {
  console.log('🚀 Running comprehensive transcript service tests...\n');
  
  const results = {
    corsProxies: await testCORSProxies(),
    languageDetection: await testLanguageDetection(),
    transcriptFetch: await quickTest()
  };
  
  console.log('\n📋 Test Summary:');
  console.log(`CORS Proxies: ${results.corsProxies.filter(r => r.status === 'working').length}/${results.corsProxies.length} working`);
  console.log(`Language Detection: ${results.languageDetection.length > 0 ? 'PASS' : 'FAIL'}`);
  console.log(`Transcript Fetch: ${results.transcriptFetch ? 'PASS' : 'FAIL'}`);
  
  return results;
}

// Make functions available globally for console testing
if (typeof window !== 'undefined') {
  window.transcriptServiceTest = {
    quickTest,
    testCORSProxies,
    testLanguageDetection,
    runAllTests
  };
  
  console.log(`
🧪 Transcript Service Test Functions:
- transcriptServiceTest.quickTest()
- transcriptServiceTest.testCORSProxies()  
- transcriptServiceTest.testLanguageDetection()
- transcriptServiceTest.runAllTests()
  `);
}
