<template>
  <div>
    <div v-if="loading || waitingForSubtitles" class="flex flex-col justify-center items-center py-12 space-y-4">
      <div class="relative">
        <div class="w-20 h-20 flex items-center justify-center">
          <svg class="animate-spin h-12 w-12 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        </div>
      </div>
      <p class="text-base-content/70 font-medium">
        {{ waitingForSubtitles ? 'Loading subtitles...' : 'Loading video...' }}
      </p>
    </div>

    <div v-else-if="error" class="alert alert-error">
      <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
      <span>{{ error }}</span>
    </div>

    <div v-else-if="videoInfo" class="space-y-4">
      <h3 class="text-lg font-bold line-clamp-2">{{ videoInfo.snippet.title }}</h3>
      <div class="relative">
        <div class="relative pb-[56.25%] h-0 overflow-hidden rounded-lg">
          <iframe
            ref="videoIframe"
            :key="videoId"
            :src="`https://www.youtube-nocookie.com/embed/${videoId}?autoplay=0&rel=0&modestbranding=1&fs=1&playsinline=1&origin=${typeof window !== 'undefined' ? window.location.origin : 'https://localhost:5175'}`"
            class="absolute top-0 left-0 w-full h-full"
            frameborder="0"
            allow="autoplay; encrypted-media"
            allowfullscreen
            title="YouTube video player"
            loading="lazy"
          ></iframe>
        </div>
        <div
          v-if="currentTime > 0"
          class="absolute bottom-4 right-4 bg-black/70 text-white px-3 py-1 rounded font-medium text-sm"
        >
          {{ formatTime(currentTime) }}
        </div>
      </div>
    </div>

    <div v-else class="alert">
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-info shrink-0 w-6 h-6"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
      <span>No video selected. Please select a video to view.</span>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, onUnmounted } from 'vue';
import api from '../services/api';

const props = defineProps({
  videoId: {
    type: String,
    required: true
  },
  transcript: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['time-update']);

// Reactive state
const loading = ref(false);
const error = ref('');
const videoInfo = ref(null);
const videoIframe = ref(null);
const currentTime = ref(0);
const waitingForSubtitles = ref(false); // Start with false to avoid infinite loading

// Simple seek function using iframe src manipulation
function seekToTime(time, forcePlay = false) {
  if (!videoIframe.value || !props.videoId) {
    console.warn('VideoPreview: Cannot seek - iframe not available');
    return;
  }

  const seekTime = Math.max(0, parseFloat(time));
  console.log(`VideoPreview: 🎯 Seeking to ${seekTime} seconds, forcePlay: ${forcePlay}`);

  // Clear loading states when seeking
  waitingForSubtitles.value = false;

  try {
    // Use iframe src with start parameter for seeking
    const autoplayParam = forcePlay ? '&autoplay=1' : '';
    const startTime = Math.floor(seekTime);
    const origin = typeof window !== 'undefined' ? window.location.origin : 'https://localhost:5175';
    const newSrc = `https://www.youtube-nocookie.com/embed/${props.videoId}?start=${startTime}&rel=0&modestbranding=1&fs=1&playsinline=1&origin=${origin}${autoplayParam}`;

    videoIframe.value.src = newSrc;
    console.log(`VideoPreview: Updated iframe src with start=${startTime}, autoplay=${forcePlay}`);

    // Update current time immediately for UI feedback
    currentTime.value = seekTime;
    emit('time-update', seekTime);

  } catch (error) {
    console.error('VideoPreview: Error seeking:', error);
  }
}

// Simple utility functions
function setSubtitleLoadingState(isLoading) {
  console.log(`VideoPreview: Setting subtitle loading state to ${isLoading}`);
  waitingForSubtitles.value = isLoading;

  // Add timeout to prevent infinite loading
  if (isLoading) {
    setTimeout(() => {
      if (waitingForSubtitles.value) {
        console.log('VideoPreview: Subtitle loading timeout, clearing state');
        waitingForSubtitles.value = false;
      }
    }, 5000); // Shorter timeout
  }
}

function formatTime(seconds) {
  if (typeof seconds !== 'number' || isNaN(seconds)) seconds = 0;
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  if (hours > 0) {
    return [hours.toString().padStart(2, '0'), minutes.toString().padStart(2, '0'), secs.toString().padStart(2, '0')].join(':');
  } else {
    return [minutes.toString().padStart(2, '0'), secs.toString().padStart(2, '0')].join(':');
  }
}

async function fetchVideoInfo() {
  if (!props.videoId) return;
  loading.value = true;
  error.value = '';

  try {
    // Try to fetch from YouTube API first, with quota error handling
    try {
      console.log(`VideoPreview: Attempting to fetch video info for ${props.videoId}`);
      const response = await api.getVideoInfo(props.videoId);
      videoInfo.value = response.data;
      console.log(`VideoPreview: Successfully fetched video info: "${response.data.snippet.title}"`);
      return;
    } catch (apiError) {
      console.warn('VideoPreview: YouTube API error, falling back to smart title generation:', apiError.message);

      // Check if it's a quota error
      if (apiError.response?.data?.error?.includes('quota') ||
          apiError.response?.status === 403) {
        console.log('VideoPreview: Quota exceeded, using fallback method');
      }
    }

    // Fallback: Create a smart title based on video ID and transcript content
    let videoTitle = `Video ${props.videoId}`;

    // Try to create a better title from transcript if available
    if (props.transcript && props.transcript.length > 0) {
      const firstFewLines = props.transcript.slice(0, 5).map(item => item.text).join(' ');
      if (firstFewLines.length > 20) {
        // Create a title from the first few lines
        videoTitle = firstFewLines.substring(0, 60) + '...';
      }
    }

    // Special titles for known videos
    const knownTitles = {
      'arj7oStGLkU': 'Inside the mind of a master procrastinator | Tim Urban',
      'TT81fe2IobI': 'The Dunning-Kruger Effect',
      'dQw4w9WgXcQ': 'Rick Astley - Never Gonna Give You Up'
    };

    if (knownTitles[props.videoId]) {
      videoTitle = knownTitles[props.videoId];
    }

    videoInfo.value = {
      snippet: {
        title: videoTitle,
        description: 'Video loaded successfully (fallback mode)'
      }
    };

    console.log(`VideoPreview: Using fallback title: "${videoTitle}"`);

  } catch (err) {
    console.error('VideoPreview: Error in fetchVideoInfo:', err);
    error.value = err.response?.data?.error || 'Failed to fetch video information';
  } finally {
    loading.value = false;
  }
}

// Lifecycle hooks
onMounted(() => {
  console.log('VideoPreview: Component mounted');
});

onUnmounted(() => {
  console.log('VideoPreview: Component unmounting');
});

// Watch for video ID changes
watch(() => props.videoId, async (newId, oldId) => {
  console.log(`🎥 VideoPreview: Video ID watcher triggered`);
  console.log(`🎥 VideoPreview: oldId: "${oldId}" -> newId: "${newId}"`);
  console.log(`🎥 VideoPreview: props.videoId is: "${props.videoId}"`);

  if (!newId) {
    console.log(`🎥 VideoPreview: No video ID provided, clearing state`);
    videoInfo.value = null;
    error.value = '';
    loading.value = false;
    return;
  }

  console.log(`🎥 VideoPreview: Starting load process for video: ${newId}`);

  // Special debugging for problematic video
  if (newId === 'arj7oStGLkU') {
    console.log('🔍 VideoPreview: DEBUGGING PROBLEMATIC VIDEO arj7oStGLkU');
    console.log('🔍 VideoPreview: Setting waitingForSubtitles to FALSE immediately');
    waitingForSubtitles.value = false;
  }

  currentTime.value = 0;
  error.value = '';
  await fetchVideoInfo();
}, { immediate: true });

// Watch for transcript changes
watch(() => props.transcript, (newTranscript) => {
  console.log(`VideoPreview: Transcript watcher triggered for video ${props.videoId}`);
  console.log(`VideoPreview: Transcript length: ${newTranscript ? newTranscript.length : 0}`);

  if (newTranscript && newTranscript.length > 0) {
    const firstItem = newTranscript[0];
    const transcriptStartTime = firstItem.start || 0;
    console.log(`VideoPreview: Transcript loaded, starts at ${transcriptStartTime} seconds`);

    // Special handling for problematic video
    if (props.videoId === 'arj7oStGLkU') {
      console.log('🔍 VideoPreview: PROBLEMATIC VIDEO - Force clearing loading states');
      waitingForSubtitles.value = false;
      loading.value = false;
      return;
    }

    // Clear loading states for videos with intro delays
    if (transcriptStartTime > 5) {
      console.log(`VideoPreview: Video has ${transcriptStartTime}s intro, clearing loading states`);
      waitingForSubtitles.value = false;
    }
  } else {
    console.log(`VideoPreview: No transcript data available for video ${props.videoId}`);
  }
}, { immediate: true });

// Expose methods to parent components
defineExpose({
  seekToTime,
  setSubtitleLoadingState
});
</script>
