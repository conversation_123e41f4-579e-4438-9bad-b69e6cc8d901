<template>
  <div class="tabs-container">
    <!-- Tab Navigation -->
    <div class="flex flex-wrap gap-2 mb-6 border-b pb-2">
      <button
        v-for="tab in tabs"
        :key="tab.id"
        class="px-4 py-2 rounded-md font-medium transition-all duration-200"
        :class="{
          'bg-primary text-white shadow-md': activeTab === tab.id,
          'bg-base-200 hover:bg-base-300': activeTab !== tab.id
        }"
        @click="activeTab = tab.id"
      >
        {{ tab.label }}
      </button>
    </div>

    <!-- Tab Content -->
    <div class="tab-content">
      <!-- Transcript Tab -->
      <div v-if="activeTab === 'transcript'" class="tab-pane">
        <BasicTranscript
          :videoId="videoId"
          @seek-to-time="$emit('seek-to-time', $event)"
        />
      </div>

      <!-- Summary Tab -->
      <div v-else-if="activeTab === 'summary'" class="tab-pane">
        <div class="flex flex-col items-center justify-center h-full p-8 text-center">
          <div class="w-16 h-16 mb-4 text-primary">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <h3 class="text-2xl font-bold mb-2">Video Summary</h3>
          <p class="text-base-content/70 mb-6 max-w-md">
            This feature will provide an AI-generated summary of the video content, highlighting the key points and main ideas.
          </p>
          <div class="badge badge-primary badge-outline">Coming Soon</div>
        </div>
      </div>

      <!-- Quotes Tab -->
      <div v-else-if="activeTab === 'quotes'" class="tab-pane">
        <div class="flex flex-col items-center justify-center h-full p-8 text-center">
          <div class="w-16 h-16 mb-4 text-primary">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
            </svg>
          </div>
          <h3 class="text-2xl font-bold mb-2">Key Quotes</h3>
          <p class="text-base-content/70 mb-6 max-w-md">
            This feature will extract important quotes and memorable statements from the video, making them easy to reference and share.
          </p>
          <div class="badge badge-primary badge-outline">Coming Soon</div>
        </div>
      </div>

      <!-- Descriptions Tab -->
      <div v-else-if="activeTab === 'descriptions'" class="tab-pane">
        <div class="flex flex-col items-center justify-center h-full p-8 text-center">
          <div class="w-16 h-16 mb-4 text-primary">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h7" />
            </svg>
          </div>
          <h3 class="text-2xl font-bold mb-2">Video Descriptions</h3>
          <p class="text-base-content/70 mb-6 max-w-md">
            This feature will generate alternative descriptions for your video, optimized for different platforms and audiences.
          </p>
          <div class="badge badge-primary badge-outline">Coming Soon</div>
        </div>
      </div>

      <!-- Social Media Tab -->
      <div v-else-if="activeTab === 'social'" class="tab-pane">
        <div class="flex flex-col items-center justify-center h-full p-8 text-center">
          <div class="w-16 h-16 mb-4 text-primary">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z" />
            </svg>
          </div>
          <h3 class="text-2xl font-bold mb-2">Social Media Content</h3>
          <p class="text-base-content/70 mb-6 max-w-md">
            This feature will generate social media posts based on your video content, tailored for different platforms like Twitter, LinkedIn, and Instagram.
          </p>
          <div class="badge badge-primary badge-outline">Coming Soon</div>
        </div>
      </div>

      <!-- Magic Chat Tab -->
      <div v-else-if="activeTab === 'chat'" class="tab-pane">
        <div class="flex flex-col items-center justify-center h-full p-8 text-center">
          <div class="w-16 h-16 mb-4 text-primary">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
            </svg>
          </div>
          <h3 class="text-2xl font-bold mb-2">Magic Chat</h3>
          <p class="text-base-content/70 mb-6 max-w-md">
            This feature will allow you to ask questions about the video content and get AI-powered answers, making it easier to understand and explore the material.
          </p>
          <div class="badge badge-primary badge-outline">Coming Soon</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import BasicTranscript from './BasicTranscript.vue';

export default {
  components: {
    BasicTranscript
  },

  props: {
    videoId: {
      type: String,
      required: true
    }
  },

  data() {
    return {
      activeTab: 'transcript',
      tabs: [
        { id: 'transcript', label: 'Transcript' },
        { id: 'summary', label: 'Summary' },
        { id: 'quotes', label: 'Quotes' },
        { id: 'descriptions', label: 'Descriptions' },
        { id: 'social', label: 'Social Media' },
        { id: 'chat', label: 'Magic Chat' }
      ]
    };
  },

  emits: ['seek-to-time']
};
</script>

<style scoped>
.tabs-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.tab-content {
  flex: 1;
}

.tab-pane {
  height: 100%;
}
</style>
