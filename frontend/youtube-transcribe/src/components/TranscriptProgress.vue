<template>
  <div v-if="visible" class="bg-base-200 rounded-lg p-4 mb-4">
    <div class="flex items-center justify-between mb-3">
      <h3 class="text-lg font-semibold">Fetching Transcript</h3>
      <div class="text-sm text-base-content/70">
        {{ currentStep + 1 }} of {{ totalSteps }}
      </div>
    </div>
    
    <!-- Progress bar -->
    <div class="w-full bg-base-300 rounded-full h-2 mb-4">
      <div 
        class="bg-primary h-2 rounded-full transition-all duration-300 ease-out"
        :style="{ width: progressPercentage + '%' }"
      ></div>
    </div>
    
    <!-- Current step info -->
    <div class="space-y-2">
      <div v-for="(step, index) in steps" :key="step.name" class="flex items-center space-x-3">
        <!-- Status icon -->
        <div class="flex-shrink-0">
          <div v-if="index < currentStep" class="w-5 h-5 bg-success rounded-full flex items-center justify-center">
            <svg class="w-3 h-3 text-success-content" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
          </div>
          <div v-else-if="index === currentStep" class="w-5 h-5">
            <svg class="animate-spin text-primary" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </div>
          <div v-else-if="step.status === 'error'" class="w-5 h-5 bg-error rounded-full flex items-center justify-center">
            <svg class="w-3 h-3 text-error-content" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </div>
          <div v-else class="w-5 h-5 bg-base-300 rounded-full"></div>
        </div>
        
        <!-- Step info -->
        <div class="flex-1">
          <div class="flex items-center justify-between">
            <span class="font-medium" :class="{
              'text-success': index < currentStep,
              'text-primary': index === currentStep,
              'text-error': step.status === 'error',
              'text-base-content/50': index > currentStep && step.status !== 'error'
            }">
              {{ step.label }}
            </span>
            <span v-if="step.status === 'error'" class="text-xs text-error">
              Failed
            </span>
            <span v-else-if="index < currentStep" class="text-xs text-success">
              Success
            </span>
            <span v-else-if="index === currentStep" class="text-xs text-primary">
              Trying...
            </span>
          </div>
          
          <!-- Error message -->
          <div v-if="step.status === 'error' && step.error" class="text-xs text-error mt-1">
            {{ step.error }}
          </div>
          
          <!-- Step description -->
          <div v-if="step.description" class="text-xs text-base-content/70 mt-1">
            {{ step.description }}
          </div>
        </div>
      </div>
    </div>
    
    <!-- Overall status message -->
    <div v-if="statusMessage" class="mt-4 p-3 rounded-lg" :class="{
      'bg-info/10 text-info': status === 'attempting',
      'bg-success/10 text-success': status === 'success',
      'bg-error/10 text-error': status === 'error'
    }">
      <div class="flex items-center space-x-2">
        <svg v-if="status === 'attempting'" class="animate-spin w-4 h-4" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <svg v-else-if="status === 'success'" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
        </svg>
        <svg v-else-if="status === 'error'" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
        <span class="text-sm font-medium">{{ statusMessage }}</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TranscriptProgress',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    progress: {
      type: Object,
      default: () => null
    }
  },
  
  data() {
    return {
      steps: [
        {
          name: 'cors-proxy',
          label: 'Direct Browser Fetch',
          description: 'Trying to fetch transcript directly from YouTube via CORS proxy',
          status: 'pending'
        },
        {
          name: 'youtube-api',
          label: 'YouTube Data API',
          description: 'Using YouTube Data API v3 for caption access',
          status: 'pending'
        },
        {
          name: 'backend-api',
          label: 'Backend Server',
          description: 'Falling back to server-side transcript fetching',
          status: 'pending'
        }
      ],
      currentStep: 0,
      status: 'attempting',
      statusMessage: ''
    };
  },
  
  computed: {
    totalSteps() {
      return this.steps.length;
    },
    
    progressPercentage() {
      if (this.status === 'success') return 100;
      return Math.round((this.currentStep / this.totalSteps) * 100);
    }
  },
  
  watch: {
    progress: {
      handler(newProgress) {
        if (!newProgress) return;
        
        this.updateProgress(newProgress);
      },
      deep: true,
      immediate: true
    }
  },
  
  methods: {
    updateProgress(progress) {
      const { step, status, error, details } = progress;
      
      // Find the step index
      const stepIndex = this.steps.findIndex(s => s.name === step);
      
      if (stepIndex !== -1) {
        // Update current step
        this.currentStep = stepIndex;
        
        // Update step status
        this.steps[stepIndex].status = status;
        if (error) {
          this.steps[stepIndex].error = error;
        }
        
        // Update overall status
        this.status = status;
        
        // Update status message
        if (status === 'attempting') {
          this.statusMessage = `Trying ${this.steps[stepIndex].label}...`;
        } else if (status === 'success') {
          this.statusMessage = `Successfully fetched transcript via ${this.steps[stepIndex].label}`;
          if (details && details.transcriptLength) {
            this.statusMessage += ` (${details.transcriptLength} segments)`;
          }
        } else if (status === 'error') {
          this.statusMessage = `${this.steps[stepIndex].label} failed: ${error}`;
        } else if (status === 'failed') {
          this.statusMessage = `${this.steps[stepIndex].label} returned no results`;
        }
      }
      
      // Handle special progress events
      if (step === 'start') {
        this.currentStep = 0;
        this.status = 'attempting';
        this.statusMessage = 'Starting transcript fetch...';
        // Reset all steps
        this.steps.forEach(s => {
          s.status = 'pending';
          s.error = null;
        });
      } else if (step === 'complete') {
        if (status === 'error') {
          this.status = 'error';
          this.statusMessage = 'All transcript fetching methods failed';
        }
      }
    },
    
    reset() {
      this.currentStep = 0;
      this.status = 'attempting';
      this.statusMessage = '';
      this.steps.forEach(step => {
        step.status = 'pending';
        step.error = null;
      });
    }
  }
};
</script>
