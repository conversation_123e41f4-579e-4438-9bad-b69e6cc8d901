<template>
  <div v-if="visible" class="flex flex-col items-center justify-center py-8 space-y-4">
    <div class="relative">
      <div class="w-16 h-16 flex items-center justify-center">
        <svg class="animate-spin h-10 w-10 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      </div>
    </div>
    <p class="text-base-content/70 font-medium">{{ userFriendlyMessage }}</p>
  </div>
</template>

<script>
export default {
  name: 'TranscriptProgress',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    progress: {
      type: Object,
      default: () => null
    }
  },
  
  data() {
    return {
      userFriendlyMessage: 'Loading transcript...'
    };
  },
  
  watch: {
    progress: {
      handler(newProgress) {
        if (!newProgress) return;
        
        this.updateProgress(newProgress);
      },
      deep: true,
      immediate: true
    }
  },
  
  methods: {
    updateProgress(progress) {
      const { step, status } = progress;

      // Show user-friendly messages without technical details
      if (status === 'attempting') {
        this.userFriendlyMessage = 'Fetching transcript...';
      } else if (status === 'success') {
        this.userFriendlyMessage = 'Transcript loaded successfully!';
      } else if (status === 'error' && step === 'complete') {
        this.userFriendlyMessage = 'Unable to load transcript. Please try again.';
      }

      // Don't show technical step details to end users
    },

    reset() {
      this.userFriendlyMessage = 'Loading transcript...';
    }
  }
};
</script>
