<template>
  <div class="max-w-3xl mx-auto py-8">
    <h1 class="text-3xl font-bold mb-6"><PERSON><PERSON> Settings</h1>
    
    <div class="card bg-base-100 shadow-md">
      <div class="card-body">
        <p class="mb-6">We use cookies to enhance your experience on our website. You can choose which types of cookies you allow us to use.</p>
        
        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <div>
              <h3 class="font-bold">Essential Cookies</h3>
              <p class="text-sm text-base-content/70">These cookies are necessary for the website to function and cannot be switched off.</p>
            </div>
            <input type="checkbox" checked disabled class="toggle toggle-primary" />
          </div>
          
          <div class="flex items-center justify-between">
            <div>
              <h3 class="font-bold">Performance Cookies</h3>
              <p class="text-sm text-base-content/70">These cookies allow us to count visits and traffic sources so we can measure and improve the performance of our site.</p>
            </div>
            <input type="checkbox" class="toggle toggle-primary" />
          </div>
          
          <div class="flex items-center justify-between">
            <div>
              <h3 class="font-bold">Functional Cookies</h3>
              <p class="text-sm text-base-content/70">These cookies enable the website to provide enhanced functionality and personalization.</p>
            </div>
            <input type="checkbox" class="toggle toggle-primary" />
          </div>
          
          <div class="flex items-center justify-between">
            <div>
              <h3 class="font-bold">Targeting Cookies</h3>
              <p class="text-sm text-base-content/70">These cookies may be set through our site by our advertising partners to build a profile of your interests.</p>
            </div>
            <input type="checkbox" class="toggle toggle-primary" />
          </div>
        </div>
        
        <div class="flex justify-end gap-2 mt-6">
          <button class="btn btn-outline">Reject All</button>
          <button class="btn btn-primary">Accept Selected</button>
        </div>
      </div>
    </div>
  </div>
</template>
