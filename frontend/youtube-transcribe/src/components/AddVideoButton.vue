<script setup>
const emit = defineEmits(['add-video']);

function addAnotherVideo() {
  emit('add-video');
}
</script>

<template>
  <div 
    class="card bg-base-100 shadow-sm hover:shadow-md transition-all cursor-pointer flex-shrink-0 border-2 border-dashed border-primary/30 hover:border-primary"
    style="width: calc(20% - 12px);"
    @click="addAnotherVideo"
  >
    <div class="flex flex-col items-center justify-center p-4 h-full min-h-[120px]">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-primary/50" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
      </svg>
      <span class="text-xs mt-2 text-primary/70 font-medium">Add another video</span>
      <span class="text-xs text-base-content/40 mt-1">Paste a new URL above</span>
    </div>
  </div>
</template>
