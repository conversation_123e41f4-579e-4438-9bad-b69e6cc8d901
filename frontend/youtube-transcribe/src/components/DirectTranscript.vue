<template>
  <div class="p-4">
    <h2 class="text-xl font-bold mb-4">Direct Transcript</h2>

    <div v-if="loading" class="flex justify-center py-8">
      <span class="loading loading-spinner loading-lg text-primary"></span>
    </div>

    <div v-else-if="error" class="alert alert-error mb-4">
      <span>{{ error }}</span>
    </div>

    <div v-else-if="!transcriptItems || transcriptItems.length === 0" class="alert alert-warning mb-4">
      <span>No transcript data available.</span>
    </div>

    <div v-else>
      <div class="alert alert-success mb-4">
        <span>Transcript loaded successfully! {{ transcriptItems.length }} items found.</span>
      </div>

      <div class="h-[500px] overflow-y-auto bg-base-100 p-4 rounded-lg border border-base-300 relative z-30">
        <div v-for="(item, index) in transcriptItems" :key="index" class="mb-4 p-2 border-b border-base-200">
          <div class="font-medium text-primary mb-1">{{ item.formattedStart || formatTime(item.start) }}</div>
          <p>{{ item.text }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    videoId: {
      type: String,
      required: true
    }
  },

  data() {
    return {
      loading: false,
      error: null,
      transcriptData: null,
      transcriptItems: []
    };
  },

  // Add watcher for videoId to automatically fetch transcript when it changes
  watch: {
    videoId: {
      immediate: true,
      handler: 'fetchTranscript'
    }
  },

  methods: {
    async fetchTranscript() {
      if (!this.videoId) {
        console.log('DirectTranscript: No videoId provided, skipping transcript fetch');
        return;
      }

      this.loading = true;
      this.error = null;
      this.transcriptData = null;
      this.transcriptItems = [];

      try {
        console.log('DirectTranscript: Fetching transcript for video ID:', this.videoId);

        // Make a direct fetch request to the backend API
        const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000/api';
        const response = await fetch(`${API_URL}/transcript/${this.videoId}`);

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('DirectTranscript: Raw API response:', data);

        this.transcriptData = data;

        if (data && data.transcript && Array.isArray(data.transcript)) {
          console.log('DirectTranscript: Transcript found, length:', data.transcript.length);
          this.transcriptItems = data.transcript;
        } else {
          throw new Error('Invalid transcript data format');
        }
      } catch (err) {
        console.error('DirectTranscript: Error fetching transcript:', err);
        this.error = err.message;
      } finally {
        this.loading = false;
      }
    },

    formatTime(seconds) {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const secs = Math.floor(seconds % 60);

      return [
        hours.toString().padStart(2, '0'),
        minutes.toString().padStart(2, '0'),
        secs.toString().padStart(2, '0')
      ].join(':');
    }
  }
};
</script>
