<script setup>
const emit = defineEmits(['play-video']);

function playVideo() {
  // This is now just a placeholder that emits an event to focus on the input field
  emit('play-video', '');
}
</script>

<template>
  <div
    class="card-container"
    @click="playVideo"
  >
    <div class="card transition-all cursor-pointer flex-shrink-0 bg-base-100 hover:shadow-lg rounded-xl">
      <div class="flex flex-col items-center justify-center p-4 h-full min-h-[120px]">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-base-content/30" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
        </svg>
        <span class="text-xs mt-2 text-base-content/50 font-medium">Add your own video</span>
        <span class="text-xs text-base-content/40 mt-1">Paste URL above to add</span>
      </div>
    </div>
  </div>
</template>

<style scoped>
.card-container {
  position: relative;
  width: calc(20% - 12px);
  flex-shrink: 0;
  border-radius: var(--rounded-box, 1rem);
  cursor: pointer;
}

.card-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: var(--rounded-box, 1rem);
  padding: 2px;
  background: linear-gradient(135deg, #6419e6, #d926aa, #1fb2a5);
  mask: repeating-linear-gradient(45deg,
    transparent 0,
    transparent 4px,
    #000 4px,
    #000 8px
  );
  -webkit-mask: repeating-linear-gradient(45deg,
    transparent 0,
    transparent 4px,
    #000 4px,
    #000 8px
  );
  pointer-events: none;
}

.card {
  position: relative;
  z-index: 1;
  margin: 2px;
  height: calc(100% - 4px);
  width: calc(100% - 4px);
}
</style>
