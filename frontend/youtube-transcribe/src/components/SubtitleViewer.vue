<template>
  <div class="subtitle-viewer">
    <div v-if="loading" class="loading">
      <p>Loading subtitles...</p>
    </div>

    <div v-else-if="error" class="error">
      <p>{{ error }}</p>
    </div>

    <div v-else-if="captions.length === 0" class="no-captions">
      <p>No subtitles found for this video.</p>
    </div>

    <div v-else class="captions-container">
      <h3>Available Subtitles</h3>

      <div class="language-selector">
        <label for="language-select">Select Language:</label>
        <select id="language-select" v-model="selectedLanguage">
          <option
            v-for="caption in captions"
            :key="caption.id"
            :value="caption.id"
          >
            {{ caption.snippet.language }} ({{ caption.snippet.trackKind }})
          </option>
        </select>
      </div>

      <div class="subtitle-content">
        <p v-if="subtitleContent">{{ subtitleContent }}</p>
        <p v-else>Select a language to view subtitles</p>
      </div>

      <div class="download-options">
        <h4>Download Options</h4>
        <div class="format-buttons">
          <button @click="downloadSubtitles('txt')">Download as TXT</button>
          <button @click="downloadSubtitles('srt')">Download as SRT</button>
          <button @click="downloadSubtitles('vtt')">Download as VTT</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue';
import api from '../services/api';

const props = defineProps({
  videoId: {
    type: String,
    required: true
  }
});

const loading = ref(false);
const error = ref('');
const captions = ref([]);
const selectedLanguage = ref('');
const subtitleContent = ref('');

// Fetch available captions when component mounts or videoId changes
watch(() => props.videoId, fetchCaptions, { immediate: true });

async function fetchCaptions() {
  if (!props.videoId) return;

  loading.value = true;
  error.value = '';
  captions.value = [];

  try {
    const response = await api.getCaptions(props.videoId);
    captions.value = response.data;

    // Auto-select first caption if available
    if (captions.value.length > 0) {
      selectedLanguage.value = captions.value[0].id;
    }
  } catch (err) {
    console.error('Error fetching captions:', err);
    error.value = err.response?.data?.error || 'Failed to fetch captions';
  } finally {
    loading.value = false;
  }
}

// Watch for language selection changes to fetch subtitle content
watch(selectedLanguage, async (newValue) => {
  if (!newValue) return;

  // In a real implementation, you would fetch the actual subtitle content here
  // For now, we'll just set a placeholder
  subtitleContent.value = `This is a placeholder for subtitle content in the selected language (ID: ${newValue})`;
});

// Download subtitles in the selected format
async function downloadSubtitles(format) {
  if (!selectedLanguage.value) {
    error.value = 'Please select a language first';
    return;
  }

  try {
    // Call the API service to download the subtitles
    const response = await api.downloadCaptions(
      props.videoId,
      format,
      selectedLanguage.value
    );

    // Create a download link
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', `subtitles-${props.videoId}.${format}`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

  } catch (err) {
    console.error('Error downloading subtitles:', err);
    error.value = 'Failed to download subtitles';
  }
}
</script>

<style scoped>
.subtitle-viewer {
  max-width: 800px;
  margin: 20px auto;
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.loading, .error, .no-captions {
  text-align: center;
  padding: 20px;
}

.error {
  color: #ff0000;
}

.captions-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

h3 {
  margin-bottom: 10px;
  color: #333;
}

.language-selector {
  display: flex;
  align-items: center;
  gap: 10px;
}

select {
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #ccc;
}

.subtitle-content {
  background-color: white;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #ddd;
  min-height: 200px;
  max-height: 400px;
  overflow-y: auto;
}

.download-options {
  margin-top: 20px;
}

.format-buttons {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

button {
  padding: 10px 15px;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

button:hover {
  background-color: #45a049;
}
</style>
