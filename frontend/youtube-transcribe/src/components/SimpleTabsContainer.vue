<template>
  <div class="simple-tabs-container">
    <!-- Tab Navigation -->
    <div class="tab-buttons mb-4 border-b pb-2">
      <button
        v-for="tab in tabs"
        :key="tab.id"
        class="tab-button px-4 py-2 rounded-md font-medium mr-2"
        :class="{ 'active-tab': activeTab === tab.id }"
        @click="setActiveTab(tab.id)"
      >
        {{ tab.label }}
      </button>
    </div>

    <!-- Tab Content - Using v-if for maximum reliability -->
    <div class="tab-content-container">
      <!-- Transcript Tab -->
      <div v-if="activeTab === 'transcript'" class="tab-content">
        <div class="transcript-container">
          <h2 class="text-xl font-bold mb-4">Video Transcript</h2>
          <p class="text-sm text-base-content/70 mb-4">Click on any timestamp to jump to that point in the video.</p>

          <div v-if="loading" class="flex justify-center py-8">
            <span class="loading loading-spinner loading-lg text-primary"></span>
          </div>

          <div v-else-if="error" class="alert alert-error mb-4">
            <span>{{ error }}</span>
          </div>

          <div v-else-if="!transcript || transcript.length === 0" class="alert alert-warning mb-4">
            <span>No transcript data available for this video.</span>
          </div>

          <div v-else class="transcript-list bg-base-100 p-4 rounded-lg border border-base-300">
            <table class="w-full">
              <thead>
                <tr>
                  <th class="text-left pb-2 w-24">Timestamp</th>
                  <th class="text-left pb-2">Text</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(item, index) in transcript" :key="index" class="border-b border-base-200 hover:bg-base-200">
                  <td class="py-2 pr-4 font-medium text-primary whitespace-nowrap">
                    <button
                      class="px-2 py-1 rounded hover:bg-primary hover:text-white transition-colors"
                      @click="seekToTime(item.start)"
                    >
                      {{ item.formattedStart || formatTime(item.start) }}
                    </button>
                  </td>
                  <td class="py-2">{{ item.text }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- Summary Tab -->
      <div v-else-if="activeTab === 'summary'" class="tab-content">
        <div class="flex flex-col items-center justify-center p-8 text-center">
          <div class="w-16 h-16 mb-4 text-primary">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <h3 class="text-2xl font-bold mb-2">Video Summary</h3>
          <p class="text-base-content/70 mb-6 max-w-md">
            This feature will provide an AI-generated summary of the video content, highlighting the key points and main ideas.
          </p>
          <div class="badge badge-primary badge-outline">Coming Soon</div>
        </div>
      </div>

      <!-- Quotes Tab -->
      <div v-else-if="activeTab === 'quotes'" class="tab-content">
        <div class="flex flex-col items-center justify-center p-8 text-center">
          <div class="w-16 h-16 mb-4 text-primary">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
            </svg>
          </div>
          <h3 class="text-2xl font-bold mb-2">Key Quotes</h3>
          <p class="text-base-content/70 mb-6 max-w-md">
            This feature will extract important quotes and memorable statements from the video, making them easy to reference and share.
          </p>
          <div class="badge badge-primary badge-outline">Coming Soon</div>
        </div>
      </div>

      <!-- Descriptions Tab -->
      <div v-else-if="activeTab === 'descriptions'" class="tab-content">
        <div class="flex flex-col items-center justify-center p-8 text-center">
          <div class="w-16 h-16 mb-4 text-primary">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h7" />
            </svg>
          </div>
          <h3 class="text-2xl font-bold mb-2">Video Descriptions</h3>
          <p class="text-base-content/70 mb-6 max-w-md">
            This feature will generate alternative descriptions for your video, optimized for different platforms and audiences.
          </p>
          <div class="badge badge-primary badge-outline">Coming Soon</div>
        </div>
      </div>

      <!-- Social Media Tab -->
      <div v-else-if="activeTab === 'social'" class="tab-content">
        <div class="flex flex-col items-center justify-center p-8 text-center">
          <div class="w-16 h-16 mb-4 text-primary">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z" />
            </svg>
          </div>
          <h3 class="text-2xl font-bold mb-2">Social Media Content</h3>
          <p class="text-base-content/70 mb-6 max-w-md">
            This feature will generate social media posts based on your video content, tailored for different platforms like Twitter, LinkedIn, and Instagram.
          </p>
          <div class="badge badge-primary badge-outline">Coming Soon</div>
        </div>
      </div>

      <!-- Magic Chat Tab -->
      <div v-else-if="activeTab === 'chat'" class="tab-content">
        <div class="flex flex-col items-center justify-center p-8 text-center">
          <div class="w-16 h-16 mb-4 text-primary">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
            </svg>
          </div>
          <h3 class="text-2xl font-bold mb-2">Magic Chat</h3>
          <p class="text-base-content/70 mb-6 max-w-md">
            This feature will allow you to ask questions about the video content and get AI-powered answers, making it easier to understand and explore the material.
          </p>
          <div class="badge badge-primary badge-outline">Coming Soon</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    videoId: {
      type: String,
      required: true
    }
  },

  data() {
    return {
      activeTab: 'transcript',
      tabs: [
        { id: 'transcript', label: 'Transcript' },
        { id: 'summary', label: 'Summary' },
        { id: 'quotes', label: 'Quotes' },
        { id: 'descriptions', label: 'Descriptions' },
        { id: 'social', label: 'Social Media' },
        { id: 'chat', label: 'Magic Chat' }
      ],
      loading: false,
      error: '',
      transcript: [
        // Hardcoded fallback transcript
        { id: 1, start: 0, text: "the English philosopher bertran Russell" },
        { id: 2, start: 3, text: "once said the whole problem with the" },
        { id: 3, start: 6, text: "world is that fools and Fanatics are so" },
        { id: 4, start: 9, text: "sure of themselves while wiser people" },
        { id: 5, start: 12, text: "are so full of doubt in Psychology what" }
      ]
    };
  },

  watch: {
    videoId: {
      immediate: true,
      handler: 'fetchTranscript'
    }
  },

  mounted() {
    this.fetchTranscript();
  },

  methods: {
    setActiveTab(tabId) {
      this.activeTab = tabId;
      console.log(`Tab ${tabId} is now active`);
    },

    async fetchTranscript() {
      if (!this.videoId) {
        console.log('SimpleTabsContainer: No videoId provided, skipping transcript fetch');
        return;
      }

      this.loading = true;
      this.error = '';
      this.transcript = [];

      try {
        console.log('SimpleTabsContainer: Fetching transcript for video ID:', this.videoId);
        const API_URL = 'http://localhost:3000/api';

        console.log(`SimpleTabsContainer: Fetching from URL: ${API_URL}/transcript/${this.videoId}`);
        const response = await fetch(`${API_URL}/transcript/${this.videoId}`);

        console.log('SimpleTabsContainer: Response status:', response.status);

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const responseText = await response.text();
        console.log('SimpleTabsContainer: Raw response text length:', responseText.length);

        try {
          const data = JSON.parse(responseText);
          console.log('SimpleTabsContainer: API response parsed successfully');

          if (data && data.transcript && Array.isArray(data.transcript)) {
            console.log('SimpleTabsContainer: Transcript found, length:', data.transcript.length);
            this.transcript = data.transcript;
            console.log('SimpleTabsContainer: First few transcript items:', this.transcript.slice(0, 3));
          } else {
            console.error('SimpleTabsContainer: Invalid transcript data format');
            this.error = 'Invalid transcript data format';
          }
        } catch (parseError) {
          console.error('SimpleTabsContainer: Error parsing JSON:', parseError);
          this.error = 'Error parsing response: ' + parseError.message;

          // Fallback to hardcoded transcript for testing
          this.transcript = [
            { id: 1, start: 0, text: "the English philosopher bertran Russell" },
            { id: 2, start: 3, text: "once said the whole problem with the" },
            { id: 3, start: 6, text: "world is that fools and Fanatics are so" },
            { id: 4, start: 9, text: "sure of themselves while wiser people" },
            { id: 5, start: 12, text: "are so full of doubt in Psychology what" }
          ];
        }
      } catch (err) {
        console.error('SimpleTabsContainer: Error fetching transcript:', err);
        this.error = `Failed to fetch transcript: ${err.message}`;

        // Fallback to hardcoded transcript for testing
        this.transcript = [
          { id: 1, start: 0, text: "the English philosopher bertran Russell" },
          { id: 2, start: 3, text: "once said the whole problem with the" },
          { id: 3, start: 6, text: "world is that fools and Fanatics are so" },
          { id: 4, start: 9, text: "sure of themselves while wiser people" },
          { id: 5, start: 12, text: "are so full of doubt in Psychology what" }
        ];
      } finally {
        this.loading = false;
        console.log('SimpleTabsContainer: Transcript fetch completed. Has transcript:', this.transcript.length > 0);
      }
    },

    formatTime(seconds) {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const secs = Math.floor(seconds % 60);

      return [
        hours.toString().padStart(2, '0'),
        minutes.toString().padStart(2, '0'),
        secs.toString().padStart(2, '0')
      ].join(':');
    },

    seekToTime(time) {
      this.$emit('seek-to-time', time);
    }
  }
};
</script>

<style scoped>
.simple-tabs-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.tab-buttons {
  display: flex;
  flex-wrap: wrap;
}

.tab-button {
  background-color: #f0f0f0;
  transition: all 0.2s ease;
  cursor: pointer;
  color: #333; /* Dark text for light background */
}

.tab-button:hover {
  background-color: #e0e0e0;
}

.tab-button.active-tab {
  background-color: #4f46e5 !important; /* Exact blue color */
  color: white !important; /* White text */
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  font-weight: 600;
}

.tab-content-container {
  flex: 1;
  position: relative;
}

.tab-content {
  height: 100%;
  display: none; /* Hidden by default */
}

.tab-content[style*="display: block"] {
  display: block !important; /* Force display when v-show sets it */
}

.transcript-container {
  height: 100%;
}

.transcript-list {
  height: 450px;
  overflow-y: auto;
  border-radius: 0.5rem;
}

table {
  border-collapse: separate;
  border-spacing: 0;
}

tbody tr:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

td {
  padding: 0.75rem 0.5rem;
}

td:first-child button {
  transition: all 0.2s ease;
}

td:first-child button:hover {
  background-color: var(--p);
  color: white;
}
</style>
