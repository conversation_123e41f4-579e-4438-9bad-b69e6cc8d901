<template>
  <div class="min-h-screen bg-base-200 p-8">
    <div class="max-w-4xl mx-auto">
      <h1 class="text-3xl font-bold mb-6">Transcript Viewer</h1>

      <div class="card bg-base-100 shadow-md p-6">
        <!-- Video ID Input -->
        <div class="form-control mb-6">
          <label class="label">
            <span class="label-text">YouTube Video ID</span>
          </label>
          <div class="flex gap-2">
            <input
              v-model="videoId"
              type="text"
              placeholder="Enter YouTube video ID"
              class="input input-bordered flex-1"
            />
            <button class="btn btn-primary" @click="fetchTranscript">
              Fetch Transcript
            </button>
          </div>
        </div>

        <!-- Loading State -->
        <div v-if="loading" class="flex justify-center py-8">
          <span class="loading loading-spinner loading-lg text-primary"></span>
        </div>

        <!-- Error State -->
        <div v-else-if="error" class="alert alert-error mb-4">
          <span>{{ error }}</span>
        </div>

        <!-- Empty State -->
        <div v-else-if="!transcript || transcript.length === 0" class="alert alert-warning mb-4">
          <span>No transcript data available. Enter a YouTube video ID and click "Fetch Transcript".</span>
        </div>

        <!-- Success State -->
        <div v-else>
          <div class="alert alert-success mb-4">
            <span>Transcript loaded successfully! {{ transcript.length }} items found.</span>
          </div>

          <!-- Transcript Display -->
          <div class="bg-base-100 p-4 rounded-lg border border-base-300 max-h-[600px] overflow-y-auto">
            <div v-for="item in transcript" :key="item.id" class="mb-4 p-2 border-b border-base-200">
              <div class="font-medium text-primary mb-1">{{ item.formattedStart || formatTime(item.start) }}</div>
              <p>{{ item.text }}</p>
            </div>
          </div>

          <!-- Raw Data Display -->
          <div class="mt-6">
            <h3 class="text-xl font-bold mb-2">Raw Transcript Data</h3>
            <div class="bg-base-200 p-4 rounded-lg overflow-x-auto">
              <pre>{{ JSON.stringify(transcript, null, 2) }}</pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const videoId = ref('TT81fe2IobI'); // Default to one of the example videos
const loading = ref(false);
const error = ref('');
const transcript = ref([]);

async function fetchTranscript() {
  if (!videoId.value) {
    error.value = 'Please enter a YouTube video ID';
    return;
  }

  loading.value = true;
  error.value = '';
  transcript.value = [];

  try {
    console.log('TranscriptPage: Fetching transcript for video ID:', videoId.value);
    const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000/api';

    const response = await fetch(`${API_URL}/transcript/${videoId.value}`);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log('TranscriptPage: API response:', data);

    if (data && data.transcript && Array.isArray(data.transcript)) {
      console.log('TranscriptPage: Transcript found, length:', data.transcript.length);
      transcript.value = data.transcript;
    } else {
      console.error('TranscriptPage: Invalid transcript data format:', data);
      error.value = 'Invalid transcript data format';
    }
  } catch (err) {
    console.error('TranscriptPage: Error fetching transcript:', err);
    error.value = `Failed to fetch transcript: ${err.message}`;
  } finally {
    loading.value = false;
    console.log('TranscriptPage: Fetch completed. Has transcript:', transcript.value.length > 0);
  }
}

function formatTime(seconds) {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  return [
    hours.toString().padStart(2, '0'),
    minutes.toString().padStart(2, '0'),
    secs.toString().padStart(2, '0')
  ].join(':');
}

// Fetch transcript on component mount
fetchTranscript();
</script>
