<template>
  <div class="tabs-container">
    <!-- Simple Tab Navigation -->
    <div class="tabs tabs-boxed mb-4">
      <a
        class="tab"
        :class="{ 'tab-active': activeTab === 'transcript' }"
        @click="activeTab = 'transcript'"
      >
        Transcript
      </a>
    </div>

    <!-- Tab Content -->
    <div class="tab-content">
      <!-- Only show the transcript tab for now -->
      <div v-if="activeTab === 'transcript'" class="transcript-container">
        <div v-if="loading" class="flex justify-center py-8">
          <span class="loading loading-spinner loading-lg text-primary"></span>
        </div>

        <div v-else-if="error" class="alert alert-error mb-4">
          <span>{{ error }}</span>
        </div>

        <div v-else-if="!transcript || transcript.length === 0" class="alert alert-warning mb-4">
          <span>No transcript data available for this video.</span>
        </div>

        <div v-else>
          <!-- Debug info -->
          <div class="bg-info text-info-content p-4 rounded-lg mb-4">
            <h3 class="font-bold">Debug Info:</h3>
            <p>Transcript items: {{ transcript.length }}</p>
            <p>First item: {{ transcript[0] ? JSON.stringify(transcript[0]) : 'None' }}</p>
          </div>

          <!-- Simple transcript display -->
          <div class="transcript-list bg-base-100 p-4 rounded-lg border border-base-300">
            <div v-for="(item, index) in transcript" :key="item.id || index" class="mb-4 p-2 border-b border-base-200">
              <div class="font-medium text-primary mb-1">{{ formatTime(item.start) }}</div>
              <p>{{ item.text }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue';

const props = defineProps({
  videoId: {
    type: String,
    required: true
  },
  currentTime: {
    type: Number,
    default: 0
  }
});

defineEmits(['seek-to-time']);

const activeTab = ref('transcript');
const loading = ref(false);
const error = ref('');
const transcript = ref([]);

// Fetch transcript when component mounts or videoId changes
watch(() => props.videoId, fetchTranscript, { immediate: true });

// Also fetch on component mount
onMounted(fetchTranscript);

async function fetchTranscript() {
  if (!props.videoId) {
    console.log('No videoId provided, skipping transcript fetch');
    return;
  }

  loading.value = true;
  error.value = '';
  transcript.value = [];

  try {
    console.log('Fetching transcript for video ID:', props.videoId);
    const API_URL = 'http://localhost:3000/api';

    console.log(`Fetching from URL: ${API_URL}/transcript/${props.videoId}`);
    const response = await fetch(`${API_URL}/transcript/${props.videoId}`);

    console.log('Response status:', response.status);
    console.log('Response headers:', response.headers);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const responseText = await response.text();
    console.log('Raw response text:', responseText.substring(0, 200) + '...');

    let data;
    try {
      data = JSON.parse(responseText);
      console.log('API response parsed successfully:', data);

      if (data && data.transcript && Array.isArray(data.transcript)) {
        console.log('Transcript found, length:', data.transcript.length);
        transcript.value = data.transcript;
        console.log('First few transcript items:', transcript.value.slice(0, 3));
      } else {
        console.error('Invalid transcript data format:', data);
        error.value = 'Invalid transcript data format';
      }
    } catch (parseError) {
      console.error('Error parsing JSON:', parseError);
      error.value = 'Error parsing response: ' + parseError.message;
    }
  } catch (err) {
    console.error('Error fetching transcript:', err);
    error.value = `Failed to fetch transcript: ${err.message}`;
  } finally {
    loading.value = false;
    console.log('Transcript fetch completed. Has transcript:', transcript.value.length > 0);
  }
}

function formatTime(seconds) {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  return [
    hours.toString().padStart(2, '0'),
    minutes.toString().padStart(2, '0'),
    secs.toString().padStart(2, '0')
  ].join(':');
}
</script>

<style scoped>
.tabs-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.tab-content {
  flex: 1;
}

.transcript-container {
  height: 100%;
}

.transcript-list {
  height: 500px;
  overflow-y: auto;
}
</style>
