<script setup>
import { ref, computed } from 'vue';
import axios from 'axios';
import MinimalSocialProof from './MinimalSocialProof.vue';
import UserTrust from './UserTrust.vue';

const props = defineProps({
  examplesCollapsed: {
    type: Boolean,
    default: false
  },
  userVideos: {
    type: Array,
    default: () => []
  },
  userVideoDetails: {
    type: Object,
    default: () => ({})
  },
  selectedVideoId: {
    type: String,
    default: ''
  }
});

const videoUrl = ref('');
const error = ref('');
const videoId = ref('');

// Emit events to parent component
const emit = defineEmits(['video-selected', 'toggle-examples', 'play-video', 'add-video']);

// Debug: Log component mount
console.log('🚀 VideoInput component mounted');

// Validate YouTube URL
const isValidUrl = computed(() => {
  if (!videoUrl.value) return false;

  // Simple YouTube URL validation
  const youtubeRegex = /^(https?:\/\/)?(www\.)?(youtube\.com|youtu\.?be)\/.+$/;
  return youtubeRegex.test(videoUrl.value);
});

// Extract video ID from URL
const extractVideoId = (url) => {
  const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
  const match = url.match(regExp);
  return (match && match[2].length === 11) ? match[2] : null;
};

// Process the URL when button is clicked
const processUrl = () => {
  if (!isValidUrl.value) {
    error.value = 'Please enter a valid YouTube URL';
    return;
  }

  const extractedId = extractVideoId(videoUrl.value);
  if (!extractedId) {
    error.value = 'Could not extract video ID from URL';
    return;
  }

  error.value = '';

  // Emit add-video event with the extracted ID
  console.log("➕ VideoInput: Emitting add-video with ID:", extractedId);
  emit('add-video', extractedId);
  console.log("➕ VideoInput: add-video event emitted successfully");

  // Clear the input field
  videoUrl.value = '';
};

// Toggle examples visibility
const toggleExamples = () => {
  emit('toggle-examples');
};

// Select an example video
const selectExampleVideo = (videoId) => {
  console.log(`🎬 VideoInput: Selecting example video: ${videoId}`);
  console.log(`🎬 VideoInput: Emitting 'video-selected' event with videoId:`, videoId);
  emit('video-selected', videoId);
  console.log(`🎬 VideoInput: Event emitted successfully`);
};
</script>

<template>
  <div class="w-full mx-auto text-center">
    <h1 class="text-4xl md:text-6xl font-bold mb-4 text-primary">Transcribe YouTube videos and turn them into content</h1>

    <h2 class="text-xl md:text-2xl mb-6 text-base-content/80 max-w-3xl mx-auto">
      Download transcripts, get translations, generate summaries, write descriptions, social posts or get study cards
    </h2>

    <!-- Feature Checkmarks -->
    <div class="flex flex-col sm:flex-row items-center justify-center gap-4 mb-8">
      <div class="flex items-center gap-2">
        <div class="w-6 h-6 rounded-full bg-success flex items-center justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
          </svg>
        </div>
        <span class="text-base font-medium text-base-content/80">No sign-up required</span>
      </div>

      <div class="flex items-center gap-2">
        <div class="w-6 h-6 rounded-full bg-success flex items-center justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
          </svg>
        </div>
        <span class="text-base font-medium text-base-content/80">Forever FREE</span>
      </div>

      <div class="flex items-center gap-2">
        <div class="w-6 h-6 rounded-full bg-success flex items-center justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
          </svg>
        </div>
        <span class="text-base font-medium text-base-content/80">Fast results</span>
      </div>
    </div>

    <div class="card bg-base-100 shadow-md p-6 mb-6">
      <!-- Input Field -->
      <div class="form-control w-full max-w-2xl mx-auto mb-4">
        <div class="flex flex-col sm:flex-row gap-2">
          <input
            type="text"
            v-model="videoUrl"
            placeholder="Paste YouTube URL here"
            @keyup.enter="processUrl"
            class="input input-bordered input-lg w-full focus:outline-primary"
          />
          <button
            class="btn btn-primary btn-lg font-bold whitespace-nowrap"
            @click="processUrl"
            style="font-size: 16px; font-weight: 600;"
          >
            Transcribe now
          </button>
        </div>

        <div class="flex justify-center items-center mt-3 px-1">
          <UserTrust />
        </div>

        <div v-if="error" class="alert alert-error mt-4">
          <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
          <span>{{ error }}</span>
        </div>
      </div>

      <!-- Example Videos Section -->
      <div class="mt-4 border-t border-base-300 pt-4">
        <div class="flex justify-center items-center gap-2 mb-2">
          <button
            class="btn btn-circle btn-xs btn-ghost"
            @click="toggleExamples"
            aria-label="Toggle examples"
          >
            <svg
              class="w-4 h-4 transition-transform duration-300"
              :class="{ 'rotate-180': examplesCollapsed }"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path d="M7.41 15.41L12 10.83l4.59 4.58L18 14l-6-6-6 6z" fill="currentColor"></path>
            </svg>
          </button>
          <span
            v-if="examplesCollapsed"
            class="text-base font-medium text-primary cursor-pointer hover:text-primary/80 transition-colors px-3 py-1 rounded-md bg-primary/10 hover:bg-primary/20"
            @click="toggleExamples"
          >
            Show example videos
          </span>
        </div>

        <div v-if="!examplesCollapsed" class="transition-all duration-300">
          <h3 class="text-lg font-semibold mb-3">Example transcripts</h3>
          <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
            <!-- Example Video 1 -->
            <div
              class="card bg-base-200 shadow-md hover:shadow-lg transition-all cursor-pointer border border-primary/10"
              :class="{ 'ring-2 ring-primary': selectedVideoId === 'TT81fe2IobI' }"
              @click="selectExampleVideo('TT81fe2IobI')"
            >
              <figure class="relative">
                <img
                  src="https://i.ytimg.com/vi/TT81fe2IobI/hqdefault.jpg"
                  alt="The Dunning-Kruger Effect"
                  class="w-full aspect-video object-cover"
                >
                <div class="absolute inset-0 flex items-center justify-center bg-black/30 opacity-0 hover:opacity-100 transition-opacity">
                  <div class="bg-primary rounded-full p-1.5">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M8 5v14l11-7z"/>
                    </svg>
                  </div>
                </div>

              </figure>
              <div class="card-body p-2">
                <h3 class="text-xs font-medium truncate" title="Why Stupid People Think They're Smart [The Dunning-Kruger Effect]">
                  Why Stupid People Think They're Smart [The Dunning-Kruger Effect]
                </h3>
              </div>
            </div>

            <!-- Example Video 2 -->
            <div
              class="card bg-base-200 shadow-md hover:shadow-lg transition-all cursor-pointer border border-primary/10"
              :class="{ 'ring-2 ring-primary': selectedVideoId === 'pqWUuYTcG-o' }"
              @click="selectExampleVideo('pqWUuYTcG-o')"
            >
              <figure class="relative">
                <img
                  src="https://i.ytimg.com/vi/pqWUuYTcG-o/hqdefault.jpg"
                  alt="The Underdog: From His Parent's Basement to $25M"
                  class="w-full aspect-video object-cover"
                >
                <div class="absolute inset-0 flex items-center justify-center bg-black/30 opacity-0 hover:opacity-100 transition-opacity">
                  <div class="bg-primary rounded-full p-1.5">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M8 5v14l11-7z"/>
                    </svg>
                  </div>
                </div>

              </figure>
              <div class="card-body p-2">
                <h3 class="text-xs font-medium truncate" title="The Underdog: From His Parent's Basement to $25M">
                  The Underdog: From His Parent's Basement to $25M
                </h3>
              </div>
            </div>

            <!-- Example Video 3 -->
            <div
              class="card bg-base-200 shadow-md hover:shadow-lg transition-all cursor-pointer border border-primary/10"
              :class="{ 'ring-2 ring-primary': selectedVideoId === 'KpVPST_P4W8' }"
              @click="selectExampleVideo('KpVPST_P4W8')"
            >
              <figure class="relative">
                <img
                  src="https://i.ytimg.com/vi/KpVPST_P4W8/hqdefault.jpg"
                  alt="Intro to Quantum Computing"
                  class="w-full aspect-video object-cover"
                >
                <div class="absolute inset-0 flex items-center justify-center bg-black/30 opacity-0 hover:opacity-100 transition-opacity">
                  <div class="bg-primary rounded-full p-1.5">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M8 5v14l11-7z"/>
                    </svg>
                  </div>
                </div>
              </figure>
              <div class="card-body p-2">
                <h3 class="text-xs font-medium truncate" title="Intro to Quantum Computing">
                  Intro to Quantum Computing
                </h3>
              </div>
            </div>

            <!-- Example Video 4 -->
            <div
              class="card bg-base-200 shadow-md hover:shadow-lg transition-all cursor-pointer border border-primary/10"
              :class="{ 'ring-2 ring-primary': selectedVideoId === 'arj7oStGLkU' }"
              @click="selectExampleVideo('arj7oStGLkU')"
            >
              <figure class="relative">
                <img
                  src="https://i.ytimg.com/vi/arj7oStGLkU/hqdefault.jpg"
                  alt="Inside the mind of a master procrastinator | Tim Urban"
                  class="w-full aspect-video object-cover"
                >
                <div class="absolute inset-0 flex items-center justify-center bg-black/30 opacity-0 hover:opacity-100 transition-opacity">
                  <div class="bg-primary rounded-full p-1.5">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M8 5v14l11-7z"/>
                    </svg>
                  </div>
                </div>
              </figure>
              <div class="card-body p-2">
                <h3 class="text-xs font-medium truncate" title="Inside the mind of a master procrastinator | Tim Urban">
                  Inside the mind of a master procrastinator | Tim Urban
                </h3>
              </div>
            </div>

            <!-- Example Video 5 -->
            <div
              class="card bg-base-200 shadow-md hover:shadow-lg transition-all cursor-pointer border border-primary/10"
              :class="{ 'ring-2 ring-primary': selectedVideoId === 'Gv2fzC96Z40' }"
              @click="selectExampleVideo('Gv2fzC96Z40')"
            >
              <figure class="relative">
                <img
                  src="https://i.ytimg.com/vi/Gv2fzC96Z40/hqdefault.jpg"
                  alt="The Underdog: From His Parent's Basement to $25M"
                  class="w-full aspect-video object-cover"
                >
                <div class="absolute inset-0 flex items-center justify-center bg-black/30 opacity-0 hover:opacity-100 transition-opacity">
                  <div class="bg-primary rounded-full p-1.5">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M8 5v14l11-7z"/>
                    </svg>
                  </div>
                </div>
              </figure>
              <div class="card-body p-2">
                <h3 class="text-xs font-medium truncate" title="The Underdog: From His Parent's Basement to $25M">
                  The Underdog: From His Parent's Basement to $25M
                </h3>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
