<template>
  <div class="tabs-container">
    <!-- Tab Navigation -->
    <div class="tab-buttons mb-4 border-b pb-2">
      <button
        v-for="tab in tabs"
        :key="tab.id"
        class="tab-button px-4 py-2 rounded-md font-medium mr-2"
        :class="{ 'active-tab': activeTab === tab.id }"
        @click="setActiveTab(tab.id)"
      >
        <span class="tab-icon" v-html="tab.icon"></span>
        <span class="tab-text">{{ tab.label }}</span>
      </button>
    </div>



    <!-- Tab Content -->
    <div class="tab-content-container">
      <!-- Transcript Tab -->
      <template v-if="activeTab === 'transcript'">
        <div class="transcript-container">
          <div class="flex justify-between items-start mb-6">
            <div>
              <h2 class="text-xl font-bold pt-4">Video Transcript</h2>
              <p class="text-sm text-base-content/70 mt-1">Click on any timestamp to jump to that point in the video.</p>
            </div>

            <div class="flex flex-wrap items-center gap-1.5 mt-2">
              <!-- Download Button -->
              <div class="dropdown dropdown-end">
                <div tabindex="0" role="button" class="btn btn-xs sm:btn-sm btn-primary gap-1 sm:gap-2 transcript-control-btn">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 sm:h-4 sm:w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                  </svg>
                  <span class="hidden xs:inline sm:inline">Download</span>
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-2.5 w-2.5 sm:h-3 sm:w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
                <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52">
                  <li><a @click="downloadTranscript('txt')">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    Text (.txt)
                  </a></li>
                  <li><a @click="downloadTranscript('srt')">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2" />
                    </svg>
                    Subtitles (.srt)
                  </a></li>
                  <li><a @click="downloadTranscript('vtt')">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                    </svg>
                    WebVTT (.vtt)
                  </a></li>
                  <li><a @click="downloadTranscript('doc')">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    Document (.rtf)
                  </a></li>
                </ul>
              </div>

              <!-- Copy Button -->
              <button @click="copyTranscript" class="btn btn-xs sm:btn-sm btn-primary gap-1 sm:gap-2 transcript-control-btn">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 sm:h-4 sm:w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                </svg>
                <span class="hidden xs:inline sm:inline">Copy</span>
              </button>

              <!-- Language Selector (Flag + Name + Dropdown) -->
              <div v-if="availableLanguages.length > 0" class="language-dropdown">
                <div class="dropdown dropdown-end">
                  <label tabindex="0" class="btn btn-xs sm:btn-sm gap-1 sm:gap-2 transcript-control-btn">
                    <span v-if="selectedLanguage.flag" class="flag-icon text-sm sm:text-base">{{ selectedLanguage.flag }}</span>
                    <span v-else class="text-sm sm:text-base">🌐</span>
                    <span class="hidden xs:inline sm:inline text-xs sm:text-sm">{{ selectedLanguage.name || 'Select Language' }}</span>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-2.5 w-2.5 sm:h-4 sm:w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                    </svg>
                  </label>
                  <div tabindex="0" class="dropdown-content z-[1] shadow bg-base-100 rounded-box w-80 language-dropdown">
                    <!-- Search input -->
                    <div class="border-b border-base-300">
                      <div class="relative px-4 py-2">
                        <input
                          type="text"
                          v-model="languageSearch"
                          placeholder="Search languages..."
                          class="input input-sm input-bordered w-full pr-8"
                          @click.stop
                        />
                        <button
                          v-if="languageSearch"
                          @click="languageSearch = ''"
                          class="absolute right-6 top-1/2 -translate-y-1/2 text-base-content/50 hover:text-base-content"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        </button>
                      </div>
                    </div>

                    <!-- Language list -->
                    <div class="max-h-60 overflow-y-auto py-2 w-full">
                      <ul class="menu menu-compact w-full">
                        <li v-for="lang in filteredLanguages" :key="lang.code"
                            @click="changeLanguage(lang.code)"
                            class="hover:bg-base-200 cursor-pointer w-full"
                            :class="{
                              'bg-primary/10': selectedLanguage.code === lang.code
                            }">
                          <a class="flex items-center gap-2 px-4 py-2 w-full">
                            <span class="flag-icon w-6 text-center">{{ lang.flag || '🌐' }}</span>
                            <span class="flex-grow">{{ lang.name }}</span>
                            <span v-if="lang.isAvailable && lang.trackKind === 'standard'"
                                  class="badge badge-xs badge-primary whitespace-nowrap">Manual</span>
                            <span v-else-if="lang.trackKind === 'auto-generated' || lang.isAutoGenerated"
                                  class="badge badge-xs badge-secondary whitespace-nowrap">Auto generated</span>
                          </a>
                        </li>
                        <li v-if="filteredLanguages.length === 0" class="px-4 py-2 text-base-content/50 text-center w-full">
                          No languages found
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Enhanced loading state with progress indicator -->
          <div v-if="loading">
            <TranscriptProgress
              :visible="loading"
              :progress="transcriptProgress"
            />
          </div>

          <div v-else-if="error" class="alert alert-error mb-4">
            <span>{{ error }}</span>
          </div>

          <div v-else-if="!loading && (!transcript || transcript.length === 0)" class="alert alert-warning mb-4">
            <span>Sorry, no transcript could be found for this video. The video may not have subtitles available.</span>
          </div>

          <div v-else class="transcript-list bg-base-100 p-4 rounded-lg border border-base-300">
            <table class="w-full">
              <thead>
                <tr>
                  <th class="text-left pb-2 w-24">Timestamp</th>
                  <th class="text-left pb-2">Text</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(item, index) in transcript" :key="index" class="border-b border-base-200 hover:bg-base-200">
                  <td class="py-2 pr-4 font-medium text-primary whitespace-nowrap">
                    <button
                      class="px-2 py-1 rounded hover:bg-primary hover:text-white transition-colors"
                      @click="seekToTime(item.start)"
                    >
                      {{ item.formattedStart || formatTime(item.start) }}
                    </button>
                  </td>
                  <td class="py-2">{{ item.text }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </template>

      <!-- Summary Tab -->
      <template v-else-if="activeTab === 'summary'">
        <div class="summary-container">
          <div class="flex justify-between items-center mb-4">
            <div>
              <h2 class="text-xl font-bold pt-4">YouTube Summary Generator</h2>
              <p class="text-sm text-base-content/70 mt-1">Accurate YouTube video summaries</p>
            </div>
          </div>

          <!-- Loading State -->
          <div v-if="summaryLoading" class="flex flex-col items-center justify-center py-12">
            <div class="loading loading-spinner loading-lg text-primary mb-4"></div>
            <p class="text-base-content/70">Generating AI summary...</p>
          </div>

          <!-- Error State -->
          <div v-else-if="summaryError" class="alert alert-error mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>{{ summaryError }}</span>
          </div>

          <!-- Summary Content -->
          <div v-else-if="summary" class="summary-content">
            <div class="bg-base-100 p-6 rounded-lg border border-base-300">
              <div class="flex justify-between items-center mb-4">
                <span class="text-sm font-medium text-base-content/70">Summary Content</span>
                <button @click="copySummary" class="btn btn-sm btn-primary" style="font-size: 14px; font-weight: 500;">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                  </svg>
                  Copy
                </button>
              </div>
              <div class="prose prose-sm max-w-none" v-html="renderedSummary"></div>
            </div>

            <!-- Summary Info -->
            <div class="mt-4 text-xs text-base-content/60 flex justify-between items-center">
              <span>Generated from {{ summaryData?.transcriptLength || 0 }} transcript segments</span>
              <span>Language: {{ summaryData?.language || 'Unknown' }}</span>
            </div>
          </div>

          <!-- Empty State -->
          <div v-else class="flex flex-col items-center justify-center py-12">
            <div class="w-16 h-16 mb-4 text-primary">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <h3 class="text-xl font-bold mb-2">No Summary Available</h3>
            <p class="text-base-content/70 mb-4 text-center max-w-md">
              Select a video to automatically generate an AI-powered summary of its content.
            </p>
          </div>
        </div>
      </template>

      <!-- Quotes Tab -->
      <template v-else-if="activeTab === 'quotes'">
        <div class="quotes-container">
          <div class="flex justify-between items-center mb-4">
            <div>
              <h2 class="text-xl font-bold pt-4">Key Quotes</h2>
              <p class="text-sm text-base-content/70 mt-1">Extract the most sharable quotes from any YouTube video</p>
            </div>
          </div>

          <!-- Loading State -->
          <div v-if="quotesLoading" class="flex flex-col items-center justify-center py-12">
            <div class="loading loading-spinner loading-lg text-primary mb-4"></div>
            <p class="text-base-content/70">Extracting key quotes...</p>
          </div>

          <!-- Error State -->
          <div v-else-if="quotesError" class="alert alert-error mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>{{ quotesError }}</span>
          </div>

          <!-- Quotes Content -->
          <div v-else-if="quotes" class="quotes-content">
            <div class="bg-base-100 p-6 rounded-lg border border-base-300">
              <div class="flex justify-between items-center mb-4">
                <span class="text-sm font-medium text-base-content/70">Quotes Content</span>
                <button @click="copyQuotes" class="btn btn-sm btn-primary" style="font-size: 14px; font-weight: 500;">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                  </svg>
                  Copy
                </button>
              </div>
              <div class="prose prose-sm max-w-none" v-html="renderedQuotes"></div>
            </div>

            <!-- Quotes Info -->
            <div class="mt-4 text-xs text-base-content/60 flex justify-between items-center">
              <span>Extracted from {{ quotesData?.transcriptLength || 0 }} transcript segments</span>
              <span>Language: {{ quotesData?.language || 'Unknown' }}</span>
            </div>
          </div>

          <!-- Empty State -->
          <div v-else class="flex flex-col items-center justify-center py-12">
            <div class="w-16 h-16 mb-4 text-primary">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
              </svg>
            </div>
            <h3 class="text-xl font-bold mb-2">No Quotes Available</h3>
            <p class="text-base-content/70 mb-4 text-center max-w-md">
              Select a video to automatically extract key quotes and memorable statements.
            </p>
          </div>
        </div>
      </template>

      <!-- Descriptions Tab -->
      <template v-else-if="activeTab === 'descriptions'">
        <div class="descriptions-container">
          <div class="flex justify-between items-center mb-4">
            <div>
              <h2 class="text-xl font-bold pt-4">YouTube Description & Timestamps</h2>
              <p class="text-sm text-base-content/70 mt-1">SEO-optimized description with clickable timestamps</p>
            </div>
          </div>



          <!-- Loading State -->
          <div v-if="descriptionLoading" class="flex flex-col items-center justify-center py-12">
            <div class="loading loading-spinner loading-lg text-primary mb-4"></div>
            <p class="text-base-content/70">Generating YouTube description and timestamps...</p>
          </div>

          <!-- Error State -->
          <div v-else-if="descriptionError" class="alert alert-error mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>{{ descriptionError }}</span>
          </div>

          <!-- Description Content -->
          <div v-else-if="description" class="description-content">
            <div class="bg-base-100 p-6 rounded-lg border border-base-300">
              <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold">Generated Description</h3>
                <div class="flex gap-2">
                  <button @click="copyDescription" class="btn btn-sm btn-primary" style="font-size: 14px; font-weight: 500;">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                    </svg>
                    Copy
                  </button>
                </div>
              </div>
              <div class="prose prose-sm max-w-none" v-html="renderedDescription" @click="handleDescriptionClick"></div>
            </div>

            <!-- Description Info -->
            <div class="mt-4 text-xs text-base-content/60 flex justify-between items-center">
              <span>Generated from {{ descriptionData?.transcriptLength || 0 }} transcript segments</span>
              <span>Language: {{ descriptionData?.language || 'Unknown' }}</span>
            </div>
          </div>

          <!-- Empty State -->
          <div v-else class="flex flex-col items-center justify-center py-12">
            <div class="w-16 h-16 mb-4 text-primary">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h7" />
              </svg>
            </div>
            <h3 class="text-xl font-bold mb-2">No Description Available</h3>
            <p class="text-base-content/70 mb-4 text-center max-w-md">
              Select a video to automatically generate an SEO-optimized YouTube description with timestamps.
            </p>
          </div>
        </div>
      </template>

      <!-- Anki Flash Cards Tab -->
      <template v-else-if="activeTab === 'anki'">
        <div class="anki-cards-container">
          <div class="flex justify-between items-center mb-4">
            <div>
              <h2 class="text-xl font-bold pt-4">Anki Flash Cards</h2>
              <p class="text-sm text-base-content/70 mt-1">
                AI-generated study cards •
                <span v-if="ankiCards.length > 0">{{ankiCards.length}} cards • {{selectedCards.length}} selected</span>
                <span v-else>Ready to generate</span>
              </p>
            </div>
            <div class="flex gap-2" v-if="ankiCards.length > 0">
              <select v-model="filterType" class="select select-sm select-bordered">
                <option value="all">All Types</option>
                <option value="qa">Q&A</option>
                <option value="process">Process</option>
                <option value="truefalse">True/False</option>
              </select>
            </div>
          </div>

          <!-- Loading State -->
          <div v-if="ankiCardsLoading" class="flex flex-col items-center justify-center py-12">
            <div class="loading loading-spinner loading-lg text-primary mb-4"></div>
            <p class="text-base-content/70">Generating Anki flash cards...</p>
            <p class="text-xs text-base-content/50 mt-2">Analyzing content and creating study materials</p>
          </div>

          <!-- Error State -->
          <div v-else-if="ankiCardsError" class="flex flex-col items-center justify-center py-12">
            <div class="text-error mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 class="text-xl font-bold mb-2 text-error">Generation Failed</h3>
            <p class="text-base-content/70 mb-4 text-center max-w-md">{{ ankiCardsError }}</p>
            <button @click="fetchAnkiCards" class="btn btn-primary" style="font-size: 14px; font-weight: 500;">Try Again</button>
          </div>

          <!-- Cards Content -->
          <div v-else-if="ankiCards.length > 0" class="anki-cards-content">
            <!-- Bulk Actions -->
            <div class="flex gap-2 mb-4 p-3 bg-base-100 rounded-lg border border-base-300">
              <button @click="selectAll" class="btn btn-sm btn-outline" style="font-size: 14px; font-weight: 500;">Select All</button>
              <button @click="deselectAll" class="btn btn-sm btn-outline" style="font-size: 14px; font-weight: 500;">Deselect All</button>
              <div class="flex-1"></div>
              <button @click="exportToAnki" class="btn btn-sm btn-primary" :disabled="selectedCards.length === 0" style="font-size: 14px; font-weight: 500;">
                📥 Export to Anki ({{selectedCards.length}} cards)
              </button>
            </div>

            <!-- Cards Grid -->
            <div class="space-y-3 max-h-96 overflow-y-auto">
              <div v-for="card in filteredCards" :key="card.id"
                   class="card bg-base-100 border border-base-300 hover:border-primary/30 transition-colors">
                <div class="card-body p-4">
                  <div class="flex items-start gap-3">
                    <input type="checkbox" v-model="card.selected" class="checkbox checkbox-primary mt-1">
                    <div class="flex-1">
                      <div class="flex items-center gap-2 mb-2">
                        <span class="badge badge-sm" :class="getTypeColor(card.type)">{{card.type}}</span>
                        <span class="badge badge-outline badge-sm">{{card.difficulty}}</span>
                        <button @click="seekToTime(card.timestamp)"
                                class="text-primary text-sm hover:underline flex items-center gap-1">
                          ▶ {{formatTime(card.timestamp)}}
                        </button>
                      </div>
                      <div class="mb-2">
                        <strong class="text-sm">Q:</strong>
                        <span v-if="!card.editing" class="text-sm">{{card.question}}</span>
                        <input v-else v-model="card.question" class="input input-sm w-full mt-1" placeholder="Question">
                      </div>
                      <div class="mb-2">
                        <strong class="text-sm">A:</strong>
                        <span v-if="!card.editing" class="text-sm">{{card.answer}}</span>
                        <textarea v-else v-model="card.answer" class="textarea textarea-sm w-full mt-1" placeholder="Answer" rows="2"></textarea>
                      </div>
                      <div v-if="card.context" class="text-xs text-base-content/60 italic">
                        Context: {{card.context}}
                      </div>
                    </div>
                    <div class="flex flex-col gap-1">
                      <button @click="toggleEdit(card)" class="btn btn-xs btn-outline" style="font-size: 14px; font-weight: 500;">
                        {{card.editing ? '💾' : '✏️'}}
                      </button>
                      <button @click="removeCard(card)" class="btn btn-xs btn-error btn-outline" style="font-size: 14px; font-weight: 500;">
                        🗑️
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Cards Info -->
            <div class="mt-4 text-xs text-base-content/60 flex justify-between items-center">
              <span>Generated from {{ ankiCardsData?.transcriptLength || 0 }} transcript segments</span>
              <span>Video Duration: {{ ankiCardsData?.videoDuration || 'Unknown' }}</span>
            </div>
          </div>

          <!-- Empty State -->
          <div v-else class="flex flex-col items-center justify-center py-12">
            <div class="text-base-content/40 mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
              </svg>
            </div>
            <h3 class="text-xl font-bold mb-2">No Flash Cards Available</h3>
            <p class="text-base-content/70 mb-4 text-center max-w-md">
              Select a video to automatically generate Anki flash cards for studying the content.
            </p>
          </div>
        </div>
      </template>

      <!-- Social Media Tab -->
      <template v-else-if="activeTab === 'social'">
        <div class="social-media-container">
          <div class="flex justify-between items-center mb-4">
            <div>
              <h2 class="text-xl font-bold pt-4">Social Media Posts</h2>
              <p class="text-sm text-base-content/70 mt-1">Platform-optimized posts for Twitter, Instagram, and LinkedIn</p>
            </div>
          </div>

          <!-- Loading State -->
          <div v-if="socialMediaLoading" class="flex flex-col items-center justify-center py-12">
            <div class="loading loading-spinner loading-lg text-primary mb-4"></div>
            <p class="text-base-content/70">Generating social media posts...</p>
          </div>

          <!-- Error State -->
          <div v-else-if="socialMediaError" class="alert alert-error mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>{{ socialMediaError }}</span>
          </div>

          <!-- Social Media Content -->
          <div v-else-if="socialMediaPosts" class="social-media-content">
            <div class="bg-base-100 p-6 rounded-lg border border-base-300">
              <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold">Generated Social Media Posts</h3>
                <div class="flex gap-2">
                  <button @click="copySocialMedia" class="btn btn-sm btn-primary" style="font-size: 14px; font-weight: 500;">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                    </svg>
                    Copy
                  </button>
                </div>
              </div>
              <div class="prose prose-sm max-w-none" v-html="renderedSocialMedia"></div>
            </div>

            <!-- Social Media Info -->
            <div class="mt-4 text-xs text-base-content/60 flex justify-between items-center">
              <span>Generated from {{ socialMediaData?.transcriptLength || 0 }} transcript segments</span>
              <span>Language: {{ socialMediaData?.language || 'Unknown' }}</span>
            </div>
          </div>

          <!-- Empty State -->
          <div v-else class="flex flex-col items-center justify-center py-12">
            <div class="w-16 h-16 mb-4 text-primary">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z" />
              </svg>
            </div>
            <h3 class="text-xl font-bold mb-2">No Social Media Posts Available</h3>
            <p class="text-base-content/70 mb-4 text-center max-w-md">
              Select a video to automatically generate platform-optimized social media posts for Twitter, Instagram, and LinkedIn.
            </p>
          </div>
        </div>
      </template>


    </div>
  </div>
</template>

<script>
import { getTranscript, getAvailableLanguages } from '../services/transcriptService.js';
import TranscriptProgress from './TranscriptProgress.vue';

export default {
  components: {
    TranscriptProgress
  },
  props: {
    videoId: {
      type: String,
      required: true
    }
  },

  data() {
    return {
      activeTab: 'transcript',
      tabs: [
        {
          id: 'transcript',
          label: 'Transcript',
          icon: '<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" /></svg>'
        },
        {
          id: 'summary',
          label: 'Summary',
          icon: '<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" /></svg>'
        },
        {
          id: 'quotes',
          label: 'Quotes',
          icon: '<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" /></svg>'
        },
        {
          id: 'descriptions',
          label: 'Description',
          icon: '<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" /></svg>'
        },
        {
          id: 'social',
          label: 'Social Media',
          icon: '<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2" /></svg>'
        },
        {
          id: 'anki',
          label: 'Anki Flash Cards',
          icon: '<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" /></svg>'
        }
      ],
      loading: false,
      error: '',
      transcript: [],
      transcriptProgress: null, // Progress tracking for transcript fetching
      availableLanguages: [],
      selectedLanguage: { code: 'en', name: 'English', flag: '🇺🇸' },
      languageSearch: '', // For searching languages
      // Summary-related data
      summary: '',
      summaryData: null,
      summaryLoading: false,
      summaryError: '',
      // Quotes-related data
      quotes: '',
      quotesData: null,
      quotesLoading: false,
      quotesError: '',
      // Descriptions-related data
      description: '',
      descriptionData: null,
      descriptionLoading: false,
      descriptionError: '',
      // Anki Cards-related data
      ankiCards: [],
      ankiCardsData: null,
      ankiCardsLoading: false,
      ankiCardsError: '',
      filterType: 'all',
      // Social Media-related data
      socialMediaPosts: '',
      socialMediaData: null,
      socialMediaLoading: false,
      socialMediaError: '',
      languageMap: {
        // Main languages with ISO 639-1 codes
        'en': { name: 'English', flag: '🇺🇸' },
        'es': { name: 'Spanish', flag: '🇪🇸' },
        'fr': { name: 'French', flag: '🇫🇷' },
        'de': { name: 'German', flag: '🇩🇪' },
        'it': { name: 'Italian', flag: '🇮🇹' },
        'pt': { name: 'Portuguese', flag: '🇵🇹' },
        'ru': { name: 'Russian', flag: '🇷🇺' },
        'ja': { name: 'Japanese', flag: '🇯🇵' },
        'ko': { name: 'Korean', flag: '🇰🇷' },
        'zh': { name: 'Chinese', flag: '🇨🇳' },
        'zh-Hans': { name: 'Chinese (Simplified)', flag: '🇨🇳' },
        'zh-Hant': { name: 'Chinese (Traditional)', flag: '🇹🇼' },
        'ar': { name: 'Arabic', flag: '🇸🇦' },
        'hi': { name: 'Hindi', flag: '🇮🇳' },
        'nl': { name: 'Dutch', flag: '🇳🇱' },
        'sv': { name: 'Swedish', flag: '🇸🇪' },
        'fi': { name: 'Finnish', flag: '🇫🇮' },
        'no': { name: 'Norwegian', flag: '🇳🇴' },
        'da': { name: 'Danish', flag: '🇩🇰' },
        'pl': { name: 'Polish', flag: '🇵🇱' },
        'tr': { name: 'Turkish', flag: '🇹🇷' },
        'cs': { name: 'Czech', flag: '🇨🇿' },
        'hu': { name: 'Hungarian', flag: '🇭🇺' },
        'el': { name: 'Greek', flag: '🇬🇷' },
        'th': { name: 'Thai', flag: '🇹🇭' },
        'vi': { name: 'Vietnamese', flag: '🇻🇳' },
        'id': { name: 'Indonesian', flag: '🇮🇩' },
        'ms': { name: 'Malay', flag: '🇲🇾' },
        'he': { name: 'Hebrew', flag: '🇮🇱' },
        'iw': { name: 'Modern Hebrew', flag: '🇮🇱' },
        'ro': { name: 'Romanian', flag: '🇷🇴' },
        'uk': { name: 'Ukrainian', flag: '🇺🇦' },
        'ur': { name: 'Urdu', flag: '🇵🇰' },

        // Regional variants - map to main languages
        'en-US': { name: 'English (US)', flag: '🇺🇸' },
        'en-GB': { name: 'English (UK)', flag: '🇬🇧' },
        'en-CA': { name: 'English (Canada)', flag: '🇨🇦' },
        'en-AU': { name: 'English (Australia)', flag: '🇦🇺' },
        'en-NZ': { name: 'English (New Zealand)', flag: '🇳🇿' },
        'en-IE': { name: 'English (Ireland)', flag: '🇮🇪' },
        'en-ZA': { name: 'English (South Africa)', flag: '🇿🇦' },
        'en-IN': { name: 'English (India)', flag: '🇮🇳' },
        'en-SG': { name: 'English (Singapore)', flag: '🇸🇬' },
        'en-PH': { name: 'English (Philippines)', flag: '🇵🇭' },

        'es-ES': { name: 'Spanish (Spain)', flag: '🇪🇸' },
        'es-MX': { name: 'Spanish (Mexico)', flag: '🇲🇽' },
        'es-AR': { name: 'Spanish (Argentina)', flag: '🇦🇷' },
        'es-CO': { name: 'Spanish (Colombia)', flag: '🇨🇴' },
        'es-CL': { name: 'Spanish (Chile)', flag: '🇨🇱' },
        'es-PE': { name: 'Spanish (Peru)', flag: '🇵🇪' },
        'es-VE': { name: 'Spanish (Venezuela)', flag: '🇻🇪' },
        'es-UY': { name: 'Spanish (Uruguay)', flag: '🇺🇾' },
        'es-PY': { name: 'Spanish (Paraguay)', flag: '🇵🇾' },
        'es-EC': { name: 'Spanish (Ecuador)', flag: '🇪🇨' },
        'es-GT': { name: 'Spanish (Guatemala)', flag: '🇬🇹' },
        'es-CU': { name: 'Spanish (Cuba)', flag: '🇨🇺' },
        'es-BO': { name: 'Spanish (Bolivia)', flag: '🇧🇴' },
        'es-DO': { name: 'Spanish (Dominican Republic)', flag: '🇩🇴' },
        'es-HN': { name: 'Spanish (Honduras)', flag: '🇭🇳' },
        'es-SV': { name: 'Spanish (El Salvador)', flag: '🇸🇻' },
        'es-NI': { name: 'Spanish (Nicaragua)', flag: '🇳🇮' },
        'es-CR': { name: 'Spanish (Costa Rica)', flag: '🇨🇷' },
        'es-PA': { name: 'Spanish (Panama)', flag: '🇵🇦' },
        'es-PR': { name: 'Spanish (Puerto Rico)', flag: '🇵🇷' },

        'fr-FR': { name: 'French (France)', flag: '🇫🇷' },
        'fr-CA': { name: 'French (Canada)', flag: '🇨🇦' },
        'fr-BE': { name: 'French (Belgium)', flag: '🇧🇪' },
        'fr-CH': { name: 'French (Switzerland)', flag: '🇨🇭' },
        'fr-LU': { name: 'French (Luxembourg)', flag: '🇱🇺' },
        'fr-MC': { name: 'French (Monaco)', flag: '🇲🇨' },

        'pt-PT': { name: 'Portuguese (Portugal)', flag: '🇵🇹' },
        'pt-BR': { name: 'Portuguese (Brazil)', flag: '🇧🇷' },
        'pt-AO': { name: 'Portuguese (Angola)', flag: '🇦🇴' },
        'pt-MZ': { name: 'Portuguese (Mozambique)', flag: '🇲🇿' },

        'de-DE': { name: 'German (Germany)', flag: '🇩🇪' },
        'de-AT': { name: 'German (Austria)', flag: '🇦🇹' },
        'de-CH': { name: 'German (Switzerland)', flag: '🇨🇭' },
        'de-LI': { name: 'German (Liechtenstein)', flag: '🇱🇮' },
        'de-LU': { name: 'German (Luxembourg)', flag: '🇱🇺' },

        'it-IT': { name: 'Italian (Italy)', flag: '🇮🇹' },
        'it-CH': { name: 'Italian (Switzerland)', flag: '🇨🇭' },
        'it-SM': { name: 'Italian (San Marino)', flag: '🇸🇲' },
        'it-VA': { name: 'Italian (Vatican City)', flag: '🇻🇦' },

        'ru-RU': { name: 'Russian (Russia)', flag: '🇷🇺' },
        'ru-BY': { name: 'Russian (Belarus)', flag: '🇧🇾' },
        'ru-KZ': { name: 'Russian (Kazakhstan)', flag: '🇰🇿' },
        'ru-KG': { name: 'Russian (Kyrgyzstan)', flag: '🇰🇬' },

        // Additional languages
        'af': { name: 'Afrikaans', flag: '🇿🇦' },
        'sq': { name: 'Albanian', flag: '🇦🇱' },
        'am': { name: 'Amharic', flag: '🇪🇹' },
        'hy': { name: 'Armenian', flag: '🇦🇲' },
        'az': { name: 'Azerbaijani', flag: '🇦🇿' },
        'eu': { name: 'Basque', flag: '🏴󠁥󠁳󠁰󠁶󠁿' },
        'be': { name: 'Belarusian', flag: '🇧🇾' },
        'bn': { name: 'Bengali', flag: '🇧🇩' },
        'bs': { name: 'Bosnian', flag: '🇧🇦' },
        'bg': { name: 'Bulgarian', flag: '🇧🇬' },
        'ca': { name: 'Catalan', flag: '🏴󠁥󠁳󠁣󠁴󠁿' },
        'ceb': { name: 'Cebuano', flag: '🇵🇭' },
        'ny': { name: 'Chichewa', flag: '🇲🇼' },
        'co': { name: 'Corsican', flag: '🇫🇷' },
        'hr': { name: 'Croatian', flag: '🇭🇷' },
        'et': { name: 'Estonian', flag: '🇪🇪' },
        'tl': { name: 'Filipino', flag: '🇵🇭' },
        'fil': { name: 'Filipino', flag: '🇵🇭' },
        'gl': { name: 'Galician', flag: '🏴󠁥󠁳󠁧󠁡󠁿' },
        'ka': { name: 'Georgian', flag: '🇬🇪' },
        'gu': { name: 'Gujarati', flag: '🇮🇳' },
        'ht': { name: 'Haitian Creole', flag: '🇭🇹' },
        'ha': { name: 'Hausa', flag: '🇳🇬' },
        'haw': { name: 'Hawaiian', flag: '🇺🇸' },
        'hmn': { name: 'Hmong', flag: '🇨🇳' },
        'is': { name: 'Icelandic', flag: '🇮🇸' },
        'ig': { name: 'Igbo', flag: '🇳🇬' },
        'ga': { name: 'Irish', flag: '🇮🇪' },
        'jw': { name: 'Javanese', flag: '🇮🇩' },
        'kn': { name: 'Kannada', flag: '🇮🇳' },
        'kk': { name: 'Kazakh', flag: '🇰🇿' },
        'km': { name: 'Khmer', flag: '🇰🇭' },
        'ku': { name: 'Kurdish', flag: '🇮🇶' },
        'ky': { name: 'Kyrgyz', flag: '🇰🇬' },
        'lo': { name: 'Lao', flag: '🇱🇦' },
        'la': { name: 'Latin', flag: '🇻🇦' },
        'lv': { name: 'Latvian', flag: '🇱🇻' },
        'lt': { name: 'Lithuanian', flag: '🇱🇹' },
        'lb': { name: 'Luxembourgish', flag: '🇱🇺' },
        'mk': { name: 'Macedonian', flag: '🇲🇰' },
        'mg': { name: 'Malagasy', flag: '🇲🇬' },
        'ml': { name: 'Malayalam', flag: '🇮🇳' },
        'mt': { name: 'Maltese', flag: '🇲🇹' },
        'mi': { name: 'Maori', flag: '🇳🇿' },
        'mr': { name: 'Marathi', flag: '🇮🇳' },
        'mn': { name: 'Mongolian', flag: '🇲🇳' },
        'my': { name: 'Myanmar (Burmese)', flag: '🇲🇲' },
        'ne': { name: 'Nepali', flag: '🇳🇵' },
        'nb': { name: 'Norwegian Bokmål', flag: '🇳🇴' },
        'nn': { name: 'Norwegian Nynorsk', flag: '🇳🇴' },
        'ps': { name: 'Pashto', flag: '🇦🇫' },
        'fa': { name: 'Persian', flag: '🇮🇷' },
        'pa': { name: 'Punjabi', flag: '🇮🇳' },
        'qu': { name: 'Quechua', flag: '🇵🇪' },
        'sm': { name: 'Samoan', flag: '🇼🇸' },
        'gd': { name: 'Scots Gaelic', flag: '🏴󠁧󠁢󠁳󠁣󠁴󠁿' },
        'sr': { name: 'Serbian', flag: '🇷🇸' },
        'st': { name: 'Sesotho', flag: '🇱🇸' },
        'sn': { name: 'Shona', flag: '🇿🇼' },
        'sd': { name: 'Sindhi', flag: '🇵🇰' },
        'si': { name: 'Sinhala', flag: '🇱🇰' },
        'sk': { name: 'Slovak', flag: '🇸🇰' },
        'sl': { name: 'Slovenian', flag: '🇸🇮' },
        'so': { name: 'Somali', flag: '🇸🇴' },
        'su': { name: 'Sundanese', flag: '🇮🇩' },
        'sw': { name: 'Swahili', flag: '🇹🇿' },
        'tg': { name: 'Tajik', flag: '🇹🇯' },
        'ta': { name: 'Tamil', flag: '🇮🇳' },
        'te': { name: 'Telugu', flag: '🇮🇳' },
        'uz': { name: 'Uzbek', flag: '🇺🇿' },
        'cy': { name: 'Welsh', flag: '🏴󠁧󠁢󠁷󠁬󠁳󠁿' },
        'xh': { name: 'Xhosa', flag: '🇿🇦' },
        'yi': { name: 'Yiddish', flag: '🇮🇱' },
        'yo': { name: 'Yoruba', flag: '🇳🇬' },
        'zu': { name: 'Zulu', flag: '🇿🇦' },

        // Special cases for YouTube
        'en-orig': { name: 'English (Original)', flag: '🇺🇸' },
        'auto': { name: 'Auto-detected', flag: '🌐' }
      }
    };
  },

  computed: {
    // Filter languages based on search input
    filteredLanguages() {
      if (!this.languageSearch) {
        return this.availableLanguages;
      }

      const search = this.languageSearch.toLowerCase();
      return this.availableLanguages.filter(lang =>
        lang.name.toLowerCase().includes(search) ||
        lang.code.toLowerCase().includes(search)
      );
    },

    // Render markdown summary as HTML
    renderedSummary() {
      if (!this.summary) return '';

      // Simple markdown to HTML conversion
      return this.summary
        .replace(/^### (.*$)/gim, '<h3 class="text-lg font-bold mt-4 mb-2">$1</h3>')
        .replace(/^## (.*$)/gim, '<h2 class="text-xl font-bold mt-6 mb-3">$1</h2>')
        .replace(/^# (.*$)/gim, '<h1 class="text-2xl font-bold mt-8 mb-4">$1</h1>')
        .replace(/^\* (.*$)/gim, '<li class="ml-4">$1</li>')
        .replace(/^\- (.*$)/gim, '<li class="ml-4">$1</li>')
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        .replace(/\n\n/g, '</p><p class="mb-3">')
        .replace(/^(?!<[h|l])/gm, '<p class="mb-3">')
        .replace(/<p class="mb-3">(<[h|l])/g, '$1')
        .replace(/(<\/[h|l][^>]*>)<\/p>/g, '$1');
    },

    // Render markdown quotes as HTML
    renderedQuotes() {
      if (!this.quotes) return '';

      // Enhanced markdown to HTML conversion for quotes with multiple quotes per category
      return this.quotes
        .replace(/^### (.*$)/gim, '<h3 class="text-lg font-bold mt-4 mb-2">$1</h3>')
        .replace(/^## (.*$)/gim, '<h2 class="text-xl font-bold mt-6 mb-3">$1</h2>')
        .replace(/^# (.*$)/gim, '<h1 class="text-2xl font-bold mt-8 mb-4">$1</h1>')
        .replace(/^\*\*(\d+\.\s.*?)\*\*/gm, '<strong class="font-bold text-lg mb-3 block text-primary">$1</strong>')
        .replace(/^> "\[(.*?)\]"$/gm, '<blockquote class="border-l-4 border-primary pl-4 py-2 mb-2 italic text-base bg-primary/5 rounded-r">"$1"</blockquote>')
        .replace(/^> "(.*?)"$/gm, '<blockquote class="border-l-4 border-primary pl-4 py-2 mb-2 italic text-base bg-primary/5 rounded-r">"$1"</blockquote>')
        .replace(/^> \[(.*?)\]$/gm, '<blockquote class="border-l-4 border-primary pl-4 py-2 mb-2 italic text-base bg-primary/5 rounded-r">$1</blockquote>')
        .replace(/^> (.*?)$/gm, '<blockquote class="border-l-4 border-primary pl-4 py-2 mb-2 italic text-base bg-primary/5 rounded-r">$1</blockquote>')
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        .replace(/\n\n/g, '</div><div class="mb-6">')
        .replace(/^(?!<)/gm, '<p class="mb-2">')
        .replace(/<p class="mb-2">(<[h|b|s])/g, '$1')
        .replace(/(<\/[h|b|s][^>]*>)<\/p>/g, '$1')
        .replace(/^/, '<div class="mb-6">')
        .replace(/$/, '</div>');
    },

    // Render markdown description as HTML
    renderedDescription() {
      if (!this.description) return '';

      // Enhanced markdown to HTML conversion for YouTube descriptions
      return this.description
        .replace(/^### (.*$)/gim, '<h3 class="text-lg font-bold mt-4 mb-2">$1</h3>')
        .replace(/^## (.*$)/gim, '<h2 class="text-xl font-bold mt-6 mb-3">$1</h2>')
        .replace(/^# (.*$)/gim, '<h1 class="text-2xl font-bold mt-8 mb-4">$1</h1>')
        .replace(/^\*\*\s*⏰\s*\*\*TIMESTAMPS\*\*$/gm, '<h3 class="text-lg font-bold mt-6 mb-3 text-primary">⏰ TIMESTAMPS</h3>')
        .replace(/^(\d{2}:\d{2})\s*-\s*(.*)$/gm, '<div class="timestamp-item group flex items-start gap-3 mb-2 p-2 hover:bg-base-200 rounded cursor-pointer transition-colors" data-time="$1"><span class="text-primary font-mono text-sm font-semibold flex items-center gap-1"><span class="opacity-0 group-hover:opacity-100 transition-opacity">▶</span>$1</span><span class="text-sm">$2</span></div>')
        .replace(/^(#\w+)/gm, '<span class="inline-block bg-primary/10 text-primary px-2 py-1 rounded text-xs mr-2 mb-2">$1</span>')
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        .replace(/\n\n/g, '</p><p class="mb-3">')
        .replace(/^(?!<[h|d|s])/gm, '<p class="mb-3">')
        .replace(/<p class="mb-3">(<[h|d|s])/g, '$1')
        .replace(/(<\/[h|d|s][^>]*>)<\/p>/g, '$1')
        .replace(/\*\*\*/g, '<hr class="my-4 border-base-300">');
    },

    // Render markdown social media posts as HTML
    renderedSocialMedia() {
      if (!this.socialMediaPosts) return '';

      // Enhanced markdown to HTML conversion for social media posts
      return this.socialMediaPosts
        .replace(/^### (.*$)/gim, '<h3 class="text-lg font-bold mt-4 mb-2">$1</h3>')
        .replace(/^## (.*$)/gim, '<h2 class="text-xl font-bold mt-6 mb-3">$1</h2>')
        .replace(/^# (.*$)/gim, '<h1 class="text-2xl font-bold mt-8 mb-4">$1</h1>')
        .replace(/^\*\*🐦 TWITTER \(X\) POST\*\*$/gm, '<div class="platform-section twitter-section"><h3 class="platform-title">🐦 TWITTER (X) POST</h3>')
        .replace(/^\*\*📱 INSTAGRAM POST\*\*$/gm, '</div><div class="platform-section instagram-section"><h3 class="platform-title">📱 INSTAGRAM POST</h3>')
        .replace(/^\*\*💼 LINKEDIN POST\*\*$/gm, '</div><div class="platform-section linkedin-section"><h3 class="platform-title">💼 LINKEDIN POST</h3>')
        .replace(/^\*Image Idea: (.*)\*$/gm, '<div class="image-idea"><strong>💡 Image Idea:</strong> <em>$1</em></div>')
        .replace(/^• (.*$)/gm, '<li class="ml-4 mb-1">$1</li>')
        .replace(/^Here are the key takeaways:$/gm, '<p class="font-semibold mb-2">Here are the key takeaways:</p><ul class="list-disc ml-6 mb-4">')
        .replace(/^(#\w+)/gm, '<span class="inline-block bg-primary/10 text-primary px-2 py-1 rounded text-xs mr-2 mb-2">$1</span>')
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        .replace(/\n\n/g, '</p><p class="mb-3">')
        .replace(/^(?!<[h|d|l|u|s])/gm, '<p class="mb-3">')
        .replace(/<p class="mb-3">(<[h|d|l|u|s])/g, '$1')
        .replace(/(<\/[h|d|l|u|s][^>]*>)<\/p>/g, '$1')
        .replace(/\*\*\*/g, '<hr class="my-4 border-base-300">')
        .replace(/---/g, '<hr class="my-6 border-base-200">')
        + '</div>'; // Close the last platform section
    },

    // Filter Anki cards based on type selection
    filteredCards() {
      if (!this.ankiCards || this.ankiCards.length === 0) return [];

      if (this.filterType === 'all') {
        return this.ankiCards;
      }

      return this.ankiCards.filter(card => card.type === this.filterType);
    },

    // Get selected cards for export
    selectedCards() {
      return this.ankiCards.filter(card => card.selected);
    },

    // Check if current tab has content to copy
    hasContentToCopy() {
      switch (this.activeTab) {
        case 'transcript':
          return this.transcript && this.transcript.length > 0;
        case 'summary':
          return this.summary && this.summary.trim().length > 0;
        case 'quotes':
          return this.quotes && this.quotes.trim().length > 0;
        case 'descriptions':
          return this.description && this.description.trim().length > 0;
        case 'social':
          return this.socialMedia && this.socialMedia.trim().length > 0;
        default:
          return false;
      }
    }
  },

  watch: {
    videoId: {
      immediate: true,
      handler(newVideoId, oldVideoId) {
        if (newVideoId !== oldVideoId) {
          // Check if this is an example video (should default to English)
          const isExampleVideo = this.isExampleVideo(newVideoId);

          if (isExampleVideo) {
            // For example videos, always default to English
            this.selectedLanguage = { code: 'en', name: 'English', flag: '🇺🇸' };
          } else {
            // For new user videos, we'll detect the original language in fetchAvailableLanguages
            this.selectedLanguage = { code: 'en', name: 'English', flag: '🇺🇸' };
          }

          // Clear previous video data
          this.clearPreviousVideoData();

          // Fetch transcript for the new video
          this.fetchTranscript();
          // Generate summary for the new video
          this.fetchSummary();
          // Generate quotes for the new video
          this.fetchQuotes();
          // Generate description for the new video
          this.fetchDescription();
          // Generate social media posts for the new video
          this.fetchSocialMedia();
          // Generate Anki cards for the new video
          this.fetchAnkiCards();
        } else {
          this.fetchTranscript();
          // Generate summary for the current video
          this.fetchSummary();
          // Generate quotes for the current video
          this.fetchQuotes();
          // Generate description for the current video
          this.fetchDescription();
          // Generate social media posts for the current video
          this.fetchSocialMedia();
        }
      }
    }
  },

  emits: ['seek-to-time', 'subtitles-loading-state', 'language-change'],

  mounted() {
    this.fetchAvailableLanguages();
    this.fetchTranscript();
    this.fetchDescription();
    this.fetchAnkiCards();
    this.fetchSocialMedia();
    console.log('FinalTabsContainer mounted, activeTab:', this.activeTab);
  },

  methods: {
    clearPreviousVideoData() {
      // Clear all previous video data to prevent showing old content
      this.transcript = [];
      this.summary = '';
      this.quotes = '';
      this.description = '';
      this.socialMedia = '';
      this.ankiCards = [];

      // Reset loading states
      this.transcriptLoading = false;
      this.summaryLoading = false;
      this.quotesLoading = false;
      this.descriptionLoading = false;
      this.socialMediaLoading = false;
      this.ankiCardsLoading = false;

      // Reset error states
      this.transcriptError = null;
      this.summaryError = null;
      this.quotesError = null;
      this.descriptionError = null;
      this.socialMediaError = null;
      this.ankiCardsError = null;

      console.log('Cleared previous video data for new video');
    },
    setActiveTab(tabId) {
      console.log('Setting active tab to:', tabId);
      this.activeTab = tabId;
    },

    // Check if a video ID is an example video
    isExampleVideo(videoId) {
      const exampleVideoIds = [
        'TT81fe2IobI', // Dunning-Kruger Effect
        'arj7oStGLkU', // Tim Urban procrastination
        'Gv2fzC96Z40', // Underdog story
        'UF8uR6Z6KLc', // Steve Jobs Stanford
        'ZrN4bKKMlLU', // Hitman documentary
        'KpVPST_P4W8', // Business video
        'pqWUuYTcG-o'  // Roger Federer Dartmouth
      ];
      return exampleVideoIds.includes(videoId);
    },

    // Copy transcript to clipboard
    async copyTranscript() {
      if (!this.videoId || !this.transcript || this.transcript.length === 0) return;

      try {
        // Extract text from transcript
        const text = this.transcript.map(item => item.text).join('\n');

        // Copy to clipboard
        await navigator.clipboard.writeText(text);

        // Show success message (you can replace this with a toast notification)
        alert('Transcript copied to clipboard!');
      } catch (err) {
        console.error('Error copying transcript:', err);
        alert('Failed to copy transcript');
      }
    },

    // Download transcript with format selection
    async downloadTranscript(format = 'txt') {
      if (!this.videoId) return;

      try {
        // Get the download URL with current language
        const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000/api';
        const downloadUrl = `${API_URL}/captions/${this.videoId}/download?format=${format}&lang=${this.selectedLanguage.code}`;

        // Create a temporary link and trigger the download
        const link = document.createElement('a');
        link.href = downloadUrl;
        const extension = format === 'doc' ? 'rtf' : format; // DOC format uses RTF extension
        link.setAttribute('download', `youtube-transcript-${this.videoId}.${extension}`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } catch (err) {
        console.error('Error downloading transcript:', err);
        alert('Failed to download transcript');
      }
    },

    // Copy summary to clipboard
    async copySummary() {
      if (!this.summary) return;

      try {
        // Create a clean text version without HTML
        const cleanText = this.summary
          .replace(/<[^>]*>/g, '')
          .replace(/&nbsp;/g, ' ')
          .replace(/&amp;/g, '&')
          .replace(/&lt;/g, '<')
          .replace(/&gt;/g, '>')
          .trim();

        await navigator.clipboard.writeText(cleanText);
        alert('Summary copied to clipboard!');
      } catch (err) {
        console.error('Error copying summary:', err);
        alert('Failed to copy summary');
      }
    },

    // Copy quotes to clipboard
    async copyQuotes() {
      if (!this.quotes) return;

      try {
        // Create a clean text version without HTML
        const cleanText = this.quotes
          .replace(/<[^>]*>/g, '')
          .replace(/&nbsp;/g, ' ')
          .replace(/&amp;/g, '&')
          .replace(/&lt;/g, '<')
          .replace(/&gt;/g, '>')
          .trim();

        await navigator.clipboard.writeText(cleanText);
        alert('Quotes copied to clipboard!');
      } catch (err) {
        console.error('Error copying quotes:', err);
        alert('Failed to copy quotes');
      }
    },

    async fetchAvailableLanguages() {
      if (!this.videoId) {
        console.log('No videoId provided, skipping language fetch');
        return;
      }

      try {
        console.log('Fetching available languages for video ID:', this.videoId);
        const API_URL = 'http://localhost:3000/api';

        // First, set up all languages from our language map
        this.setupCommonLanguages();

        // Try to get available languages using the new service first
        try {
          const availableLanguages = await getAvailableLanguages(this.videoId);
          if (availableLanguages && availableLanguages.length > 0) {
            console.log('Got available languages from client-side service:', availableLanguages);
            // Process the languages from the new service
            const captionLanguages = new Map();

            availableLanguages.forEach(lang => {
              const langInfo = this.languageMap[lang.code] || {
                name: lang.name || lang.code.toUpperCase(),
                flag: '🌐'
              };

              captionLanguages.set(lang.code, {
                code: lang.code,
                name: langInfo.name,
                flag: langInfo.flag,
                isAutoGenerated: lang.isAutoGenerated || false
              });
            });

            // Update available languages
            this.availableLanguages = Array.from(captionLanguages.values());

            // Set default language and fetch transcript
            await this.setDefaultLanguageAndFetch();
            return;
          }
        } catch (error) {
          console.warn('Client-side language fetching failed, falling back to backend:', error);
        }

        // Fallback to backend API
        const response = await fetch(`${API_URL}/captions/${this.videoId}`);

        if (!response.ok) {
          console.warn(`HTTP error fetching languages! status: ${response.status}`);
          // Fallback: fetch transcript with default language since we have setupCommonLanguages
          if (this.availableLanguages.length > 0) {
            await this.fetchTranscript(this.selectedLanguage.code);
          }
          return;
        }

        const captions = await response.json();

        if (Array.isArray(captions)) {
          // Process the captions to extract language information
          const captionLanguages = new Map(); // Use a Map to consolidate languages

          // Process the captions from the API
          captions.forEach(caption => {
            const langCode = caption.snippet.language;
            const baseCode = this.getBaseLanguageCode(langCode);

            // Skip if this is not a valid language code (sometimes YouTube API returns video IDs as language codes)
            if (langCode.length > 10 || !isNaN(langCode)) {
              console.warn(`Skipping invalid language code: ${langCode}`);
              return;
            }

            const langInfo = this.languageMap[langCode] || this.languageMap[baseCode] || { name: caption.snippet.language, flag: '🌐' };

            const language = {
              code: langCode,
              baseCode: baseCode,
              name: langInfo.name,
              flag: langInfo.flag,
              trackKind: caption.snippet.trackKind,
              captionId: caption.id,
              isAvailable: true // Mark as actually available
            };

            // Special handling for English to avoid duplicates showing different languages
            if (baseCode === 'en') {
              // If we already have English, only replace if this is a manual caption
              if (captionLanguages.has('en')) {
                const existing = captionLanguages.get('en');
                if (existing.trackKind !== 'standard' && language.trackKind === 'standard') {
                  captionLanguages.set('en', language);
                }
              } else {
                captionLanguages.set('en', language);
              }
            }
            // For other languages, use the same logic
            else if (captionLanguages.has(baseCode)) {
              const existing = captionLanguages.get(baseCode);
              if (existing.trackKind !== 'standard' && language.trackKind === 'standard') {
                captionLanguages.set(baseCode, language);
              }
            } else {
              captionLanguages.set(baseCode, language);
            }
          });

          // Mark languages that are actually available in the video
          if (captionLanguages.size > 0) {
            // Update our availableLanguages to mark which ones are actually available
            this.availableLanguages = this.availableLanguages.map(lang => {
              const captionLang = captionLanguages.get(lang.baseCode);
              if (captionLang) {
                return {
                  ...lang,
                  isAvailable: true,
                  trackKind: captionLang.trackKind,
                  captionId: captionLang.captionId
                };
              }
              return lang;
            });

            console.log('Updated available languages with caption information');

            // Determine the default language based on video type
            const isExampleVideo = this.isExampleVideo(this.videoId);

            let defaultLang;
            if (isExampleVideo) {
              // For example videos, always default to English
              defaultLang = this.availableLanguages.find(lang => lang.baseCode === 'en') || this.availableLanguages[0];
              console.log('Example video detected - defaulting to English:', defaultLang.name);
            } else {
              // For new user videos, find the original language of the video
              // Priority: 1. Manual captions in video's language, 2. Any available language, 3. English
              defaultLang = this.availableLanguages.find(lang =>
                lang.isAvailable && lang.trackKind === 'standard'
              ) || this.availableLanguages.find(lang =>
                lang.isAvailable
              ) || this.availableLanguages.find(lang =>
                lang.baseCode === 'en'
              ) || this.availableLanguages[0];
              console.log('User video detected - setting to original language:', defaultLang.name);
            }

            this.selectedLanguage = defaultLang;

            // Emit the initial language to the parent component
            this.$emit('language-change', defaultLang.code);

            // Force a transcript fetch with the selected language
            console.log(`Fetching transcript for default language: ${defaultLang.code}`);
            await this.fetchTranscript(defaultLang.code);
          }
        } else {
          console.warn('Invalid captions data format:', captions);
          // Fallback: fetch transcript with default language since we have setupCommonLanguages
          if (this.availableLanguages.length > 0) {
            await this.fetchTranscript(this.selectedLanguage.code);
          }
        }
      } catch (err) {
        console.error('Error fetching available languages:', err);
        // Fallback: fetch transcript with default language since we have setupCommonLanguages
        if (this.availableLanguages.length > 0) {
          await this.fetchTranscript(this.selectedLanguage.code);
        }
      }
    },

    async setDefaultLanguageAndFetch() {
      // Determine the default language based on video type
      const isExampleVideo = this.isExampleVideo(this.videoId);

      let defaultLang;
      if (isExampleVideo) {
        // For example videos, always default to English
        defaultLang = this.availableLanguages.find(lang => lang.code === 'en') || this.availableLanguages[0];
        console.log('Example video detected - defaulting to English:', defaultLang.name);
      } else {
        // For new user videos, find the original language of the video
        // Priority: 1. Manual captions, 2. Any available language, 3. English
        defaultLang = this.availableLanguages.find(lang =>
          !lang.isAutoGenerated
        ) || this.availableLanguages.find(lang =>
          lang.code === 'en'
        ) || this.availableLanguages[0];
        console.log('User video detected - setting to original language:', defaultLang.name);
      }

      this.selectedLanguage = defaultLang;

      // Emit the initial language to the parent component
      this.$emit('language-change', defaultLang.code);

      // Force a transcript fetch with the selected language
      console.log(`Fetching transcript for default language: ${defaultLang.code}`);
      await this.fetchTranscript(defaultLang.code);
    },

    // This method is no longer used, but kept for compatibility
    async probeAvailableLanguages() {
      console.log('Using setupCommonLanguages instead of probing');
      this.setupCommonLanguages();
    },

    // Get the base language code (e.g., 'en' from 'en-US')
    getBaseLanguageCode(code) {
      if (!code) return 'en';

      // Handle special cases
      if (code === 'en-orig') return 'en';
      if (code === 'auto') return 'en';

      // Handle special cases like 'en-orig'
      if (code.endsWith('-orig')) {
        return code.split('-')[0];
      }

      // Extract base code (e.g., 'en' from 'en-US')
      const baseCode = code.split('-')[0];

      // Special case for Chinese
      if (code.startsWith('zh')) {
        if (code.includes('Hans')) return 'zh-Hans';
        if (code.includes('Hant')) return 'zh-Hant';
        return 'zh';
      }

      return baseCode;
    },

    // Set up all available languages
    setupCommonLanguages() {
      // Create an array of all languages from the languageMap
      const allLanguages = Object.entries(this.languageMap).map(([code, info]) => {
        return {
          code: code,
          baseCode: this.getBaseLanguageCode(code),
          name: info.name,
          flag: info.flag,
          trackKind: 'auto-generated',
          captionId: null,
          isAvailable: false
        };
      });

      // Filter out duplicates based on baseCode (keeping the first occurrence)
      const uniqueLanguages = [];
      const seenBaseCodes = new Set();

      for (const lang of allLanguages) {
        // Skip regional variants for cleaner dropdown
        if (lang.code.includes('-') && lang.code !== 'zh-Hans' && lang.code !== 'zh-Hant' &&
            lang.code !== 'en-US' && lang.code !== 'en-orig') {
          continue;
        }

        // Special handling for English variants to avoid duplicates
        if (lang.baseCode === 'en') {
          // Only add the main English entry if we haven't seen it yet
          if (!seenBaseCodes.has('en')) {
            seenBaseCodes.add('en');
            uniqueLanguages.push(lang);
          }
        } else if (!seenBaseCodes.has(lang.baseCode)) {
          seenBaseCodes.add(lang.baseCode);
          uniqueLanguages.push(lang);
        }
      }

      // Sort languages alphabetically by name
      uniqueLanguages.sort((a, b) => a.name.localeCompare(b.name));

      // Move English to the top
      const englishIndex = uniqueLanguages.findIndex(lang => lang.baseCode === 'en');
      if (englishIndex > 0) {
        const english = uniqueLanguages.splice(englishIndex, 1)[0];
        uniqueLanguages.unshift(english);
      }

      this.availableLanguages = uniqueLanguages;
      this.selectedLanguage = uniqueLanguages[0]; // Default to English
    },

    async changeLanguage(langCode) {
      if (langCode === this.selectedLanguage.code) {
        return; // Already selected
      }

      console.log(`Changing language to: ${langCode}`);
      const langInfo = this.availableLanguages.find(lang => lang.code === langCode);

      if (langInfo) {
        // Set the selected language immediately to prevent UI confusion
        this.selectedLanguage = langInfo;

        // Emit the language change to the parent component
        this.$emit('language-change', langCode);

        // Use the fetchTranscript method with the language code to get the transcript
        // This ensures consistent handling of language selection
        await this.fetchTranscript(langCode);
      }
    },

    async fetchTranscript(langCode = null) {
      if (!this.videoId) {
        console.log('No videoId provided, skipping transcript fetch');
        return;
      }

      // Prevent multiple simultaneous fetches
      if (this.loading) {
        console.log('Transcript fetch already in progress, skipping duplicate request');
        return;
      }

      // Store whether this is a new video load or a language change
      const isNewVideoLoad = !langCode;

      // When a new video is loaded, fetch available languages first
      if (isNewVideoLoad) {
        console.log('New video load detected, fetching available languages first');
        await this.fetchAvailableLanguages();
        // Note: fetchAvailableLanguages will call fetchTranscript with the original language
        // so we can return early to avoid duplicate fetching
        return;
      }

      console.log(`Starting transcript fetch for language: ${langCode}`);
      this.loading = true;
      this.error = '';
      this.transcriptProgress = null; // Reset progress

      // Emit that subtitles are loading
      this.$emit('subtitles-loading-state', true);

      try {
        // If a specific language code is provided, use it directly
        // Otherwise use the currently selected language
        const language = langCode || this.selectedLanguage.code || 'en';
        console.log(`Fetching transcript for video ID: ${this.videoId} in language: ${language}`);

        // Use the new client-side transcript service with progress reporting
        const data = await getTranscript(this.videoId, language, {
          onProgress: (progress) => {
            console.log(`Transcript fetch progress:`, progress);
            this.transcriptProgress = progress;
          },
          retryCount: 2, // Retry failed methods once
          preferredMethods: ['cors-proxy', 'youtube-api', 'backend-api'] // Try client-side first
        });

        if (data && data.transcript && Array.isArray(data.transcript)) {
          console.log('Transcript found, length:', data.transcript.length, 'language:', data.language || language, 'auto-generated:', data.isAutoGenerated);
          this.transcript = data.transcript;

          // Always update the selected language to match the actual language of the transcript
          if (data.language) {
            // Store the auto-generated flag
            const isAutoGenerated = data.isAutoGenerated || false;
            console.log(`Server returned transcript in language: ${data.language}`);

            // Find the language in our available languages
            const returnedLangInfo = this.availableLanguages.find(lang =>
              lang.code === data.language ||
              lang.baseCode === data.language ||
              this.getBaseLanguageCode(lang.code) === this.getBaseLanguageCode(data.language)
            );

            if (returnedLangInfo) {
              console.log(`Found matching language in available languages: ${returnedLangInfo.name}`);
              // Update the language with auto-generated flag if needed
              returnedLangInfo.isAutoGenerated = isAutoGenerated;
              this.selectedLanguage = returnedLangInfo;
            } else {
              // If we can't find the exact language, try to find a language with the same base code
              const baseCode = this.getBaseLanguageCode(data.language);
              const similarLang = this.availableLanguages.find(lang =>
                this.getBaseLanguageCode(lang.code) === baseCode
              );

              if (similarLang) {
                console.log(`Found similar language with base code ${baseCode}: ${similarLang.name}`);
                // Update the language with auto-generated flag if needed
                similarLang.isAutoGenerated = isAutoGenerated;
                this.selectedLanguage = similarLang;
              } else if (this.languageMap[data.language]) {
                // If we have the language in our map but not in available languages, add it
                const langInfo = this.languageMap[data.language];
                const newLang = {
                  code: data.language,
                  baseCode: this.getBaseLanguageCode(data.language),
                  name: langInfo.name,
                  flag: langInfo.flag,
                  trackKind: 'auto-generated',
                  captionId: null,
                  isAutoGenerated: isAutoGenerated
                };
                this.availableLanguages.push(newLang);
                this.selectedLanguage = newLang;
                console.log(`Added new language to available languages: ${newLang.name}`);
              } else {
                // Try to determine a proper language name based on the code
                let languageName = 'Unknown Language';
                let languageFlag = '🌐';

                // Check if it's a video ID that got mixed in somehow
                if (data.language === 'KpVPST_P4W8') {
                  languageName = 'English';
                  languageFlag = '🇺🇸';
                } else if (data.language === 'arj7oStGLkU') {
                  languageName = 'English';
                  languageFlag = '🇺🇸';
                } else if (data.language === 'TT81fe2IobI') {
                  languageName = 'English';
                  languageFlag = '🇺🇸';
                } else if (data.language === 'UF8uR6Z6KLc') {
                  languageName = 'English';
                  languageFlag = '🇺🇸';
                } else if (data.language === 'Gv2fzC96Z40') {
                  languageName = 'English';
                  languageFlag = '🇺🇸';
                } else {
                  // Try to map common language codes
                  const langCodeMap = {
                    'en': 'English',
                    'fr': 'French',
                    'de': 'German',
                    'es': 'Spanish',
                    'it': 'Italian',
                    'pt': 'Portuguese',
                    'ru': 'Russian',
                    'ja': 'Japanese',
                    'ko': 'Korean',
                    'zh': 'Chinese',
                    'ar': 'Arabic',
                    'hi': 'Hindi',
                    'nl': 'Dutch'
                  };

                  // Check if we have a mapping for this language code
                  const baseCode = this.getBaseLanguageCode(data.language);
                  if (langCodeMap[baseCode]) {
                    languageName = langCodeMap[baseCode];
                    // Use the flag from our language map if available
                    if (this.languageMap[baseCode]) {
                      languageFlag = this.languageMap[baseCode].flag;
                    }
                  }
                }

                const newLang = {
                  code: data.language,
                  baseCode: this.getBaseLanguageCode(data.language),
                  name: languageName,
                  flag: languageFlag,
                  trackKind: 'auto-generated',
                  captionId: null,
                  isAutoGenerated: isAutoGenerated
                };
                this.availableLanguages.push(newLang);
                this.selectedLanguage = newLang;
                console.log(`Added generic language to available languages: ${newLang.name}`);
              }
            }
          }
        } else {
          console.error('Invalid transcript data format:', data);
          this.error = 'The server returned invalid transcript data. Please try again or contact support if the issue persists.';
          this.transcript = [];
        }
      } catch (err) {
        console.error('Error fetching transcript:', err);

        // Check if it's a network error or server error
        if (err.message.includes('Failed to fetch') || err.message.includes('NetworkError')) {
          this.error = 'Unable to connect to the server. Please check your internet connection and try again.';
        } else if (err.message.includes('404')) {
          this.error = 'This video does not have subtitles available or the video ID is invalid.';
        } else if (err.message.includes('500')) {
          this.error = 'Server error occurred while fetching the transcript. Please try again later.';
        } else {
          this.error = `Failed to fetch transcript: ${err.message}`;
        }

        // Don't use fallback transcript for production - it's confusing for users
        this.transcript = [];
      } finally {
        console.log(`Transcript fetch completed for language: ${langCode || this.selectedLanguage.code}`);
        this.loading = false;

        // Emit that subtitles are loaded
        this.$emit('subtitles-loading-state', false);
      }
    },

    formatTime(seconds) {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const secs = Math.floor(seconds % 60);

      return [
        hours.toString().padStart(2, '0'),
        minutes.toString().padStart(2, '0'),
        secs.toString().padStart(2, '0')
      ].join(':');
    },

    // This method has been consolidated with the one above

    seekToTime(time) {
      console.log(`FinalTabsContainer: Seeking to time ${time} for video ${this.videoId}`);
      console.log(`FinalTabsContainer: Time type: ${typeof time}, Time value: ${time}`);

      // Find the transcript item to verify we're using the correct timestamp
      const transcriptItem = this.transcript.find(item => item.start === time);
      if (transcriptItem) {
        console.log(`FinalTabsContainer: Found transcript item: "${transcriptItem.text.substring(0, 50)}..."`);
        console.log(`FinalTabsContainer: Item details - start: ${transcriptItem.start}, end: ${transcriptItem.end}, formattedStart: ${transcriptItem.formattedStart}`);
      } else {
        console.warn(`FinalTabsContainer: No transcript item found for time: ${time}`);
      }

      // Emit the seek-to-time event with a flag to force autoplay
      this.$emit('seek-to-time', time, true);
    },

    handleDescriptionClick(event) {
      console.log('🖱️ Description click detected:', event.target);

      // Check if the clicked element is a timestamp
      const timestampElement = event.target.closest('.timestamp-item');
      if (timestampElement) {
        const timeString = timestampElement.getAttribute('data-time');
        if (timeString) {
          console.log(`🎯 Description timestamp clicked: ${timeString}`);

          // Parse the time string (MM:SS format) to seconds
          const timeParts = timeString.split(':');
          let timeInSeconds = 0;

          if (timeParts.length === 2) {
            // MM:SS format
            timeInSeconds = parseInt(timeParts[0]) * 60 + parseInt(timeParts[1]);
          } else if (timeParts.length === 3) {
            // HH:MM:SS format
            timeInSeconds = parseInt(timeParts[0]) * 3600 + parseInt(timeParts[1]) * 60 + parseInt(timeParts[2]);
          }

          console.log(`⏰ Parsed time: ${timeInSeconds} seconds`);

          // Use the existing seekToTime method to navigate to the timestamp
          this.seekToTime(timeInSeconds);
        } else {
          console.warn('⚠️ No data-time attribute found on timestamp element');
        }
      } else {
        console.log('ℹ️ Click was not on a timestamp element');
      }
    },

    async fetchSummary() {
      if (!this.videoId) {
        console.log('No videoId provided, skipping summary fetch');
        return;
      }

      this.summaryLoading = true;
      this.summaryError = '';
      this.summary = '';
      this.summaryData = null;

      try {
        const language = this.selectedLanguage.code || 'en';
        console.log(`Fetching summary for video ID: ${this.videoId} in language: ${language}`);
        const API_URL = 'http://localhost:3000/api';

        const response = await fetch(`${API_URL}/summary/${this.videoId}?lang=${language}`);

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        if (data && data.success && data.summary) {
          console.log('Summary generated successfully');
          this.summary = data.summary;
          this.summaryData = {
            language: data.language,
            transcriptLength: data.transcriptLength,
            model: data.model
          };
        } else {
          console.error('Invalid summary data format:', data);
          this.summaryError = 'Invalid summary data format';
        }
      } catch (err) {
        console.error('Error fetching summary:', err);
        this.summaryError = `Failed to generate summary: ${err.message}`;
      } finally {
        this.summaryLoading = false;
      }
    },

    async fetchQuotes() {
      if (!this.videoId) {
        console.log('No videoId provided, skipping quotes fetch');
        return;
      }

      this.quotesLoading = true;
      this.quotesError = '';
      this.quotes = '';
      this.quotesData = null;

      try {
        const language = this.selectedLanguage.code || 'en';
        console.log(`Fetching quotes for video ID: ${this.videoId} in language: ${language}`);
        const API_URL = 'http://localhost:3000/api';

        const response = await fetch(`${API_URL}/quotes/${this.videoId}?lang=${language}`);

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        if (data && data.success && data.quotes) {
          console.log('Quotes generated successfully');
          this.quotes = data.quotes;
          this.quotesData = {
            language: data.language,
            transcriptLength: data.transcriptLength,
            model: data.model
          };
        } else {
          console.error('Invalid quotes data format:', data);
          this.quotesError = 'Invalid quotes data format';
        }
      } catch (err) {
        console.error('Error fetching quotes:', err);
        this.quotesError = `Failed to generate quotes: ${err.message}`;
      } finally {
        this.quotesLoading = false;
      }
    },

    async fetchDescription() {
      if (!this.videoId) {
        console.log('No videoId provided, skipping description generation');
        return;
      }

      this.descriptionLoading = true;
      this.descriptionError = '';
      this.description = '';
      this.descriptionData = null;

      try {
        const language = this.selectedLanguage.code || 'en';
        console.log(`Generating description for video ID: ${this.videoId} in language: ${language}`);
        const API_URL = 'http://localhost:3000/api';

        // Use default values for automatic generation
        const requestBody = {
          videoId: this.videoId,
          videoTitle: '', // Let the API determine from video metadata
          keywords: '', // Let the API determine from content
          ctaGoal: 'Subscribe for more content', // Default CTA
          lang: language
        };

        const response = await fetch(`${API_URL}/description`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(requestBody)
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        if (data && data.success && data.description) {
          console.log('Description generated successfully');
          this.description = data.description;
          this.descriptionData = {
            language: data.language,
            transcriptLength: data.transcriptLength,
            model: data.model,
            videoTitle: data.videoTitle,
            keywords: data.keywords,
            ctaGoal: data.ctaGoal
          };
        } else {
          console.error('Invalid description data format:', data);
          this.descriptionError = 'Invalid description data format';
        }
      } catch (err) {
        console.error('Error generating description:', err);
        this.descriptionError = `Failed to generate description: ${err.message}`;
      } finally {
        this.descriptionLoading = false;
      }
    },

    async fetchSocialMedia() {
      if (!this.videoId) {
        console.log('No videoId provided, skipping social media generation');
        return;
      }

      this.socialMediaLoading = true;
      this.socialMediaError = '';
      this.socialMediaPosts = '';
      this.socialMediaData = null;

      try {
        const language = this.selectedLanguage.code || 'en';
        console.log(`Generating social media posts for video ID: ${this.videoId} in language: ${language}`);
        const API_URL = 'http://localhost:3000/api';

        // Use default values for automatic generation
        const requestBody = {
          videoId: this.videoId,
          coreMessage: '', // Let the API determine from content
          lang: language
        };

        const response = await fetch(`${API_URL}/social-media`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(requestBody)
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        if (data && data.success && data.socialMediaPosts) {
          console.log('Social media posts generated successfully');
          this.socialMediaPosts = data.socialMediaPosts;
          this.socialMediaData = {
            language: data.language,
            transcriptLength: data.transcriptLength,
            model: data.model,
            coreMessage: data.coreMessage
          };
        } else {
          console.error('Invalid social media data format:', data);
          this.socialMediaError = 'Invalid social media data format';
        }
      } catch (err) {
        console.error('Error generating social media posts:', err);
        this.socialMediaError = `Failed to generate social media posts: ${err.message}`;
      } finally {
        this.socialMediaLoading = false;
      }
    },

    async fetchAnkiCards() {
      if (!this.videoId) {
        console.log('No videoId provided, skipping Anki cards fetch');
        return;
      }

      this.ankiCardsLoading = true;
      this.ankiCardsError = '';
      this.ankiCards = [];
      this.ankiCardsData = null;

      try {
        const language = this.selectedLanguage.code || 'en';
        console.log(`Fetching Anki cards for video ID: ${this.videoId} in language: ${language}`);
        const API_URL = 'http://localhost:3000/api';

        const requestBody = {
          videoId: this.videoId,
          lang: language
        };

        const response = await fetch(`${API_URL}/anki-cards`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(requestBody)
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        if (data && data.success && data.cards) {
          console.log('Anki cards generated successfully');

          // Validate that we have cards
          if (!Array.isArray(data.cards) || data.cards.length === 0) {
            this.ankiCardsError = 'No valid cards could be generated from this video content. Try a different video with more educational material.';
            return;
          }

          // Ensure all cards are properly reactive and selected by default
          this.ankiCards = data.cards.map(card => ({
            ...card,
            editing: false,
            selected: true // Default to selected
          }));

          this.ankiCardsData = {
            language: data.language,
            transcriptLength: data.metadata?.transcriptLength || 0,
            videoDuration: data.metadata?.videoDuration || 'Unknown',
            targetCardCount: data.metadata?.targetCardCount || 0,
            actualCardCount: data.metadata?.actualCardCount || 0,
            validationRate: data.metadata?.validationRate || 100,
            model: data.metadata?.model || 'Unknown'
          };

          // Show warning if validation rate is low
          if (data.metadata?.validationRate && data.metadata.validationRate < 70) {
            console.warn(`Low validation rate: ${data.metadata.validationRate}% of generated cards were valid`);
          }
        } else {
          console.error('Invalid Anki cards data format:', data);
          this.ankiCardsError = data.error || 'Invalid Anki cards data format';
        }
      } catch (err) {
        console.error('Error generating Anki cards:', err);
        this.ankiCardsError = `Failed to generate Anki cards: ${err.message}`;
      } finally {
        this.ankiCardsLoading = false;
      }
    },

    // Anki cards management methods
    selectAll() {
      this.filteredCards.forEach(card => {
        card.selected = true;
      });
    },

    deselectAll() {
      this.filteredCards.forEach(card => {
        card.selected = false;
      });
    },

    toggleEdit(card) {
      if (card.editing) {
        // Save changes
        card.editing = false;
        console.log('Saved changes to card:', card.id);
      } else {
        // Start editing
        card.editing = true;
      }
    },

    removeCard(card) {
      const index = this.ankiCards.findIndex(c => c.id === card.id);
      if (index > -1) {
        this.ankiCards.splice(index, 1);
        console.log('Removed card:', card.id);
      }
    },

    getTypeColor(type) {
      const colors = {
        'qa': 'badge-primary',
        'process': 'badge-accent',
        'truefalse': 'badge-info'
      };
      return colors[type] || 'badge-neutral';
    },

    exportToAnki() {
      const selected = this.selectedCards;
      if (selected.length === 0) {
        alert('Please select at least one card to export.');
        return;
      }

      // Create CSV content for Anki import with proper format
      const csvContent = this.generateAnkiCSV(selected);

      // Download the file
      this.downloadFile(csvContent, `anki-cards-${this.videoId}.csv`, 'text/csv');

      console.log(`Exported ${selected.length} Anki cards for import`);
    },

    generateAnkiCSV(cards) {
      // Anki CSV format with proper headers for import
      const header = '"Front","Back","Tags","Source_Video","Timestamp","Video_URL","Card_Type","Difficulty","Created_Date","User_Notes"\n';

      // Get video title from the current video data (if available)
      const videoTitle = this.getVideoTitle() || `YouTube Video ${this.videoId}`;
      const currentDate = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format

      const rows = cards.map(card => {
        // All card types now use standard front/back format (no more cloze)
        const front = `"${card.question.replace(/"/g, '""')}"`;
        const back = `"${card.answer.replace(/"/g, '""')}"`;


        // Create tags string with spaces (Anki format)
        const tags = `"${card.tags.join(' ')} ${card.type} ${card.difficulty}"`;

        // Source video title
        const sourceVideo = `"${videoTitle.replace(/"/g, '""')}"`;

        // Timestamp in MM:SS format
        const timestamp = `"${this.formatTime(card.timestamp)}"`;

        // Video URL with timestamp
        const videoUrl = `"https://youtube.com/watch?v=${this.videoId}&t=${Math.floor(card.timestamp)}s"`;

        // Card type mapping (removed cloze)
        const cardTypeMap = {
          'qa': 'basic_qa',
          'process': 'process',
          'truefalse': 'true_false'
        };
        const cardType = `"${cardTypeMap[card.type] || 'basic_qa'}"`;

        // Difficulty
        const difficulty = `"${card.difficulty}"`;

        // Created date
        const createdDate = `"${currentDate}"`;

        // User notes (empty for now)
        const userNotes = '""';

        return `${front},${back},${tags},${sourceVideo},${timestamp},${videoUrl},${cardType},${difficulty},${createdDate},${userNotes}`;
      }).join('\n');

      return header + rows;
    },

    getVideoTitle() {
      // Try to get video title from various sources
      if (this.descriptionData && this.descriptionData.videoTitle) {
        return this.descriptionData.videoTitle;
      }

      // Fallback to a generic title with video ID
      return `YouTube Video ${this.videoId}`;
    },



    downloadFile(content, filename, mimeType) {
      const blob = new Blob([content], { type: mimeType });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    },

    copyDescription() {
      if (!this.description) return;

      // Create a clean text version without HTML
      const cleanText = this.description
        .replace(/<[^>]*>/g, '')
        .replace(/&nbsp;/g, ' ')
        .replace(/&amp;/g, '&')
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .trim();

      navigator.clipboard.writeText(cleanText).then(() => {
        // You could add a toast notification here
        console.log('Description copied to clipboard');
      }).catch(err => {
        console.error('Failed to copy description:', err);
      });
    },

    copySocialMedia() {
      if (!this.socialMediaPosts) return;

      // Create a clean text version without HTML
      const cleanText = this.socialMediaPosts
        .replace(/<[^>]*>/g, '')
        .replace(/&nbsp;/g, ' ')
        .replace(/&amp;/g, '&')
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .trim();

      navigator.clipboard.writeText(cleanText).then(() => {
        // You could add a toast notification here
        console.log('Social media posts copied to clipboard');
      }).catch(err => {
        console.error('Failed to copy social media posts:', err);
      });
    }
  }
};
</script>

<style scoped>
.tabs-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.tab-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
  margin: -0.125rem;
}

.tab-button {
  margin: 0.125rem;
  background-color: #f0f0f0;
  transition: all 0.2s ease;
  cursor: pointer;
  color: #333;
  font-size: 14px !important;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.tab-button:hover {
  background-color: #e0e0e0;
}

.tab-button.active-tab {
  background-color: #4f46e5;
  color: white;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  font-weight: 600;
}

.tab-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.tab-text {
  white-space: nowrap;
}



/* Mobile Responsive Design */
@media (max-width: 768px) {
  .tab-button {
    padding: 0.5rem 0.75rem;
    font-size: 12px !important;
  }

  .tab-text {
    font-size: 0.75rem;
  }

  .tab-icon {
    font-size: 0.875rem;
  }
}

/* Transcript Control Buttons */
.transcript-control-btn {
  font-size: 11px;
  font-weight: 500;
  min-height: 1.75rem;
}

@media (min-width: 640px) {
  .transcript-control-btn {
    font-size: 13px;
    min-height: 2rem;
  }
}

/* Custom breakpoint for extra small screens */
@media (min-width: 475px) {
  .xs\:inline {
    display: inline !important;
  }
}

/* Extra small screens */
@media (max-width: 480px) {
  .tab-button {
    padding: 0.375rem 0.5rem;
    font-size: 11px !important;
  }

  .tab-text {
    font-size: 0.7rem;
  }

  .tab-icon {
    font-size: 0.75rem;
  }

  .transcript-control-btn {
    font-size: 10px;
    min-height: 1.5rem;
    padding: 0.25rem 0.5rem;
  }
}



.tab-content-container {
  flex: 1;
  position: relative;
}

.transcript-container, .coming-soon-container, .summary-container, .quotes-container, .descriptions-container, .anki-cards-container, .social-media-container {
  height: 100%;
}

.coming-soon-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 2rem;
}

.transcript-list {
  height: 450px;
  overflow-y: auto;
  border-radius: 0.5rem;
}

table {
  border-collapse: separate;
  border-spacing: 0;
}

tbody tr:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

td {
  padding: 0.75rem 0.5rem;
}

td:first-child button {
  transition: all 0.2s ease;
}

td:first-child button:hover {
  background-color: #4f46e5;
  color: white;
}

/* Language dropdown styles */
.menu li a {
  display: flex;
  width: 100%;
  padding: 0.5rem 1rem;
  cursor: pointer;
}

.menu li:hover a {
  background-color: inherit;
}

/* Search input container */
.dropdown-content .relative {
  width: 100%;
}

/* Ensure consistent padding */
.dropdown-content .input {
  box-sizing: border-box;
  width: 100%;
}

/* Language dropdown styling */
.language-dropdown .menu {
  width: 100%;
  padding: 0;
}

.language-dropdown .menu li {
  width: 100%;
}

/* Custom loading bar animation */
@keyframes loadingBar {
  0% {
    width: 0%;
    margin-left: 0;
  }
  50% {
    width: 100%;
    margin-left: 0;
  }
  100% {
    width: 0%;
    margin-left: 100%;
  }
}

.language-dropdown .menu li a {
  width: 100%;
  box-sizing: border-box;
  padding: 0.5rem 1rem;
}

/* Summary container styles */
.summary-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.summary-content {
  flex: 1;
  overflow-y: auto;
}

.summary-content .prose {
  color: inherit;
}

.summary-content .prose h1,
.summary-content .prose h2,
.summary-content .prose h3 {
  color: inherit;
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
}

.summary-content .prose h1:first-child,
.summary-content .prose h2:first-child,
.summary-content .prose h3:first-child {
  margin-top: 0;
}

.summary-content .prose p {
  margin-bottom: 1rem;
  line-height: 1.6;
}

.summary-content .prose li {
  margin-bottom: 0.5rem;
  line-height: 1.5;
}

.summary-content .prose strong {
  font-weight: 600;
}

.summary-content .prose em {
  font-style: italic;
}

/* Quotes container styles */
.quotes-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.quotes-content {
  flex: 1;
  overflow-y: auto;
}

.quotes-content .prose blockquote {
  border-left: 4px solid #4f46e5;
  padding-left: 1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  margin-bottom: 0.75rem;
  font-style: italic;
  font-size: 1.125rem;
  background-color: rgba(79, 70, 229, 0.05);
  border-radius: 0.25rem;
}

.quotes-content .prose strong {
  font-weight: 600;
  font-size: 1.125rem;
  margin-bottom: 0.5rem;
  display: block;
  color: #4f46e5;
}

/* Descriptions container styles */
.descriptions-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.description-content {
  flex: 1;
  overflow-y: auto;
}

.description-content .prose {
  color: inherit;
}

.timestamp-item {
  transition: background-color 0.2s ease;
}

.timestamp-item:hover {
  background-color: rgba(79, 70, 229, 0.1);
}

.timestamp-item span:first-child {
  min-width: 50px;
  color: #4f46e5;
  font-family: 'Courier New', monospace;
  font-weight: 600;
}

.description-content .prose hr {
  border-color: rgba(79, 70, 229, 0.2);
  margin: 1.5rem 0;
}

/* Social Media container styles */
.social-media-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.social-media-content {
  flex: 1;
  overflow-y: auto;
}

.social-media-content .prose {
  color: inherit;
}

.platform-section {
  margin-bottom: 2rem;
  padding: 1.5rem;
  border-radius: 0.75rem;
  border: 2px solid transparent;
}

.twitter-section {
  background: linear-gradient(135deg, rgba(29, 161, 242, 0.1), rgba(29, 161, 242, 0.05));
  border-color: rgba(29, 161, 242, 0.2);
}

.instagram-section {
  background: linear-gradient(135deg, rgba(225, 48, 108, 0.1), rgba(225, 48, 108, 0.05));
  border-color: rgba(225, 48, 108, 0.2);
}

.linkedin-section {
  background: linear-gradient(135deg, rgba(0, 119, 181, 0.1), rgba(0, 119, 181, 0.05));
  border-color: rgba(0, 119, 181, 0.2);
}

.platform-title {
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #4f46e5;
}

.image-idea {
  background: rgba(79, 70, 229, 0.1);
  padding: 1rem;
  border-radius: 0.5rem;
  margin: 1rem 0;
  border-left: 4px solid #4f46e5;
}

.social-media-content .prose hr {
  border-color: rgba(79, 70, 229, 0.2);
  margin: 2rem 0;
}
</style>
