<template>
  <Teleport to="body">
    <div class="toast-container">
      <TransitionGroup name="toast" tag="div">
        <div
          v-for="toast in toasts"
          :key="toast.id"
          class="toast-item"
          :class="[
            'toast-' + toast.type,
            { 'toast-dismissible': toast.dismissible }
          ]"
          @click="toast.dismissible && removeToast(toast.id)"
        >
          <div class="toast-content">
            <!-- Icon -->
            <div class="toast-icon">
              <!-- Success Icon -->
              <svg v-if="toast.type === 'success'" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
              <!-- Error Icon -->
              <svg v-else-if="toast.type === 'error'" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
              <!-- Warning Icon -->
              <svg v-else-if="toast.type === 'warning'" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
              </svg>
              <!-- Info Icon -->
              <svg v-else-if="toast.type === 'info'" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              <!-- Copy Icon -->
              <svg v-else-if="toast.type === 'copy'" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
              </svg>
            </div>
            
            <!-- Message -->
            <div class="toast-message">
              <div class="toast-title" v-if="toast.title">{{ toast.title }}</div>
              <div class="toast-text">{{ toast.message }}</div>
            </div>
            
            <!-- Close button for dismissible toasts -->
            <button 
              v-if="toast.dismissible" 
              @click.stop="removeToast(toast.id)"
              class="toast-close"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>
          
          <!-- Progress bar for auto-dismiss -->
          <div 
            v-if="toast.duration && toast.duration > 0" 
            class="toast-progress"
            :style="{ animationDuration: toast.duration + 'ms' }"
          ></div>
        </div>
      </TransitionGroup>
    </div>
  </Teleport>
</template>

<script>
export default {
  name: 'ToastNotification',
  data() {
    return {
      toasts: []
    };
  },
  
  mounted() {
    // Listen for global toast events
    window.addEventListener('show-toast', this.handleToastEvent);
  },
  
  beforeUnmount() {
    window.removeEventListener('show-toast', this.handleToastEvent);
  },
  
  methods: {
    handleToastEvent(event) {
      this.showToast(event.detail);
    },
    
    showToast(options) {
      const toast = {
        id: Date.now() + Math.random(),
        type: options.type || 'info',
        title: options.title || null,
        message: options.message || 'Notification',
        duration: options.duration !== undefined ? options.duration : 3000,
        dismissible: options.dismissible !== undefined ? options.dismissible : true,
        ...options
      };
      
      this.toasts.push(toast);
      
      // Auto-remove after duration
      if (toast.duration > 0) {
        setTimeout(() => {
          this.removeToast(toast.id);
        }, toast.duration);
      }
    },
    
    removeToast(id) {
      const index = this.toasts.findIndex(toast => toast.id === id);
      if (index > -1) {
        this.toasts.splice(index, 1);
      }
    }
  }
};
</script>

<style scoped>
.toast-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  pointer-events: none;
}

.toast-item {
  pointer-events: auto;
  margin-bottom: 12px;
  min-width: 300px;
  max-width: 400px;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.toast-content {
  display: flex;
  align-items: flex-start;
  padding: 16px;
  gap: 12px;
}

.toast-icon {
  flex-shrink: 0;
  width: 20px;
  height: 20px;
  margin-top: 2px;
}

.toast-message {
  flex: 1;
  min-width: 0;
}

.toast-title {
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 4px;
}

.toast-text {
  font-size: 14px;
  line-height: 1.4;
  word-wrap: break-word;
}

.toast-close {
  flex-shrink: 0;
  width: 20px;
  height: 20px;
  border: none;
  background: none;
  cursor: pointer;
  opacity: 0.6;
  transition: opacity 0.2s;
  margin-top: 2px;
}

.toast-close:hover {
  opacity: 1;
}

.toast-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  background: currentColor;
  opacity: 0.3;
  animation: toast-progress linear forwards;
}

@keyframes toast-progress {
  from { width: 100%; }
  to { width: 0%; }
}

/* Toast type styles */
.toast-success {
  background: rgba(34, 197, 94, 0.95);
  color: white;
  border-left: 4px solid rgb(34, 197, 94);
}

.toast-error {
  background: rgba(239, 68, 68, 0.95);
  color: white;
  border-left: 4px solid rgb(239, 68, 68);
}

.toast-warning {
  background: rgba(245, 158, 11, 0.95);
  color: white;
  border-left: 4px solid rgb(245, 158, 11);
}

.toast-info {
  background: rgba(59, 130, 246, 0.95);
  color: white;
  border-left: 4px solid rgb(59, 130, 246);
}

.toast-copy {
  background: rgba(16, 185, 129, 0.95);
  color: white;
  border-left: 4px solid rgb(16, 185, 129);
}

/* Transitions */
.toast-enter-active {
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.toast-leave-active {
  transition: all 0.3s cubic-bezier(0.55, 0.085, 0.68, 0.53);
}

.toast-enter-from {
  opacity: 0;
  transform: translateX(100%) scale(0.8);
}

.toast-leave-to {
  opacity: 0;
  transform: translateX(100%) scale(0.8);
}

.toast-move {
  transition: transform 0.3s ease;
}

/* Responsive */
@media (max-width: 640px) {
  .toast-container {
    top: 10px;
    right: 10px;
    left: 10px;
  }
  
  .toast-item {
    min-width: auto;
    max-width: none;
  }
}
</style>
