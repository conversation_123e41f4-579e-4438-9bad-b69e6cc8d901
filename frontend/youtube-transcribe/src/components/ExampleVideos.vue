<script setup>
import { ref, onMounted, computed } from 'vue';
import axios from 'axios';
import YourVideo from './YourVideo.vue';
import AddVideoButton from './AddVideoButton.vue';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000/api';

const props = defineProps({
  currentVideoId: {
    type: String,
    default: ''
  },
  userVideos: {
    type: Array,
    default: () => []
  },
  userVideoDetails: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['video-selected', 'play-video', 'add-video']);

const videos = ref([]);
const loading = ref(true);
const error = ref('');
const scrollContainer = ref(null);

// Computed property to check if we have a current video
const hasCurrentVideo = computed(() => !!props.currentVideoId);

// Truncate long titles
function truncateTitle(title) {
  return title.length > 30 ? title.substring(0, 27) + '...' : title;
}

// Fetch example videos when component mounts
onMounted(async () => {
  console.log('ExampleVideos component mounted');
  console.log('API_URL:', API_URL);

  try {
    console.log('Fetching example videos...');
    const response = await axios.get(`${API_URL}/examples`);
    console.log('Example videos response:', response.data);
    videos.value = response.data.videos;
    loading.value = false;
  } catch (err) {
    console.error('Error fetching example videos:', err);
    console.error('Error details:', err.response || err.message);
    error.value = 'Failed to load example videos';
    loading.value = false;
  }
});

// Emit event when a video is selected
function selectVideo(videoId) {
  emit('video-selected', videoId);
}

// Handle play video
function playVideo(videoId) {
  emit('play-video', videoId);
}

// Scroll functions
function scrollLeft() {
  if (scrollContainer.value) {
    scrollContainer.value.scrollBy({ left: -300, behavior: 'smooth' });
  }
}

function scrollRight() {
  if (scrollContainer.value) {
    scrollContainer.value.scrollBy({ left: 300, behavior: 'smooth' });
  }
}

// Handle add video button click
function handleAddVideo() {
  emit('add-video');
}
</script>

<template>
  <div class="relative">
    <!-- Scroll buttons -->
    <button
      @click="scrollLeft"
      class="absolute left-0 top-1/2 transform -translate-y-1/2 z-10 btn btn-circle btn-sm bg-base-100/80 hover:bg-base-100"
    >
      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
      </svg>
    </button>

    <button
      @click="scrollRight"
      class="absolute right-0 top-1/2 transform -translate-y-1/2 z-10 btn btn-circle btn-sm bg-base-100/80 hover:bg-base-100"
    >
      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
      </svg>
    </button>

    <div v-if="loading" class="flex justify-center">
      <span class="loading loading-spinner loading-lg text-primary"></span>
    </div>

    <div v-else-if="error" class="alert alert-error">
      <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
      <span>{{ error }}</span>
    </div>

    <div
      v-else
      ref="scrollContainer"
      class="flex gap-3 pb-2 overflow-x-auto hide-scrollbar px-6 py-2 scroll-smooth"
    >
      <!-- Add Video Component (always empty) -->
      <YourVideo
        @play-video="playVideo"
      />

      <!-- User Added Videos - Displayed in reverse order (newest first) so new videos appear right after the "Add" box -->
      <div
        v-for="videoId in [...props.userVideos].reverse()"
        :key="'user-' + videoId"
        class="card bg-base-200 shadow-md hover:shadow-lg transition-all cursor-pointer flex-shrink-0 border border-base-300"
        :class="{
          'border-gradient': props.currentVideoId === videoId,
          'ring-2 ring-primary': props.currentVideoId === videoId
        }"
        style="width: calc(20% - 12px);"
        @click="selectVideo(videoId)"
      >
        <figure class="relative">
          <img
            :src="`https://img.youtube.com/vi/${videoId}/mqdefault.jpg`"
            :alt="props.userVideoDetails[videoId]?.title || 'Video ' + videoId"
            class="w-full aspect-video object-cover"
          >
          <div class="absolute inset-0 flex items-center justify-center bg-black/30 opacity-0 hover:opacity-100 transition-opacity">
            <div class="bg-primary rounded-full p-1.5">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" viewBox="0 0 24 24" fill="currentColor">
                <path d="M8 5v14l11-7z"/>
              </svg>
            </div>
          </div>
        </figure>
        <div class="card-body p-2">
          <h3 class="text-xs font-medium truncate" :title="props.userVideoDetails[videoId]?.title || 'Video ' + videoId">
            {{ props.userVideoDetails[videoId]?.title || 'Video ' + videoId }}
          </h3>
        </div>
      </div>

      <!-- Example Videos -->
      <div
        v-for="video in videos"
        :key="video.id"
        class="card bg-base-200 shadow-md hover:shadow-lg transition-all cursor-pointer flex-shrink-0 border border-primary/10"
        :class="{ 'ring-2 ring-primary': currentVideoId === video.id }"
        style="width: calc(20% - 12px);"
        @click="selectVideo(video.id)"
      >
        <figure class="relative">
          <img
            :src="video.thumbnail"
            :alt="video.title"
            class="w-full aspect-video object-cover"
          >
          <div class="absolute inset-0 flex items-center justify-center bg-black/30 opacity-0 hover:opacity-100 transition-opacity">
            <div class="bg-primary rounded-full p-1.5">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" viewBox="0 0 24 24" fill="currentColor">
                <path d="M8 5v14l11-7z"/>
              </svg>
            </div>
          </div>

        </figure>
        <div class="card-body p-2">
          <h3 class="text-xs font-medium truncate" :title="video.title">
            {{ truncateTitle(video.title) }}
          </h3>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.hide-scrollbar::-webkit-scrollbar {
  display: none;
}
.hide-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
</style>
