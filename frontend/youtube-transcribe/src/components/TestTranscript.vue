<template>
  <div class="p-4">
    <h2 class="text-xl font-bold mb-4">Test Transcript</h2>

    <div v-if="loading" class="flex justify-center py-8">
      <span class="loading loading-spinner loading-lg text-primary"></span>
    </div>

    <div v-else-if="error" class="alert alert-error">
      <span>{{ error }}</span>
    </div>

    <div v-else-if="transcript.length > 0" class="space-y-4">
      <div class="p-4 bg-base-200 rounded-lg">
        <p>Transcript length: {{ transcript.length }}</p>
      </div>

      <div class="h-[500px] overflow-y-auto bg-base-100 p-4 rounded-lg border border-base-300 relative z-30">
        <div v-for="item in transcript" :key="item.id" class="mb-6">
          <div class="flex items-center mb-1">
            <button class="text-primary font-medium">
              {{ item.formattedStart || formatTime(item.start) }}
            </button>
          </div>
          <p>{{ item.text }}</p>
        </div>
      </div>
    </div>

    <div v-else class="alert alert-warning">
      <span>No transcript data available.</span>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue';
import axios from 'axios';

const props = defineProps({
  videoId: {
    type: String,
    default: 'TT81fe2IobI' // Default to one of the example videos
  }
});

const loading = ref(false);
const error = ref('');
const transcript = ref([]);

// Watch for changes to videoId and fetch transcript when it changes
watch(() => props.videoId, fetchTranscript, { immediate: true });

// Also fetch on component mount
onMounted(fetchTranscript);

async function fetchTranscript() {
  if (!props.videoId) {
    console.log('TestTranscript: No videoId provided, skipping transcript fetch');
    return;
  }

  loading.value = true;
  error.value = '';
  transcript.value = [];

  try {
    console.log('TestTranscript: Fetching transcript for video ID:', props.videoId);
    const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000/api';
    console.log('Using API URL:', API_URL);

    const response = await axios.get(`${API_URL}/transcript/${props.videoId}`);
    console.log('TestTranscript: API response:', response.data);

    if (response.data && response.data.transcript) {
      console.log('TestTranscript: Transcript data found, length:', response.data.transcript.length);
      transcript.value = response.data.transcript;

      // Add formatted time for each transcript item if not already present
      transcript.value.forEach(item => {
        if (!item.formattedStart) {
          item.formattedStart = formatTime(item.start);
        }
      });

      console.log('TestTranscript: Processed transcript:', transcript.value);
    } else {
      console.error('TestTranscript: Invalid transcript data format:', response.data);
      error.value = 'Invalid transcript data format';
    }
  } catch (err) {
    console.error('TestTranscript: Error fetching transcript:', err);
    error.value = err.response?.data?.error || 'Failed to fetch transcript';
  } finally {
    loading.value = false;
  }
}

function formatTime(seconds) {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  return [
    hours.toString().padStart(2, '0'),
    minutes.toString().padStart(2, '0'),
    secs.toString().padStart(2, '0')
  ].join(':');
}
</script>
