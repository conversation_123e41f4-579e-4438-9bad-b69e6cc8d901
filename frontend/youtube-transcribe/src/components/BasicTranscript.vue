<template>
  <div class="basic-transcript">
    <h2 class="text-xl font-bold mb-4">Video Transcript</h2>
    <p class="text-sm text-base-content/70 mb-4">Click on any timestamp to jump to that point in the video.</p>

    <div v-if="loading" class="flex justify-center py-8">
      <span class="loading loading-spinner loading-lg text-primary"></span>
    </div>

    <div v-else-if="error" class="alert alert-error mb-4">
      <span>{{ error }}</span>
    </div>

    <div v-else-if="!transcript || transcript.length === 0" class="alert alert-warning mb-4">
      <span>No transcript data available for this video.</span>
    </div>

    <div v-else>


      <!-- Simple transcript display -->
      <div class="transcript-list bg-base-100 p-4 rounded-lg border border-base-300">
        <table class="w-full">
          <thead>
            <tr>
              <th class="text-left pb-2 w-24">Timestamp</th>
              <th class="text-left pb-2">Text</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, index) in transcript" :key="index" class="border-b border-base-200 hover:bg-base-200">
              <td class="py-2 pr-4 font-medium text-primary whitespace-nowrap">
                <button
                  class="px-2 py-1 rounded hover:bg-primary hover:text-white transition-colors"
                  @click="$emit('seek-to-time', item.start)"
                >
                  {{ item.formattedStart || formatTime(item.start) }}
                </button>
              </td>
              <td class="py-2">{{ item.text }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    videoId: {
      type: String,
      required: true
    }
  },

  data() {
    return {
      loading: false,
      error: '',
      transcript: []
    };
  },

  watch: {
    videoId: {
      immediate: true,
      handler: 'fetchTranscript'
    }
  },

  mounted() {
    this.fetchTranscript();
  },

  methods: {
    async fetchTranscript() {
      if (!this.videoId) {
        console.log('BasicTranscript: No videoId provided, skipping transcript fetch');
        return;
      }

      this.loading = true;
      this.error = '';
      this.transcript = [];

      try {
        console.log('BasicTranscript: Fetching transcript for video ID:', this.videoId);
        const API_URL = 'http://localhost:3000/api';

        console.log(`BasicTranscript: Fetching from URL: ${API_URL}/transcript/${this.videoId}`);
        const response = await fetch(`${API_URL}/transcript/${this.videoId}`);

        console.log('BasicTranscript: Response status:', response.status);

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const responseText = await response.text();
        console.log('BasicTranscript: Raw response text:', responseText.substring(0, 200) + '...');

        try {
          const data = JSON.parse(responseText);
          console.log('BasicTranscript: API response parsed successfully:', data);

          if (data && data.transcript && Array.isArray(data.transcript)) {
            console.log('BasicTranscript: Transcript found, length:', data.transcript.length);
            this.transcript = data.transcript;
            console.log('BasicTranscript: First few transcript items:', this.transcript.slice(0, 3));
          } else {
            console.error('BasicTranscript: Invalid transcript data format:', data);
            this.error = 'Invalid transcript data format';
          }
        } catch (parseError) {
          console.error('BasicTranscript: Error parsing JSON:', parseError);
          this.error = 'Error parsing response: ' + parseError.message;
        }
      } catch (err) {
        console.error('BasicTranscript: Error fetching transcript:', err);
        this.error = `Failed to fetch transcript: ${err.message}`;

        // Fallback to hardcoded transcript for testing
        this.transcript = [
          { id: 1, start: 0, text: "the English philosopher bertran Russell" },
          { id: 2, start: 3, text: "once said the whole problem with the" },
          { id: 3, start: 6, text: "world is that fools and Fanatics are so" },
          { id: 4, start: 9, text: "sure of themselves while wiser people" },
          { id: 5, start: 12, text: "are so full of doubt in Psychology what" }
        ];
      } finally {
        this.loading = false;
        console.log('BasicTranscript: Transcript fetch completed. Has transcript:', this.transcript.length > 0);
      }
    },

    formatTime(seconds) {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const secs = Math.floor(seconds % 60);

      return [
        hours.toString().padStart(2, '0'),
        minutes.toString().padStart(2, '0'),
        secs.toString().padStart(2, '0')
      ].join(':');
    }
  }
};
</script>

<style scoped>
.basic-transcript {
  height: 100%;
}

.transcript-list {
  height: 450px;
  overflow-y: auto;
  border-radius: 0.5rem;
}

table {
  border-collapse: separate;
  border-spacing: 0;
}

tbody tr:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

td {
  padding: 0.75rem 0.5rem;
}

td:first-child button {
  transition: all 0.2s ease;
}

td:first-child button:hover {
  background-color: var(--p);
  color: white;
}
</style>
