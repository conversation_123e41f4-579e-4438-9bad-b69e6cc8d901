import axios from 'axios';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000/api';

// Create axios instance with base URL
const apiClient = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

export default {
  // Get video information
  getVideoInfo(videoId) {
    return apiClient.get(`/video/${videoId}`);
  },

  // Get available captions for a video
  getCaptions(videoId) {
    return apiClient.get(`/captions/${videoId}`);
  },

  // Get transcript content with timestamps
  getTranscript(videoId, lang = 'en') {
    return apiClient.get(`/transcript/${videoId}`, {
      params: { lang }
    });
  },

  // Download captions in specific format
  downloadCaptions(videoId, format = 'txt', captionId) {
    return apiClient.get(`/captions/${videoId}/download`, {
      params: { format, captionId },
      responseType: 'blob',
    });
  },
};
