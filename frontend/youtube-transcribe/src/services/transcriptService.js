/**
 * Client-Side YouTube Transcript Service
 * 
 * This service attempts to fetch YouTube transcripts directly from the browser
 * using multiple fallback mechanisms to reduce backend API usage.
 * 
 * Fallback chain:
 * 1. Direct YouTube transcript URL fetch (with CORS proxy)
 * 2. YouTube Data API v3 (client-side)
 * 3. Alternative CORS proxies
 * 4. Backend API (last resort)
 */

// CORS proxy services for fallback (ordered by reliability and tested regularly)
const CORS_PROXIES = [
  // AllOrigins - most reliable, supports JSON and raw responses
  {
    url: 'https://api.allorigins.win/raw?url=',
    name: 'AllOrigins Raw',
    timeout: 8000,
    priority: 1
  },
  // Alternative AllOrigins endpoint with JSON parsing
  {
    url: 'https://api.allorigins.win/get?url=',
    name: 'AllOrigins JSON',
    timeout: 8000,
    priority: 1,
    parseResponse: (data) => {
      try {
        const parsed = JSON.parse(data);
        return parsed.contents || data;
      } catch {
        return data;
      }
    }
  },
  // Corsproxy.io - fast and reliable
  {
    url: 'https://corsproxy.io/?',
    name: 'CorsProxy.io',
    timeout: 6000,
    priority: 2
  },
  // CodeTabs proxy - good backup
  {
    url: 'https://api.codetabs.com/v1/proxy?quest=',
    name: 'CodeTabs',
    timeout: 5000,
    priority: 3
  },
  // ThingProxy - reliable backup
  {
    url: 'https://thingproxy.freeboard.io/fetch/',
    name: 'ThingProxy',
    timeout: 7000,
    priority: 4
  },
  // CORS Anywhere (often rate limited, last resort)
  {
    url: 'https://cors-anywhere.herokuapp.com/',
    name: 'CORS Anywhere',
    timeout: 10000,
    priority: 5
  }
];

// Proxy health status cache (in-memory)
const proxyHealthCache = new Map();
const HEALTH_CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

/**
 * Test proxy health with a simple request
 */
async function testProxyHealth(proxy) {
  const cacheKey = proxy.name;
  const cached = proxyHealthCache.get(cacheKey);

  // Return cached result if still valid
  if (cached && (Date.now() - cached.timestamp) < HEALTH_CACHE_DURATION) {
    return cached.healthy;
  }

  try {
    // Test with a simple, fast endpoint
    const testUrl = 'https://httpbin.org/json';
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 3000);

    const response = await fetch(`${proxy.url}${encodeURIComponent(testUrl)}`, {
      signal: controller.signal,
      headers: { 'Accept': 'application/json' }
    });

    clearTimeout(timeoutId);

    const healthy = response.ok;
    proxyHealthCache.set(cacheKey, { healthy, timestamp: Date.now() });
    return healthy;

  } catch (error) {
    proxyHealthCache.set(cacheKey, { healthy: false, timestamp: Date.now() });
    return false;
  }
}

/**
 * Get healthy proxies sorted by priority
 */
async function getHealthyProxies() {
  const healthChecks = await Promise.allSettled(
    CORS_PROXIES.map(async (proxy) => ({
      proxy,
      healthy: await testProxyHealth(proxy)
    }))
  );

  return healthChecks
    .filter(result => result.status === 'fulfilled' && result.value.healthy)
    .map(result => result.value.proxy)
    .sort((a, b) => a.priority - b.priority);
}

// YouTube Data API v3 configuration
const YOUTUBE_API_CONFIG = {
  key: import.meta.env.VITE_YOUTUBE_API_KEY, // You'll need to add this to your .env
  baseUrl: 'https://www.googleapis.com/youtube/v3'
};

// Example video IDs that should always use backend cache
const EXAMPLE_VIDEO_IDS = ['TT81fe2IobI', 'arj7oStGLkU', 'Gv2fzC96Z40', 'UF8uR6Z6KLc', 'ZrN4bKKMlLU', 'KpVPST_P4W8', 'pqWUuYTcG-o'];

/**
 * Check if a video is an example video that should use backend cache
 */
function isExampleVideo(videoId) {
  return EXAMPLE_VIDEO_IDS.includes(videoId);
}

/**
 * Extract video ID from YouTube URL
 */
function extractVideoId(url) {
  const regex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/;
  const match = url.match(regex);
  return match ? match[1] : null;
}

/**
 * Format time in MM:SS format
 */
function formatTime(seconds) {
  if (typeof seconds !== 'number' || isNaN(seconds)) {
    seconds = 0;
  }

  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  if (hours > 0) {
    return [
      hours.toString().padStart(2, '0'),
      minutes.toString().padStart(2, '0'),
      secs.toString().padStart(2, '0')
    ].join(':');
  } else {
    return [
      minutes.toString().padStart(2, '0'),
      secs.toString().padStart(2, '0')
    ].join(':');
  }
}

/**
 * Parse YouTube transcript XML/JSON response
 */
function parseTranscriptData(data, videoId, language = 'en') {
  try {
    let transcriptItems = [];

    // Handle different response formats
    if (typeof data === 'string') {
      // Try to parse as XML first (YouTube transcript format)
      if (data.includes('<transcript>') || data.includes('<text')) {
        transcriptItems = parseXMLTranscript(data);
      } else {
        // Try to parse as JSON
        try {
          const jsonData = JSON.parse(data);
          transcriptItems = parseJSONTranscript(jsonData);
        } catch (e) {
          console.error('Failed to parse transcript data:', e);
          return null;
        }
      }
    } else if (Array.isArray(data)) {
      transcriptItems = data;
    } else if (data && data.transcript) {
      transcriptItems = data.transcript;
    }

    // Format the transcript items
    const formattedItems = transcriptItems.map((item, index) => ({
      start: parseFloat(item.start || item.offset || 0),
      duration: parseFloat(item.duration || item.dur || 3),
      text: (item.text || item.content || '').trim(),
      timestamp: formatTime(parseFloat(item.start || item.offset || 0))
    })).filter(item => item.text);

    return {
      videoId,
      language,
      transcript: formattedItems,
      isAutoGenerated: true, // Assume auto-generated for client-side fetches
      source: 'client-side',
      fetchedAt: new Date().toISOString()
    };
  } catch (error) {
    console.error('Error parsing transcript data:', error);
    return null;
  }
}

/**
 * Parse XML transcript format
 */
function parseXMLTranscript(xmlString) {
  const parser = new DOMParser();
  const xmlDoc = parser.parseFromString(xmlString, 'text/xml');
  const textElements = xmlDoc.querySelectorAll('text');
  
  return Array.from(textElements).map(element => ({
    start: parseFloat(element.getAttribute('start') || 0),
    duration: parseFloat(element.getAttribute('dur') || 3),
    text: element.textContent.trim()
  }));
}

/**
 * Parse JSON transcript format
 */
function parseJSONTranscript(jsonData) {
  if (Array.isArray(jsonData)) {
    return jsonData;
  }
  
  if (jsonData.events) {
    // YouTube API format
    return jsonData.events
      .filter(event => event.segs)
      .flatMap(event => 
        event.segs.map(seg => ({
          start: event.tStartMs / 1000,
          duration: (seg.utf8 || '').length * 0.1, // Estimate duration
          text: seg.utf8 || ''
        }))
      );
  }
  
  return [];
}

/**
 * Method 1: Enhanced CORS proxy fetch with health checking
 */
async function fetchWithCORSProxy(videoId, language = 'en') {
  console.log(`Attempting CORS proxy fetch for ${videoId} in ${language}`);

  // Get healthy proxies first
  const healthyProxies = await getHealthyProxies();

  if (healthyProxies.length === 0) {
    console.log('No healthy CORS proxies available, using all proxies as fallback');
    // If no healthy proxies, try all proxies anyway
    healthyProxies.push(...CORS_PROXIES);
  } else {
    console.log(`Found ${healthyProxies.length} healthy proxies`);
  }

  // YouTube transcript URLs (these are the actual URLs YouTube uses)
  const transcriptUrls = [
    `https://www.youtube.com/api/timedtext?v=${videoId}&lang=${language}&fmt=json3`,
    `https://www.youtube.com/api/timedtext?v=${videoId}&lang=${language}&fmt=srv1`,
    `https://www.youtube.com/api/timedtext?v=${videoId}&lang=${language}`,
    `https://www.youtube.com/api/timedtext?v=${videoId}&lang=${language}&fmt=srv2`,
    `https://www.youtube.com/api/timedtext?v=${videoId}&lang=${language}&fmt=srv3`
  ];

  // Try healthy proxies first, then fallback to all proxies
  for (const proxy of healthyProxies) {
    for (const transcriptUrl of transcriptUrls) {
      try {
        console.log(`Trying proxy: ${proxy.name} with format: ${transcriptUrl.split('&fmt=')[1] || 'default'}`);

        // Create AbortController for timeout
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), proxy.timeout);

        const response = await fetch(`${proxy.url}${encodeURIComponent(transcriptUrl)}`, {
          method: 'GET',
          headers: {
            'Accept': 'application/json, text/plain, */*',
            'User-Agent': 'Mozilla/5.0 (compatible; TranscriptFetcher/1.0)',
            'Cache-Control': 'no-cache'
          },
          signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (response.ok) {
          let data = await response.text();

          // Apply proxy-specific response parsing if needed
          if (proxy.parseResponse) {
            data = proxy.parseResponse(data);
          }

          if (data && data.length > 100) { // Basic validation
            console.log(`✅ Successfully fetched transcript via ${proxy.name} (${data.length} chars)`);
            const parsed = parseTranscriptData(data, videoId, language);
            if (parsed && parsed.transcript && parsed.transcript.length > 0) {
              return {
                ...parsed,
                source: 'cors-proxy',
                proxyUsed: proxy.name
              };
            }
          } else {
            console.log(`⚠️ ${proxy.name} returned empty/small response: ${data.length} chars`);
          }
        } else {
          console.log(`❌ ${proxy.name} returned status: ${response.status}`);
        }
      } catch (error) {
        if (error.name === 'AbortError') {
          console.log(`⏰ ${proxy.name} timed out after ${proxy.timeout}ms`);
        } else {
          console.log(`❌ ${proxy.name} failed: ${error.message}`);
        }
        continue;
      }
    }
  }

  console.log('❌ All CORS proxy attempts failed');
  return null;
}

/**
 * Method 2: YouTube Data API v3 (requires API key)
 */
async function fetchWithYouTubeAPI(videoId, language = 'en') {
  if (!YOUTUBE_API_CONFIG.key) {
    console.log('YouTube API key not configured, skipping API method');
    return null;
  }

  try {
    console.log(`Attempting YouTube Data API fetch for ${videoId} in ${language}`);

    // First, get the list of available captions
    const captionsResponse = await fetch(
      `${YOUTUBE_API_CONFIG.baseUrl}/captions?part=snippet&videoId=${videoId}&key=${YOUTUBE_API_CONFIG.key}`
    );

    if (!captionsResponse.ok) {
      if (captionsResponse.status === 403) {
        console.log('YouTube API quota exceeded or invalid key');
      } else if (captionsResponse.status === 404) {
        console.log('Video not found or captions not available');
      }
      throw new Error(`YouTube API error: ${captionsResponse.status}`);
    }

    const captionsData = await captionsResponse.json();

    if (!captionsData.items || captionsData.items.length === 0) {
      console.log('No captions available via YouTube API');
      return null;
    }

    console.log(`Found ${captionsData.items.length} caption tracks:`,
      captionsData.items.map(item => `${item.snippet.language} (${item.snippet.trackKind})`));

    // Find the best caption track (prefer requested language, then English, then any)
    // Prioritize manual captions over auto-generated
    let captionTrack = captionsData.items.find(item =>
      item.snippet.language === language && item.snippet.trackKind !== 'asr'
    );

    if (!captionTrack) {
      captionTrack = captionsData.items.find(item =>
        item.snippet.language === language
      );
    }

    if (!captionTrack) {
      captionTrack = captionsData.items.find(item =>
        item.snippet.language === 'en' && item.snippet.trackKind !== 'asr'
      );
    }

    if (!captionTrack) {
      captionTrack = captionsData.items.find(item =>
        item.snippet.language === 'en'
      );
    }

    if (!captionTrack) {
      captionTrack = captionsData.items[0];
    }

    console.log(`Selected caption track: ${captionTrack.snippet.language} (${captionTrack.snippet.trackKind})`);

    // Try different formats for downloading captions
    const formats = ['srv1', 'srv2', 'srv3', 'ttml', 'vtt'];

    for (const format of formats) {
      try {
        const downloadUrl = `${YOUTUBE_API_CONFIG.baseUrl}/captions/${captionTrack.id}?key=${YOUTUBE_API_CONFIG.key}&tfmt=${format}`;
        console.log(`Trying to download captions in ${format} format`);

        const downloadResponse = await fetch(downloadUrl, {
          headers: {
            'Accept': 'application/json, text/plain, */*'
          }
        });

        if (downloadResponse.ok) {
          const transcriptData = await downloadResponse.text();
          console.log(`Successfully fetched transcript via YouTube API (${format} format)`);

          const parsed = parseTranscriptData(transcriptData, videoId, captionTrack.snippet.language);
          if (parsed && parsed.transcript && parsed.transcript.length > 0) {
            return {
              ...parsed,
              isAutoGenerated: captionTrack.snippet.trackKind === 'asr',
              source: 'youtube-api'
            };
          }
        }
      } catch (formatError) {
        console.log(`Failed to download ${format} format: ${formatError.message}`);
        continue;
      }
    }

    console.log('All caption download formats failed');

  } catch (error) {
    console.error(`YouTube API fetch failed: ${error.message}`);
  }

  return null;
}

/**
 * Method 3: Backend API fallback (existing implementation)
 */
async function fetchWithBackendAPI(videoId, language = 'en') {
  try {
    console.log(`Falling back to backend API for ${videoId}`);

    const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000/api';

    // For user videos, add a parameter to prevent caching
    const noCacheParam = !isExampleVideo(videoId) ? '&no_cache=true' : '';
    const response = await fetch(`${API_URL}/transcript/${videoId}?lang=${language}${noCacheParam}`);

    if (!response.ok) {
      throw new Error(`Backend API error: ${response.status}`);
    }

    const data = await response.json();

    if (data && data.transcript && Array.isArray(data.transcript)) {
      console.log('Successfully fetched transcript via backend API');
      return {
        ...data,
        source: 'backend-api'
      };
    }

    throw new Error('Invalid transcript data format from backend');
  } catch (error) {
    console.error(`Backend API fetch failed: ${error.message}`);
    throw error;
  }
}

/**
 * Main transcript fetching function with fallback chain
 */
export async function getTranscript(videoId, language = 'en', options = {}) {
  const {
    skipClientSide = false,
    skipBackend = false,
    onProgress = null,
    retryCount = 1,
    preferredMethods = ['cors-proxy', 'youtube-api', 'backend-api']
  } = options;

  // Validate video ID
  if (!videoId || typeof videoId !== 'string') {
    throw new Error('Invalid video ID provided');
  }

  // Example videos should ALWAYS use backend cache for consistency and speed
  if (isExampleVideo(videoId)) {
    console.log(`Example video detected (${videoId}), using backend cache directly`);
    try {
      const backendResult = await fetchWithBackendAPI(videoId, language);
      return backendResult;
    } catch (error) {
      throw new Error(`Failed to fetch example video transcript: ${error.message}`);
    }
  }

  console.log(`Starting transcript fetch for ${videoId} in ${language} (retry count: ${retryCount})`);

  // Progress callback helper
  const reportProgress = (step, status, error = null, details = null) => {
    if (onProgress) {
      onProgress({
        step,
        status,
        error,
        details,
        videoId,
        language,
        timestamp: new Date().toISOString()
      });
    }
  };

  const errors = [];

  // Define available methods
  const methods = {
    'cors-proxy': {
      name: 'CORS Proxy',
      fn: fetchWithCORSProxy,
      clientSide: true
    },
    'youtube-api': {
      name: 'YouTube Data API',
      fn: fetchWithYouTubeAPI,
      clientSide: true
    },
    'backend-api': {
      name: 'Backend API',
      fn: fetchWithBackendAPI,
      clientSide: false
    }
  };

  // Filter methods based on options and preferences
  const methodsToTry = preferredMethods.filter(methodName => {
    const method = methods[methodName];
    if (!method) return false;
    if (method.clientSide && skipClientSide) return false;
    if (!method.clientSide && skipBackend) return false;
    return true;
  });

  if (methodsToTry.length === 0) {
    throw new Error('No transcript fetching methods available with current options');
  }

  reportProgress('start', 'attempting', null, {
    methodsToTry: methodsToTry.map(m => methods[m].name),
    retryCount
  });

  // Try each method in sequence
  for (const methodName of methodsToTry) {
    const method = methods[methodName];

    for (let attempt = 1; attempt <= retryCount; attempt++) {
      try {
        const attemptSuffix = retryCount > 1 ? ` (attempt ${attempt}/${retryCount})` : '';
        reportProgress(methodName, 'attempting', null, { attempt, maxAttempts: retryCount });
        console.log(`Trying ${method.name}${attemptSuffix}...`);

        const result = await method.fn(videoId, language);

        if (result && result.transcript && result.transcript.length > 0) {
          reportProgress(methodName, 'success', null, {
            transcriptLength: result.transcript.length,
            source: result.source || methodName
          });
          console.log(`✅ Successfully fetched transcript via ${method.name}`);
          return result;
        } else {
          console.log(`❌ ${method.name} returned empty or invalid transcript`);
          if (attempt === retryCount) {
            reportProgress(methodName, 'failed', 'Empty or invalid transcript returned');
            errors.push(`${method.name}: Empty or invalid transcript returned`);
          }
        }
      } catch (error) {
        console.error(`❌ ${method.name} failed (attempt ${attempt}/${retryCount}):`, error.message);
        if (attempt === retryCount) {
          reportProgress(methodName, 'error', error.message);
          errors.push(`${method.name}: ${error.message}`);
        }

        // Add delay between retries (exponential backoff)
        if (attempt < retryCount) {
          const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000);
          console.log(`Retrying ${method.name} in ${delay}ms...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }
  }

  // All methods failed
  const errorMessage = `All transcript fetching methods failed:\n${errors.join('\n')}`;
  reportProgress('complete', 'error', errorMessage);
  throw new Error(errorMessage);
}

/**
 * Get available languages for a video
 */
export async function getAvailableLanguages(videoId) {
  // Example videos should always use backend for consistency
  if (isExampleVideo(videoId)) {
    console.log(`Example video detected (${videoId}), using backend for language detection`);
    try {
      const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000/api';
      const response = await fetch(`${API_URL}/captions/${videoId}`);

      if (response.ok) {
        const data = await response.json();
        return data.languages || [];
      }
    } catch (error) {
      console.error('Failed to get languages via backend API:', error);
    }
    return [];
  }

  // For non-example videos, try YouTube API first if available
  if (YOUTUBE_API_CONFIG.key) {
    try {
      const response = await fetch(
        `${YOUTUBE_API_CONFIG.baseUrl}/captions?part=snippet&videoId=${videoId}&key=${YOUTUBE_API_CONFIG.key}`
      );

      if (response.ok) {
        const data = await response.json();
        return data.items?.map(item => ({
          code: item.snippet.language,
          name: item.snippet.name,
          isAutoGenerated: item.snippet.trackKind === 'asr'
        })) || [];
      }
    } catch (error) {
      console.error('Failed to get languages via YouTube API:', error);
    }
  }

  // Fallback to backend API
  try {
    const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000/api';
    const response = await fetch(`${API_URL}/captions/${videoId}`);

    if (response.ok) {
      const data = await response.json();
      return data.languages || [];
    }
  } catch (error) {
    console.error('Failed to get languages via backend API:', error);
  }

  return [];
}

// Export utility functions
export { extractVideoId, formatTime, isExampleVideo };
