@tailwind base;
@tailwind components;
@tailwind utilities;

#app {
  min-height: 100vh;
}

/* Hide scrollbar but keep functionality */
.hide-scrollbar {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.hide-scrollbar::-webkit-scrollbar {
  display: none;  /* Chrome, Safari and Opera */
}

/* Gradient border for user videos */
.border-gradient {
  position: relative;
  border: none !important; /* Remove default border */
  border-radius: var(--rounded-box, 1rem) !important; /* Match daisyUI's card border-radius */
  overflow: hidden; /* Ensure content respects the border radius */
}

/* Create gradient border using pseudo-element */
.border-gradient::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 2px dashed transparent;
  border-radius: var(--rounded-box, 1rem);
  background: linear-gradient(135deg, #6419e6, #d926aa, #1fb2a5) border-box;
  -webkit-mask:
    linear-gradient(#fff 0 0) padding-box,
    linear-gradient(#fff 0 0);
  -webkit-mask-composite: destination-out;
  mask-composite: exclude;
  pointer-events: none; /* Allow clicks to pass through to the card */
  z-index: 10;
}

/* Dashed gradient border for "Add your own video" box */
@property --dash-phase {
  syntax: '<length>';
  inherits: false;
  initial-value: 0;
}

.dashed-gradient {
  --dash-size: 10px;
  --dash-phase: 0;
  position: relative;
  border: none !important;
  border-radius: var(--rounded-box, 1rem) !important;
  background-clip: padding-box;
}

.dashed-gradient::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: var(--rounded-box, 1rem);
  padding: 2px; /* Border width */
  background: linear-gradient(135deg, #6419e6, #d926aa, #1fb2a5);
  -webkit-mask:
    linear-gradient(#fff 0 0) content-box,
    linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  mask: repeating-linear-gradient(45deg,
    transparent,
    transparent var(--dash-size),
    #000 var(--dash-size),
    #000 calc(2 * var(--dash-size))
  );
  pointer-events: none;
}

/* Selected state enhancement */
.ring-2.ring-primary {
  box-shadow: 0 0 0 2px theme('colors.primary'), 0 0 10px 2px rgba(100, 25, 230, 0.3);
}

/* No special treatment for transcript tab - let v-show handle visibility */

/* Make sure tab content is properly hidden/shown */
[id^="tab-"] {
  display: none !important;
}

[id="tab-transcript"][style*="display: block"],
[id="tab-summary"][style*="display: block"],
[id="tab-quotes"][style*="display: block"],
[id="tab-descriptions"][style*="display: block"],
[id="tab-social"][style*="display: block"],
[id="tab-chat"][style*="display: block"] {
  display: block !important;
}

/* Ensure active tab buttons have white text */
.tab-button.active-tab {
  color: white !important;
  background-color: #4f46e5 !important; /* Exact blue color */
  font-weight: 600;
}

/* ===== BUTTON FONT SIZE IMPROVEMENTS ===== */
/* Ensure all buttons have minimum 14px font size for better readability */

/* Primary CTA Buttons - 16px+ for main actions */
.btn-primary.btn-lg,
.btn.btn-lg {
  font-size: 16px !important;
  font-weight: 600;
  padding: 0.75rem 1.5rem !important;
  min-height: 3rem !important;
}

/* Standard Action Buttons - 14px for secondary actions */
.btn,
.btn-sm,
.btn-outline,
.btn-primary:not(.btn-lg) {
  font-size: 14px !important;
  font-weight: 500;
  padding: 0.5rem 1rem !important;
  min-height: 2.5rem !important;
}

/* Small Utility Buttons - 14px minimum */
.btn-xs {
  font-size: 14px !important;
  font-weight: 500;
  padding: 0.375rem 0.75rem !important;
  min-height: 2rem !important;
}

/* Tab Buttons - Enhanced readability */
.tab-button {
  font-size: 14px !important;
  font-weight: 500;
  padding: 0.5rem 1rem !important;
  min-height: 2.5rem !important;
}

/* Circle buttons - maintain proportions but ensure touch targets */
.btn-circle {
  min-width: 2.5rem !important;
  min-height: 2.5rem !important;
  font-size: 14px !important;
}

.btn-circle.btn-sm {
  min-width: 2rem !important;
  min-height: 2rem !important;
  font-size: 12px !important;
}

/* Language dropdown and similar controls */
.dropdown label.btn {
  font-size: 14px !important;
  font-weight: 500;
  padding: 0.5rem 1rem !important;
}

/* Ensure proper line height for better text rendering */
.btn, .btn-sm, .btn-lg, .btn-xs {
  line-height: 1.2 !important;
}

/* Special case for icon-text buttons */
.btn svg + span,
.btn span + svg {
  margin-left: 0.375rem;
  margin-right: 0.375rem;
}

/* Dropdown menu items - ensure readability */
.dropdown-content li a {
  font-size: 14px !important;
  font-weight: 500;
  padding: 0.5rem 1rem !important;
  min-height: 2.25rem !important;
}

/* Input field consistency */
.input-lg {
  font-size: 16px !important;
  padding: 0.75rem 1rem !important;
}

/* Ensure button text is never too small */
button, .btn {
  font-size: 14px !important;
  font-weight: 500;
}

/* Override any remaining small text in buttons */
.btn-xs, .btn-sm, .btn, .btn-lg {
  font-size: 14px !important;
}

.btn-lg {
  font-size: 16px !important;
}

/* Accessibility: Ensure minimum touch targets */
.btn, button {
  min-height: 44px;
  min-width: 44px;
}

.btn-sm {
  min-height: 40px;
  min-width: 40px;
}

.btn-xs {
  min-height: 36px;
  min-width: 36px;
}
