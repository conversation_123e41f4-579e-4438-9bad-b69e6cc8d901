<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Video Comparison Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .video-container {
            position: relative;
            padding-bottom: 56.25%;
            height: 0;
            overflow: hidden;
            margin: 20px 0;
            border: 2px solid #ccc;
        }
        .video-container iframe {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }
        .comparison {
            display: flex;
            gap: 20px;
        }
        .video-section {
            flex: 1;
        }
        .controls {
            margin: 10px 0;
        }
        button {
            margin: 5px;
            padding: 8px 12px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        button:hover {
            background: #0056b3;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 11px;
        }
        h3 {
            margin-top: 0;
            color: #333;
        }
        .working {
            border-color: #28a745;
        }
        .problematic {
            border-color: #dc3545;
        }
    </style>
</head>
<body>
    <h1>Video Player Comparison Test</h1>
    <p>Comparing a <strong>working video</strong> vs the <strong>problematic video</strong></p>
    
    <div class="comparison">
        <!-- Working Video -->
        <div class="video-section">
            <h3>✅ Working Video (TT81fe2IobI)</h3>
            <div class="video-container working">
                <iframe 
                    id="working-player"
                    src="https://www.youtube.com/embed/TT81fe2IobI?enablejsapi=1&autoplay=0&origin=http://localhost:5175&rel=0&modestbranding=1&fs=1&playsinline=1&widgetid=1"
                    frameborder="0"
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                    allowfullscreen>
                </iframe>
            </div>
            <div class="controls">
                <button onclick="initPlayer('working')">Init</button>
                <button onclick="playVideo('working')">Play</button>
                <button onclick="seekTo('working', 30)">Seek 30s</button>
                <button onclick="seekTo('working', 60)">Seek 60s</button>
                <button onclick="getState('working')">Get State</button>
            </div>
            <div class="log" id="working-log"></div>
        </div>

        <!-- Problematic Video -->
        <div class="video-section">
            <h3>❌ Problematic Video (arj7oStGLkU)</h3>
            <div class="video-container problematic">
                <iframe 
                    id="problematic-player"
                    src="https://www.youtube.com/embed/arj7oStGLkU?enablejsapi=1&autoplay=0&origin=http://localhost:5175&rel=0&modestbranding=1&fs=1&playsinline=1&widgetid=1"
                    frameborder="0"
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                    allowfullscreen>
                </iframe>
            </div>
            <div class="controls">
                <button onclick="initPlayer('problematic')">Init</button>
                <button onclick="playVideo('problematic')">Play</button>
                <button onclick="seekTo('problematic', 30)">Seek 30s</button>
                <button onclick="seekTo('problematic', 60)">Seek 60s</button>
                <button onclick="getState('problematic')">Get State</button>
            </div>
            <div class="log" id="problematic-log"></div>
        </div>
    </div>

    <div style="margin-top: 30px;">
        <button onclick="clearAllLogs()">Clear All Logs</button>
        <button onclick="testBothVideos()">Test Both Videos</button>
    </div>

    <script>
        const players = {
            working: document.getElementById('working-player'),
            problematic: document.getElementById('problematic-player')
        };
        
        function logMessage(type, message) {
            const timestamp = new Date().toLocaleTimeString();
            const log = document.getElementById(`${type}-log`);
            log.innerHTML += `[${timestamp}] ${message}\n`;
            log.scrollTop = log.scrollHeight;
            console.log(`[${type}] ${message}`);
        }
        
        function clearAllLogs() {
            document.getElementById('working-log').innerHTML = '';
            document.getElementById('problematic-log').innerHTML = '';
        }
        
        // Listen for messages from both YouTube players
        window.addEventListener('message', function(event) {
            if (!event.origin.includes('youtube.com')) return;
            
            // Determine which player sent the message
            let playerType = 'unknown';
            if (event.source === players.working.contentWindow) {
                playerType = 'working';
            } else if (event.source === players.problematic.contentWindow) {
                playerType = 'problematic';
            }
            
            try {
                const data = typeof event.data === 'string' ? JSON.parse(event.data) : event.data;
                logMessage(playerType, `📨 ${JSON.stringify(data)}`);
            } catch (error) {
                logMessage(playerType, `📨 (non-JSON): ${event.data}`);
            }
        });
        
        function sendCommand(type, command) {
            const player = players[type];
            if (!player || !player.contentWindow) {
                logMessage(type, '❌ Player not ready');
                return;
            }
            
            const message = JSON.stringify(command);
            player.contentWindow.postMessage(message, '*');
            logMessage(type, `📤 ${message}`);
        }
        
        function initPlayer(type) {
            logMessage(type, '🔧 Initializing...');
            sendCommand(type, {
                event: 'listening',
                id: 'widget',
                channel: 'widget'
            });
        }
        
        function playVideo(type) {
            sendCommand(type, {
                event: 'command',
                func: 'playVideo',
                args: []
            });
        }
        
        function seekTo(type, seconds) {
            sendCommand(type, {
                event: 'command',
                func: 'seekTo',
                args: [seconds, true]
            });
        }
        
        function getState(type) {
            sendCommand(type, {
                event: 'command',
                func: 'getPlayerState',
                args: []
            });
            
            sendCommand(type, {
                event: 'command',
                func: 'getCurrentTime',
                args: []
            });
        }
        
        function testBothVideos() {
            logMessage('working', '🧪 Starting automated test...');
            logMessage('problematic', '🧪 Starting automated test...');
            
            // Initialize both players
            setTimeout(() => {
                initPlayer('working');
                initPlayer('problematic');
            }, 1000);
            
            // Test seeking after initialization
            setTimeout(() => {
                logMessage('working', '🧪 Testing seek to 30s...');
                logMessage('problematic', '🧪 Testing seek to 30s...');
                seekTo('working', 30);
                seekTo('problematic', 30);
            }, 3000);
            
            // Test play after seeking
            setTimeout(() => {
                logMessage('working', '🧪 Testing play...');
                logMessage('problematic', '🧪 Testing play...');
                playVideo('working');
                playVideo('problematic');
            }, 5000);
            
            // Get final state
            setTimeout(() => {
                logMessage('working', '🧪 Getting final state...');
                logMessage('problematic', '🧪 Getting final state...');
                getState('working');
                getState('problematic');
            }, 7000);
        }
        
        // Auto-initialize after page load
        setTimeout(() => {
            logMessage('working', '✅ Page loaded, iframe ready');
            logMessage('problematic', '❌ Page loaded, iframe ready');
        }, 2000);
    </script>
</body>
</html>
