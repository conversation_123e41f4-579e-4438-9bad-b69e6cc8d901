services:
  # Backend API service
  - type: web
    name: youtube-transcribe-api
    env: node
    region: oregon
    plan: free
    buildCommand: ./build-node.sh
    startCommand: cd backend && npm start
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 10000
      - key: YOUTUBE_API_KEY
        sync: false
      - key: GOOGLE_API_KEY
        sync: false
      - key: RENDER_ENV
        value: node
      - key: RENDER_RUNTIME
        value: node

  # Frontend static site
  - type: web
    name: youtube-transcribe-frontend
    env: static
    region: oregon
    buildCommand: cd frontend/youtube-transcribe && npm install && npm run build
    staticPublishPath: ./frontend/youtube-transcribe/dist
    envVars:
      - key: VITE_API_URL
        value: https://youtube-transcribe-api.onrender.com/api
    headers:
      - path: /*
        name: Cache-Control
        value: no-cache
