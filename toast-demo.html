<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Toast Notification Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .demo-container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .button-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.2s;
        }
        button:hover { background-color: #0056b3; }
        button.success { background-color: #28a745; }
        button.success:hover { background-color: #1e7e34; }
        button.error { background-color: #dc3545; }
        button.error:hover { background-color: #c82333; }
        button.warning { background-color: #ffc107; color: #212529; }
        button.warning:hover { background-color: #e0a800; }
        button.copy { background-color: #17a2b8; }
        button.copy:hover { background-color: #138496; }
        .code-block {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin: 15px 0;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>🍞 Toast Notification System Demo</h1>
    
    <div class="demo-container">
        <h2>Interactive Demo</h2>
        <p>Click the buttons below to see different types of toast notifications in action:</p>
        
        <div class="button-grid">
            <button onclick="showSuccessToast()">✅ Success Toast</button>
            <button class="error" onclick="showErrorToast()">❌ Error Toast</button>
            <button class="warning" onclick="showWarningToast()">⚠️ Warning Toast</button>
            <button onclick="showInfoToast()">ℹ️ Info Toast</button>
            <button class="copy" onclick="showCopyToast()">📋 Copy Success</button>
            <button onclick="showDownloadToast()">⬇️ Download Toast</button>
            <button onclick="testCopyFunction()">📝 Test Copy Function</button>
            <button onclick="showMultipleToasts()">🎯 Multiple Toasts</button>
        </div>
    </div>

    <div class="demo-container">
        <h2>Usage Examples</h2>
        
        <h3>Basic Usage:</h3>
        <div class="code-block">
// Import the composable
import { useToast } from './composables/useToast.js';

// In your component setup
const toast = useToast();

// Show different types of toasts
toast.showSuccess('Operation completed successfully!');
toast.showError('Something went wrong');
toast.showWarning('Please check your input');
toast.showInfo('Here\'s some helpful information');
        </div>

        <h3>Copy to Clipboard:</h3>
        <div class="code-block">
// Easy copy with automatic toast feedback
await toast.copyToClipboard('Text to copy', 'Content Type');

// Manual copy handling
async function copyTranscript() {
  const text = transcript.map(item => item.text).join('\n');
  await toast.copyToClipboard(text, 'Transcript');
}
        </div>

        <h3>Download with Toast:</h3>
        <div class="code-block">
// Show download success
toast.showDownloadSuccess('transcript.txt');

// Download file with toast
toast.downloadAsFile(content, 'transcript.txt', 'text/plain');
        </div>
    </div>

    <div class="demo-container">
        <h2>Features</h2>
        <ul>
            <li>✨ <strong>Auto-dismiss</strong>: Toasts automatically disappear after 2-5 seconds</li>
            <li>🎨 <strong>Beautiful Design</strong>: Modern, clean appearance with smooth animations</li>
            <li>📱 <strong>Responsive</strong>: Works perfectly on mobile and desktop</li>
            <li>🎯 <strong>Multiple Types</strong>: Success, Error, Warning, Info, Copy notifications</li>
            <li>🔧 <strong>Easy Integration</strong>: Simple composable for Vue.js components</li>
            <li>⚡ <strong>No Click Required</strong>: No annoying "OK" buttons to dismiss</li>
            <li>📋 <strong>Copy Helper</strong>: Built-in clipboard functionality with fallbacks</li>
            <li>⬇️ <strong>Download Helper</strong>: File download with automatic notifications</li>
        </ul>
    </div>

    <!-- Toast notification system (simulated for demo) -->
    <div id="toast-container" style="position: fixed; top: 20px; right: 20px; z-index: 9999;"></div>

    <script>
        // Simulated toast system for demo purposes
        let toastId = 0;

        function createToast(type, title, message, duration = 3000) {
            const container = document.getElementById('toast-container');
            const toast = document.createElement('div');
            toast.id = `toast-${++toastId}`;
            
            const colors = {
                success: 'background: rgba(34, 197, 94, 0.95); color: white; border-left: 4px solid rgb(34, 197, 94);',
                error: 'background: rgba(239, 68, 68, 0.95); color: white; border-left: 4px solid rgb(239, 68, 68);',
                warning: 'background: rgba(245, 158, 11, 0.95); color: white; border-left: 4px solid rgb(245, 158, 11);',
                info: 'background: rgba(59, 130, 246, 0.95); color: white; border-left: 4px solid rgb(59, 130, 246);',
                copy: 'background: rgba(16, 185, 129, 0.95); color: white; border-left: 4px solid rgb(16, 185, 129);'
            };

            toast.style.cssText = `
                ${colors[type]}
                margin-bottom: 12px;
                min-width: 300px;
                max-width: 400px;
                border-radius: 8px;
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
                backdrop-filter: blur(10px);
                position: relative;
                overflow: hidden;
                animation: slideIn 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
                cursor: pointer;
            `;

            toast.innerHTML = `
                <div style="display: flex; align-items: flex-start; padding: 16px; gap: 12px;">
                    <div style="flex-shrink: 0; width: 20px; height: 20px; margin-top: 2px;">
                        ${getIcon(type)}
                    </div>
                    <div style="flex: 1; min-width: 0;">
                        ${title ? `<div style="font-weight: 600; font-size: 14px; margin-bottom: 4px;">${title}</div>` : ''}
                        <div style="font-size: 14px; line-height: 1.4;">${message}</div>
                    </div>
                </div>
                <div style="position: absolute; bottom: 0; left: 0; height: 3px; background: currentColor; opacity: 0.3; animation: progress ${duration}ms linear forwards;"></div>
            `;

            toast.onclick = () => removeToast(toast.id);
            container.appendChild(toast);

            setTimeout(() => removeToast(toast.id), duration);
        }

        function getIcon(type) {
            const icons = {
                success: '<svg style="width: 20px; height: 20px;" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>',
                error: '<svg style="width: 20px; height: 20px;" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>',
                warning: '<svg style="width: 20px; height: 20px;" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path></svg>',
                info: '<svg style="width: 20px; height: 20px;" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>',
                copy: '<svg style="width: 20px; height: 20px;" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path></svg>'
            };
            return icons[type] || icons.info;
        }

        function removeToast(id) {
            const toast = document.getElementById(id);
            if (toast) {
                toast.style.animation = 'slideOut 0.3s cubic-bezier(0.55, 0.085, 0.68, 0.53)';
                setTimeout(() => toast.remove(), 300);
            }
        }

        // Demo functions
        function showSuccessToast() {
            createToast('success', 'Success!', 'Transcript copied to clipboard successfully', 3000);
        }

        function showErrorToast() {
            createToast('error', 'Error', 'Failed to fetch transcript. Please try again.', 5000);
        }

        function showWarningToast() {
            createToast('warning', 'Warning', 'No transcript available to copy', 4000);
        }

        function showInfoToast() {
            createToast('info', null, 'Loading transcript from YouTube...', 3000);
        }

        function showCopyToast() {
            createToast('copy', 'Copied!', 'Summary copied to clipboard', 2500);
        }

        function showDownloadToast() {
            createToast('success', 'Download Started', 'Downloading youtube-transcript-abc123.txt', 3000);
        }

        function testCopyFunction() {
            const testText = 'This is a test transcript that was copied to your clipboard!';
            navigator.clipboard.writeText(testText).then(() => {
                createToast('copy', 'Copied!', 'Test transcript copied to clipboard', 2500);
            }).catch(() => {
                createToast('error', 'Copy Failed', 'Failed to copy to clipboard', 4000);
            });
        }

        function showMultipleToasts() {
            createToast('info', null, 'Starting transcript fetch...', 3000);
            setTimeout(() => createToast('success', 'Success!', 'Transcript fetched successfully', 3000), 500);
            setTimeout(() => createToast('copy', 'Copied!', 'Transcript copied to clipboard', 2500), 1000);
        }

        // Add CSS animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { opacity: 0; transform: translateX(100%) scale(0.8); }
                to { opacity: 1; transform: translateX(0) scale(1); }
            }
            @keyframes slideOut {
                from { opacity: 1; transform: translateX(0) scale(1); }
                to { opacity: 0; transform: translateX(100%) scale(0.8); }
            }
            @keyframes progress {
                from { width: 100%; }
                to { width: 0%; }
            }
        `;
        document.head.appendChild(style);

        // Show welcome toast
        setTimeout(() => {
            createToast('info', 'Welcome!', 'Click the buttons above to test different toast types', 4000);
        }, 500);
    </script>
</body>
</html>
