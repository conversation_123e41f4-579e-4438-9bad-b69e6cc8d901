<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Test API</title>
</head>
<body>
  <h1>Test API</h1>
  <button id="fetchExamples">Fetch Examples</button>
  <pre id="result"></pre>

  <script>
    document.getElementById('fetchExamples').addEventListener('click', async () => {
      const resultElement = document.getElementById('result');
      resultElement.textContent = 'Loading...';
      
      try {
        const response = await fetch('http://localhost:3000/api/examples');
        const data = await response.json();
        resultElement.textContent = JSON.stringify(data, null, 2);
      } catch (error) {
        resultElement.textContent = `Error: ${error.message}`;
      }
    });
  </script>
</body>
</html>
