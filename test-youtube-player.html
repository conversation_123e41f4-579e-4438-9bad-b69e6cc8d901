<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YouTube Player Communication Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .video-container {
            position: relative;
            padding-bottom: 56.25%;
            height: 0;
            overflow: hidden;
            margin: 20px 0;
        }
        .video-container iframe {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }
        .controls {
            margin: 20px 0;
        }
        button {
            margin: 5px;
            padding: 10px 15px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>YouTube Player Communication Test</h1>
    <p>Testing video: <strong>Inside the mind of a master procrastinator (arj7oStGLkU)</strong></p>
    
    <div class="video-container">
        <iframe 
            id="youtube-player"
            src="https://www.youtube.com/embed/arj7oStGLkU?enablejsapi=1&autoplay=0&origin=http://localhost:5175&rel=0&modestbranding=1&fs=1&playsinline=1&widgetid=1"
            frameborder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowfullscreen>
        </iframe>
    </div>
    
    <div class="controls">
        <button onclick="initPlayer()">Initialize Player</button>
        <button onclick="playVideo()">Play Video</button>
        <button onclick="pauseVideo()">Pause Video</button>
        <button onclick="seekTo(60)">Seek to 1:00</button>
        <button onclick="seekTo(120)">Seek to 2:00</button>
        <button onclick="getCurrentTime()">Get Current Time</button>
        <button onclick="getPlayerState()">Get Player State</button>
        <button onclick="clearLog()">Clear Log</button>
    </div>
    
    <div class="log" id="log"></div>

    <script>
        const iframe = document.getElementById('youtube-player');
        const log = document.getElementById('log');
        
        function logMessage(message) {
            const timestamp = new Date().toLocaleTimeString();
            log.innerHTML += `[${timestamp}] ${message}\n`;
            log.scrollTop = log.scrollHeight;
            console.log(message);
        }
        
        function clearLog() {
            log.innerHTML = '';
        }
        
        // Listen for messages from YouTube
        window.addEventListener('message', function(event) {
            if (!event.origin.includes('youtube.com')) return;
            
            try {
                const data = typeof event.data === 'string' ? JSON.parse(event.data) : event.data;
                logMessage(`📨 Received: ${JSON.stringify(data)}`);
            } catch (error) {
                logMessage(`📨 Received (non-JSON): ${event.data}`);
            }
        });
        
        function sendCommand(command) {
            if (!iframe.contentWindow) {
                logMessage('❌ Error: iframe not ready');
                return;
            }
            
            const message = JSON.stringify(command);
            iframe.contentWindow.postMessage(message, '*');
            logMessage(`📤 Sent: ${message}`);
        }
        
        function initPlayer() {
            logMessage('🔧 Initializing player...');
            sendCommand({
                event: 'listening',
                id: 'widget',
                channel: 'widget'
            });
            
            setTimeout(() => {
                sendCommand({ event: 'listening' });
            }, 100);
        }
        
        function playVideo() {
            sendCommand({
                event: 'command',
                func: 'playVideo',
                args: []
            });
        }
        
        function pauseVideo() {
            sendCommand({
                event: 'command',
                func: 'pauseVideo',
                args: []
            });
        }
        
        function seekTo(seconds) {
            sendCommand({
                event: 'command',
                func: 'seekTo',
                args: [seconds, true]
            });
        }
        
        function getCurrentTime() {
            sendCommand({
                event: 'command',
                func: 'getCurrentTime',
                args: []
            });
        }
        
        function getPlayerState() {
            sendCommand({
                event: 'command',
                func: 'getPlayerState',
                args: []
            });
        }
        
        // Auto-initialize after a delay
        setTimeout(() => {
            logMessage('🚀 Auto-initializing player...');
            initPlayer();
        }, 2000);
        
        logMessage('✅ Test page loaded. Waiting for iframe to load...');
    </script>
</body>
</html>
