{"name": "youtube-transcript-generator", "version": "1.0.0", "description": "A web application that allows users to view and download subtitles from YouTube videos", "private": true, "type": "commonjs", "engines": {"node": "18.18.0"}, "main": "backend/server.js", "scripts": {"start": "cd backend && node server.js", "build": "bash ./.render-build.sh", "install:backend": "cd backend && npm install", "install:frontend": "cd frontend/youtube-transcribe && npm install", "install:all": "npm run install:backend && npm run install:frontend", "start:backend": "cd backend && npm start", "start:frontend": "cd frontend/youtube-transcribe && npm run dev", "build:frontend": "cd frontend/youtube-transcribe && npm run build", "dev": "concurrently \"npm run start:backend\" \"npm run start:frontend\""}, "repository": {"type": "git", "url": "git+https://gitlab.com/LaurinWirth/youtubetranscriptgeneratior.git"}, "keywords": ["youtube", "transcript", "subtitles", "vue", "node", "express"], "author": "<PERSON><PERSON>", "license": "MIT", "dependencies": {"dotenv": "^16.5.0", "express": "^5.1.0", "node-fetch": "^3.3.2"}, "devDependencies": {"concurrently": "^8.2.0"}}